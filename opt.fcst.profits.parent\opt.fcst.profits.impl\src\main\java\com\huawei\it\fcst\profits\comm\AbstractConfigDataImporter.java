/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.comm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.constants.Constants;
import com.huawei.it.fcst.profits.common.enums.ModuleEnum;
import com.huawei.it.fcst.profits.common.enums.OptTypeEnum;
import com.huawei.it.fcst.profits.common.enums.RecStsEnum;
import com.huawei.it.fcst.profits.common.poi.ExcelUtilPro;
import com.huawei.it.fcst.profits.common.poi.PoiUtils;
import com.huawei.it.fcst.profits.common.utils.FileProcessUtis;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.utils.UserHandle;
import com.huawei.it.fcst.profits.common.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.common.vo.ExcelVO;
import com.huawei.it.fcst.profits.common.vo.ResultCode;
import com.huawei.it.fcst.profits.common.vo.TableHeaderVo;
import com.huawei.it.fcst.profits.common.vo.UploadInfoVO;
import com.huawei.it.fcst.profits.dao.ILabelConfigDao;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.utils.HeaderUtils;
import com.huawei.it.fcst.profits.vo.CoaConfigDataVO;
import com.huawei.it.fcst.profits.vo.DmFopRecordVO;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.fcst.profits.vo.ObjectConfigDataVO;
import com.huawei.it.fcst.profits.vo.PlanComConfigDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * This class AbstractConfigImporter.java
 *
 * <AUTHOR>
 * @since 2021年2月9日
 */

public abstract class AbstractConfigDataImporter extends AbstractService {

    private static final Logger logger = LoggerFactory.getLogger(AbstractConfigDataImporter.class);

    @Autowired
    protected ExcelUtilPro excelUtilPro;

    @Inject
    protected ILabelConfigDao iLabelConfigDao;

    public CommonResult importData(Attachment attachment, String roleId)
        throws CommonApplicationException {
        // 封装用户以及保存文件的相关信息
        UploadInfoVO uploadInfoVO = setUserUploadInfo(attachment);
        // 设置时间
        Timestamp curTime = TimeUtils.getCurTime();
        uploadInfoVO.setStartTime(curTime);
        // 构建Excel表头信息
        List<ExcelVO> excelList = getHeadList();
        Map<String, Object> paramMap = new HashMap<>();
        readExcel(attachment, excelList, uploadInfoVO, paramMap, roleId);
        if (StringUtils.isNotBlank(uploadInfoVO.getErrorTips())) {
            if (uploadInfoVO.getRowNumber() < 0) {
                uploadInfoVO.setRowNumber(0);
            }
            logErrorRecord(uploadInfoVO, (List) paramMap.get("dataInfo"));
            return CommonResult.failed(ResultCode.CHECK_TIP, uploadInfoVO.getErrorTips());
        }
        if (CollectionUtils.isEmpty((List) paramMap.get("dataInfo"))) {
            // 导入空文件时，由于去掉了表头一行，导致出现负数。在此处修复。
            if (uploadInfoVO.getRowNumber() < 0) {
                uploadInfoVO.setRowNumber(0);
            }
            logErrorRecord(uploadInfoVO, (List) paramMap.get("dataInfo"));
            return CommonResult.failed(ResultCode.CHECK_TIP, CommonConstant.LABEL_CONFIG_EXCEPTION3);
        }
        // 数据校验通过
        if ((paramMap.containsKey("countNum") && (0 == (int) paramMap.get("countNum")))) {
            insertData((List<Object>) paramMap.get("dataInfo"));
            // 获取我的导入所需信息
            Map<String, Object> preparedInfo  = (Map<String, Object>) paramMap.get("preparedInfo");
            creatRecord(uploadInfoVO.getFileName(), uploadInfoVO.getFileSize(), "Save", "",
                uploadInfoVO.getRowNumber(), TimeUtils.getCurPeriod(), uploadInfoVO.getFileKey(), "", OptTypeEnum.IMPORT.getCode(),
                RecStsEnum.SUCCESS.getCode(), getModuleName(String.valueOf(preparedInfo.get("dataType"))));
            return setResultAndNum("导入成功",uploadInfoVO.getRowNumber());
        } else {
            logErrorRecord(uploadInfoVO, (List) paramMap.get("dataInfo"));
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    private CommonResult<Object> setResultAndNum(String message, int num) {
        CommonResult<Object> result = CommonResult.success(message);
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("upsertNum", num);
        result.setEntity(messageMap);
        return result;
    }

    private String getModuleName(String dataType) {
        String moduleName = "";
        switch (dataType) {
            case "COA":
                moduleName = CommonConstant.LABEL_CONFIG_MODULE_NAME1;
                break;
            case "PLAN_COM":
                moduleName = CommonConstant.LABEL_CONFIG_MODULE_NAME3;
                break;
            case "HOLISTIC_VIEW":
                moduleName = CommonConstant.LABEL_CONFIG_MODULE_NAME2;
                break;
            default:
                moduleName = CommonConstant.LABEL_CONFIG_MODULE_NAME4;
                break;
        }
        return moduleName;
    }

    public abstract List<ExcelVO> getHeadList();

    private void insertData(List<Object> voList) {
        Set<String> lv1NameSet = new HashSet();
        if (voList.isEmpty()) {
            return;
        }
        int batchNum = voList.size() / 600;
        if (voList.get(0) instanceof CoaConfigDataVO) {
            doCoaData(batchNum, lv1NameSet, voList);
        } else if (voList.get(0) instanceof PlanComConfigDataVO) {
            doPlanComData(batchNum, lv1NameSet, voList);
        } else if (voList.get(0) instanceof HolisticViewConfigDataVO) {
            doIctData(batchNum, lv1NameSet, voList);
        } else if (voList.get(0) instanceof ObjectConfigDataVO) {
            doObjectData(batchNum, lv1NameSet, voList);
        }
    }

    public void doCoaData(int batchNum, Set<String> lv1NameSet, List<Object> voList) {
        // 删除规则更新，不全部删除，按产业带状态进行删除
        voList.stream().forEach(voInfo -> {
            CoaConfigDataVO dataVo = (CoaConfigDataVO) voInfo;
            lv1NameSet.add(dataVo.getLv1Name());
        });
        List<String> lv1Name = new ArrayList(lv1NameSet);
        iLabelConfigDao.deleteSelectedCoaDataList(lv1Name);
        // 分批次插入
        updateImportCoaData(batchNum, voList);
    }

    public void doIctData(int batchNum, Set<String> lv1NameSet, List<Object> voList) {
        voList.stream().forEach(voInfo -> {
            HolisticViewConfigDataVO dataVo = (HolisticViewConfigDataVO) voInfo;
            lv1NameSet.add(dataVo.getLv1Name());
        });
        List<String> lv1Name = new ArrayList(lv1NameSet);
        iLabelConfigDao.deleteSelectedIctDataList(lv1Name);
        updateImportIctData(batchNum, voList);
    }

    public void doPlanComData(int batchNum, Set<String> lv1NameSet, List<Object> voList) {
        voList.stream().forEach(voInfo -> {
            PlanComConfigDataVO dataVo = (PlanComConfigDataVO) voInfo;
            lv1NameSet.add(dataVo.getLv1Name());
        });
        List<String> lv1Name = new ArrayList(lv1NameSet);
        // 删除规则更新，不全部删除，按产业带状态进行删除 同上
        iLabelConfigDao.deleteSelectedPlanComDataList(lv1Name);
        updateImportPlanComData(batchNum, voList);
    }

    public void doObjectData(int batchNum, Set<String> lv1NameSet, List<Object> voList) {
        voList.stream().forEach(voInfo -> {
            ObjectConfigDataVO dataVo = (ObjectConfigDataVO) voInfo;
            lv1NameSet.add(dataVo.getLv1Name());
        });
        List<String> lv1Name = new ArrayList(lv1NameSet);
        iLabelConfigDao.deleteSelectedObjectDataList(lv1Name);
        updateImportObjectData(batchNum, voList);
    }

    private UploadInfoVO setUserUploadInfo(Attachment attachment) {
        Map<String, String> resultMap = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        params.put("module", ModuleEnum.MODULE_AUDIT.getDesc());
        FileProcessUtis.getFileName(attachment, resultMap);
        Long userId = UserHandle.getUserId();
        // 设置上传参数
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName(resultMap.get("prefix"));
        uploadInfoVO.setFileSize(FileProcessUtis.getFileSize(attachment));
        uploadInfoVO.setParams(params);
        uploadInfoVO.setUserId(userId);
        uploadInfoVO.setOptFlag(false);
        uploadInfoVO.setCheckList(null);
        uploadInfoVO.setSuffix(resultMap.get("suffix"));
        return uploadInfoVO;
    }

    /**
     * 记录错误日志信息
     *
     * @param uploadInfoVO
     * @param verifyDataList
     */
    public abstract void logErrorRecord(UploadInfoVO uploadInfoVO, List verifyDataList)
            throws CommonApplicationException;

    /**
     * 读取Excel内容
     *
     * @param attachment   attachment
     * @param heads        heads
     * @param uploadInfoVO uploadInfoVO
     * @param paramMap     paramMap
     * @param roleId
     */
    public void readExcel(Attachment attachment, List<ExcelVO> heads, UploadInfoVO uploadInfoVO,
                          Map<String, Object> paramMap, String roleId) throws CommonApplicationException {
        ByteArrayOutputStream byteArrayOutputStream = null;
        InputStream inputStream = null;
        try {
            DmFopRecordVO dmFoiImpExpRecordVO = new DmFopRecordVO();
            ZipSecureFile.setMinInflateRatio(-1.0d);
            // 生成指定文件字节流
            byteArrayOutputStream = excelUtil.putInputStreamCacher(attachment.getDataHandler().getInputStream());
            inputStream = getInputStream(byteArrayOutputStream);
            // 基本校验
            if (!((Constants.XLSX.getValue().equalsIgnoreCase(uploadInfoVO.getSuffix())
                || Constants.XLS.getValue().equalsIgnoreCase(uploadInfoVO.getSuffix()))
                && inputStream.available() <= 6144000)) {
                throw new CommonApplicationException(CommonConstant.LABEL_CONFIG_EXCEPTION2);
            }
            // 上传文件至S3，保留文件副本
            FileProcessUtis.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1,
                    getInputStream(byteArrayOutputStream));
            uploadInfoVO.setFileKey(dmFoiImpExpRecordVO.getFileSourceKey());
            List<Object> dataList = new ArrayList<>();
            AtomicInteger counterRef = new AtomicInteger();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            Map<String, Object> preparedInfo = new HashMap<>();
            // 导入前所需信息准备,有其他用途可自行重写
            getReadyInfoBeforeTraversal(preparedInfo, roleId);
            // 开始读取，遍历信息
            XSSFSheet sheet = workbook.getSheetAt(0);
            uploadInfoVO.setSheetName(sheet.getSheetName());
            uploadInfoVO.setRowNumber(-1);
            preparedInfo.put("ExcelName", getTitleList(sheet));
            for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                XSSFRow row = sheet.getRow(rowNum);
                if (Objects.isNull(row)|| CommUtils.checkNullRow(row)) {
                    // 记录插入条数
                    uploadInfoVO.setRowNumber(rowNum - 1);
                    break;
                }
                Map<String, Object> dataMap = new HashMap();
                // 获取自定义数据，由heads指定获取赋值的数据项
                setRequestAndVerifyData(row, dataList, heads, preparedInfo, counterRef, dataMap);
                setNormalInfoAndCheckRepeated(dataList, dataMap, counterRef, preparedInfo);
            }
            // 记录待持久化数据List
            paramMap.put("dataInfo", dataList);
            uploadInfoVO.setRowNumber(dataList.size());
            // 记录问题信息条数
            paramMap.put("countNum", counterRef.get());
            // 记录本次导入所需要的信息（包含权限）
            paramMap.put("preparedInfo", preparedInfo);
        } catch (CommonApplicationException ex) {
            // 记录异常信息
            uploadInfoVO.setErrorTips(ex.getMessage());
        } catch (Exception ex) {
            logger.error("importExcel occurs error：{}", ex.getMessage());
            // 记录异常信息
            uploadInfoVO.setErrorTips(CommonConstant.LABEL_CONFIG_EXCEPTION5);
        } finally {
            // 关闭流
            try {
                if (null != inputStream) {
                    inputStream.close();
                }
                if (null != byteArrayOutputStream) {
                    byteArrayOutputStream.close();
                }
            } catch (IOException ex) {
                logger.error("io close occurs error: {}", CommonConstant.STREAM_READ_ERROR_TIPS);
            }
        }
    }

    private List<String> getTitleList(XSSFSheet sheet) {
        XSSFRow nameRow = sheet.getRow(0);
        List<String> excelName = new ArrayList<>();
        for (int i = 0; i < nameRow.getPhysicalNumberOfCells(); i++) {
            excelName.add(PoiUtils.getStringCellValue(nameRow.getCell(i)));
        }
        return excelName;
    }

    public void setRequestAndVerifyData(XSSFRow row, List<Object> dataList, List<ExcelVO> heads,
        Map<String, Object> keys, AtomicInteger counterRef, Map<String, Object> dataMap)
        throws CommonApplicationException {
        List excelName = (List) keys.get("ExcelName");
        int lineNum = 0;
        Map<String, ExcelVO> excelVOMap = getLegalTitleMap();
        for (int cellNum = 0; cellNum < excelName.size(); cellNum++) {
            XSSFCell cell = row.getCell(cellNum);
            String title = String.valueOf(excelName.get(cellNum));
            if (StrUtil.equals(Constants.SNULL.getValue(), title) || !excelVOMap.keySet().contains(title)) {
                continue;
            }
            setVoDataInfoAndCheck(dataList, title,
                excelUtilPro.getStringCellValue(cell, excelVOMap.get(title).getHeadType()), counterRef, keys, dataMap);
            lineNum++;
        }
        if (excelVOMap.size() != lineNum) {
            throw new CommonApplicationException(CommonConstant.LABEL_CONFIG_EXCEPTION5);
        }
    }

    // 判断当前列是否是需要的列
    public abstract Map<String, ExcelVO> getLegalTitleMap();

    public void getReadyInfoBeforeTraversal(Map<String, Object> keys, String roleId) {
        List<String> lv1Name = new ArrayList<>();
        DataPermissionsVO rightVO = getUserRoleOpt(Integer.parseInt(roleId));
        // 校验权限信息
        if (!rightVO.isOptLv1Right()) {
            keys.put("lv1Name", new ArrayList<>());
            return;
        }
        // 用户产业权限为all的，才可以新增产业（新增产业必须在产品维表里面）
        // 用户产业权限为权限非All时候，校验excel内的产业，是否在权限列表里面
        Map<String, HolisticViewConfigDataVO> hierarchySortInfoLv1 = iLabelConfigDao.getHierarchySortInfoLv1();
        Map<String,String> infoMap = new HashMap<>();
        hierarchySortInfoLv1.keySet()
            .forEach(lv1 -> infoMap.put(hierarchySortInfoLv1.get(lv1).getLv1Name(),
                hierarchySortInfoLv1.get(lv1).getLv1Code()));
        keys.put("hierarchySortInfoLv1", infoMap);
        // 增加l1名称字段校验的数据源
        keys.put("l1NameList", iLabelConfigDao.getIctAllL1Item());
        if (!StringUtils.equals("ALL", rightVO.getLv1DataType())) {
            // 取权限与支持产品的交集
            Arrays.asList(rightVO.getLv1DimensionSet().toArray(new String[0])).stream().forEach(lv1 -> {
                if (hierarchySortInfoLv1.containsKey(lv1)) {
                    lv1Name.add(hierarchySortInfoLv1.get(lv1).getLv1Name());
                }
            });
            keys.put("lv1Name", lv1Name);
        } else {
            hierarchySortInfoLv1.keySet().forEach(lv1 -> lv1Name.add(hierarchySortInfoLv1.get(lv1).getLv1Name()));
            keys.put("lv1Name", lv1Name);
        }
    }

    public abstract void setVoDataInfoAndCheck(List<Object> dataList, String title, String value,
        AtomicInteger atomicInteger, Map<String, Object> keys, Map<String, Object> info);

    public abstract void setNormalInfoAndCheckRepeated(List<Object> dataList, Map<String, Object> dataMap,
        AtomicInteger counterRef, Map<String, Object> preparedInfo);

    public InputStream getInputStream(ByteArrayOutputStream byteArrayOutputStream) {
        return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
    }

    public void buildExportStreamAndUpload(Map params, String userId, List list, List<TableHeaderVo> headerVoList)
            throws CommonApplicationException {
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        // 构建导出Excel的表头信息
        HeaderUtils.buildHeader(titleVoList, titles, headerVoList);
        String fileName = String.valueOf(params.get("fileName"));
        FileOutputStream outputStream = null;
        File targetFile = null;
        try {
            targetFile = File.createTempFile(fileName, ".xlsx");
            outputStream = new FileOutputStream(targetFile);
            List<Map<String, Object>> resultList = new ArrayList();
            // Bean转化为Map形式方便数据使用
            resetDataList(list, resultList);
            excelUtil.expSelectColumnExcel(outputStream, params, userId, titles, titleVoList, resultList);
            params.put("fileKey",
                    FileProcessUtis.uploadToS3(targetFile, fileName, String.valueOf(params.get("userId"))));
        } catch (Exception ex) {
            logger.error("生成导出|修改流异常");
            throw new CommonApplicationException("生成导出|修改流异常");
        } finally {
            if (null != targetFile) {
                if (!targetFile.delete()) {
                    logger.error("delete file " + fileName + "failed");
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    logger.error("close stream failed");
                }
            }
        }
    }

    public void resetDataList(List resultResult, List<Map<String, Object>> infoList) {
        resultResult.stream().forEach(item -> infoList.add(BeanUtil.beanToMap(item)));
    }

    /**
     * 处理coa维表导入的数据
     *
     * @param batchNum batchNum
     * @param voList voList
     */
    protected void updateImportCoaData(int batchNum, List<Object> voList) {
        if (batchNum < 1) {
            iLabelConfigDao.createCoaDataList(voList);
        } else {
            for (int i = 0; i < batchNum; i++) {
                List<Object> subList = voList.subList(i * 600, (i + 1) * 600);
                iLabelConfigDao.createCoaDataList(subList);
            }
            // 处理尾部数据
            List<Object> subList = voList.subList(batchNum * 600, voList.size());
            if (!subList.isEmpty()) {
                iLabelConfigDao.createCoaDataList(subList);
            }
        }
    }

    /**
     * 处理对象维表导入的数据
     *
     * @param batchNum batchNum
     * @param voList voList
     */
    protected void updateImportObjectData(int batchNum, List<Object> voList) {
        if (batchNum < 1) {
            iLabelConfigDao.createObjectDataList(voList);
        } else {
            for (int i = 0; i < batchNum; i++) {
                List<Object> subList = voList.subList(i * 600, (i + 1) * 600);
                iLabelConfigDao.createObjectDataList(subList);
            }
            // 处理尾部数据
            List<Object> subList = voList.subList(batchNum * 600, voList.size());
            if (!subList.isEmpty()) {
                iLabelConfigDao.createObjectDataList(subList);
            }
        }
    }

    /**
     * 处理Ict全景图导入的数据
     *
     * @param batchNum batchNum
     * @param voList voList
     */
    protected void updateImportIctData(int batchNum, List<Object> voList) {
        if (batchNum < 1) {
            iLabelConfigDao.createIctDataList(voList);
        } else {
            for (int i = 0; i < batchNum; i++) {
                List<Object> subList = voList.subList(i * 600, (i + 1) * 600);
                iLabelConfigDao.createIctDataList(subList);
            }
            // 处理尾部数据
            List<Object> subList = voList.subList(batchNum * 600, voList.size());
            if (!subList.isEmpty()) {
                iLabelConfigDao.createIctDataList(subList);
            }
        }
    }
    protected void updateImportPlanComData(int batchNum, List<Object> voList) {
        // 分批次插入
        if (batchNum < 1) {
            iLabelConfigDao.createPlanComDataList(voList);
        } else {
            for (int i = 0; i < batchNum; i++) {
                List<Object> subList = voList.subList(i * 600, (i + 1) * 600);
                iLabelConfigDao.createPlanComDataList(subList);
            }
            // 处理尾部数据
            List<Object> subList = voList.subList(batchNum * 600, voList.size());
            if (!subList.isEmpty()) {
                iLabelConfigDao.createPlanComDataList(subList);
            }
        }
    }

    protected void checkErrorValue(AtomicInteger atomicInteger, Map<String, Object> objectMap, StringBuilder builder) {
        if (builder.length() > 0) {
            atomicInteger.addAndGet(1);
            if (objectMap.containsKey("errorMsg")) {
                objectMap.put("errorMsg", objectMap.get("errorMsg") + builder.toString());
            } else {
                objectMap.put("errorMsg", builder.toString());
            }
        }
    }
}
