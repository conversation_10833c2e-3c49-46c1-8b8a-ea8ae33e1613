/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.utils;

import com.huawei.it.jalor5.core.request.IUserPrincipal;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.lang3.StringUtils;

/**
 * The class UserHandle.java
 *
 * <AUTHOR>
 * @since 2021年7月28日
 */
public class UserHandle {
    /**
     * [服务名称]getUser
     *
     * @param type 入参
     * @return String
     * <AUTHOR>
     */
    public static String getUser(String type) {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        String uu = "";
        switch (user == null ? "OTHER" : type) {
            case "W3ACCOUT":
                uu = user.getUserAccount();
                break;
            case "USERID":
                uu = user.getUserId() + "";
                break;
            case "USERNAME":
                uu = user.getUserCN();
                break;
            case "NUMBER":
                uu = user.getEmployeeNumber();
                break;
            default:
                uu = "0";
        }
        return uu;
    }

    /**
     * [服务名称]getUserName
     *
     * @return String
     * <AUTHOR>
     */
    public static Long getUserId() {
        String user = UserHandle.getUser("USERID");
        return (user == null ? -1 : Long.parseLong(user));
    }

    /**
     * [服务名称]getUserName
     *
     * @return String
     * <AUTHOR>
     */
    public static String getUserName() {
        String user = getUser("NUMBER");
        return StringUtils.isBlank(user) ? getUser("W3ACCOUT") : user;
    }

    /**
     * [服务名称]getUserRoleList
     *
     * @return List<RoleVO>
     */
    public static String getCurrentRole() {
        UserVO curUser = (UserVO) RequestContext.getCurrent(true).getUser();
        RoleVO currentRole = curUser.getCurrentRole();
        return currentRole.getRoleName();
    }

    /**
     * get role id of the current login user
     *
     * @return int role id
     */
    public static int getRoleId() {
        return ((UserVO) RequestContext.getCurrent().getUser()).getCurrentRole().getRoleId();
    }

}
