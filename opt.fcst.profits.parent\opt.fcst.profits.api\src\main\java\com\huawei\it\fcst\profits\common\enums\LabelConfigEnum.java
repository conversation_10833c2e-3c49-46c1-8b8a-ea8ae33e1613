/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * BgEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum LabelConfigEnum {

    CONFIG("OBJECT","对象维表"),
    COA("COA","COA维表"),
    PLAN_COM("PLAN_COM","计委包维表"),
    GROUP_ANALYST("GROUP_ANALYST","集团分析师预测结果"),
    HOLISTIC_VIEW("HOLISTIC_VIEW","ICT业务预测全景图");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    LabelConfigEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * getByCode
     *
     * @param code code
     * @return StepTypeEnum
     */
    public static LabelConfigEnum getByCode(String code) {
        for (LabelConfigEnum value : LabelConfigEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

