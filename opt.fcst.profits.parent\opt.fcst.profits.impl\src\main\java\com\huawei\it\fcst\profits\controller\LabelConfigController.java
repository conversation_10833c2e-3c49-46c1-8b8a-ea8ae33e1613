/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.LabelConfigEnum;
import com.huawei.it.fcst.profits.common.utils.FcstGlobalParameterUtil;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.common.vo.ResultCode;
import com.huawei.it.fcst.profits.dao.ILabelConfigDao;
import com.huawei.it.fcst.profits.service.IFcstProfitsCommonService;
import com.huawei.it.fcst.profits.service.ILabelConfigurtionService;
import com.huawei.it.fcst.profits.service.ILabelProductionService;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.utils.HeaderTitleUtils;
import com.huawei.it.fcst.profits.vo.CoaConfigDataVO;
import com.huawei.it.fcst.profits.vo.CoaProdInfoVO;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.fcst.profits.vo.HolisticViewProdInfoVO;
import com.huawei.it.fcst.profits.vo.ObjectConfigDataVO;
import com.huawei.it.fcst.profits.vo.PlanComConfigDataVO;
import com.huawei.it.fcst.profits.vo.PlanComProdInfoVo;
import com.huawei.it.fcst.profits.vo.request.LabelConfigQueryRequest;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.annotation.SecurityPolicy;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.cache.ehcache.CacheEvent;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;

import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.web.support.service.IClusterInvokeService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.MessageFormat;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

/**
 * 标签审视、标签配置
 */
@JalorResource(code = "labelConfigController", desc = "labelConfigController")
@Named("labelConfigController")
public class LabelConfigController implements ILabelConfigController {

    private static final Logger logger = LoggerFactory.getLogger(LabelConfigController.class);

    @Autowired
    private ILabelConfigurtionService labelConfigurationService;

    @Autowired
    private IFcstProfitsCommonService iFcstProfitsCommonService;

    @Autowired
    private ILabelProductionService iLabelProductionService;

    @Inject
    private IClusterInvokeService cluster;

    @Inject
    private ILabelConfigDao iLabelConfigDao;

    @Audit(module="labelConfigController-templateConfigDataDownload",operation="templateConfigDataDownload",message="downImportTemplate 下载导入模板")
    @JalorOperation(code = "templateConfigDataDownload", desc = "downImportTemplate 下载导入模板")
    @Override
    public CommonResult templateConfigDataDownload(HttpServletResponse response, String datatype) {
        try {
            labelConfigurationService.templateDownload(response, datatype);
            return CommonResult.success("下载成功");
        } catch (CommonApplicationException ex) {
            logger.error("下载模板异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "下载失败");
        }
    }

    @Audit(module="labelConfigController-importConfigCoaData",operation="importConfigCoaData",message="导入标签配置Coa信息")
    @JalorOperation(code = "importConfigCoaData", desc = "导入标签配置Coa信息")
    @Override
    public CommonResult importConfigCoaData(Attachment file, String roleId) {
        try {
            CommonResult<Object> checkTip = getObjectCommonResult();
            if (null != checkTip) {
                return checkTip;
            }
            return labelConfigurationService.importCoaData(file, roleId);
        } catch (CommonApplicationException e) {
            logger.error("导入异常：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    @Nullable
    private CommonResult<Object> getObjectCommonResult() {
        int day = TimeUtils.getDayOfMonth();
        Map<String, Object> daysInfo = iFcstProfitsCommonService.validateSumbit();
        Integer startDay = (Integer) daysInfo.get("startDay");
        Integer endDay = (Integer) daysInfo.get("endDay");
        if (!(Boolean) daysInfo.get("isSubmit")) {
            if (ObjectUtils.anyNull(startDay, endDay)) {
                return CommonResult.failed(ResultCode.CHECK_TIP, "不可导入!");
            } else {
                return CommonResult.failed(ResultCode.CHECK_TIP,
                    String.format(Locale.ROOT, "请于每月的%s号00:00:00后至%s号12:00:00前进行导入！当前为%s号,不可提交!", startDay, endDay, day));
            }
        }
        return null;
    }

    @Audit(module="labelConfigController-importConfigPlanComData",operation="importConfigPlanComData",message="导入标签配置plancom信息")
    @JalorOperation(code = "importConfigPlanComData", desc = "导入标签配置plancom信息")
    @Override
    public CommonResult importConfigPlanComData(Attachment file, String roleId) {
        try {
            CommonResult<Object> checkTip = getObjectCommonResult();
            if (null != checkTip) {
                return checkTip;
            }
            return labelConfigurationService.importPlanComData(file, roleId);
        } catch (CommonApplicationException e) {
            logger.error("导入异常：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    @Audit(module="labelConfigController-importConfigIctData",operation="importConfigIctData",message="导入标签配置ict信息")
    @JalorOperation(code = "importConfigIctData", desc = "导入标签配置ict信息")
    @Override
    public CommonResult importConfigIctData(Attachment file, String roleId) {
        try {
            CommonResult<Object> checkTip = getObjectCommonResult();
            if (null != checkTip) {
                return checkTip;
            }
            return labelConfigurationService.importIctData(file, roleId);
        } catch (CommonApplicationException e) {
            logger.error("导入异常：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    @Audit(module="labelConfigController-exportConfigCoaData",operation="exportConfigCoaData",message="导出标签配置coa信息")
    @JalorOperation(code = "exportConfigCoaData", desc = "导出标签配置coa信息")
    @Override
    public CommonResult exportConfigCoaData(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest) {
        try {
            vailArrayParamLimitSize(labelConfigRequest);
            labelConfigurationService.exportCoaData(response, labelConfigRequest);
            return CommonResult.success("成功");
        } catch (CommonApplicationException ex) {
            logger.error("导出Coa维表信息：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, ex.getMessage());
        }
    }

    @Audit(module="labelConfigController-exportConfigObjectData",operation="exportConfigObjectData",message="导入标签配置object信息")
    @JalorOperation(code = "exportConfigObjectData", desc = "导入标签配置object信息")
    @Override
    public CommonResult exportConfigObjectData(HttpServletResponse response,
        LabelConfigQueryRequest labelConfigRequest) {
        try {
            vailArrayParamLimitSize(labelConfigRequest);
            labelConfigurationService.exportObjectData(response, labelConfigRequest);
            return CommonResult.success("成功");
        } catch (CommonApplicationException ex) {
            logger.error("导出对象维表信息：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, ex.getMessage());
        }
    }

    @Audit(module="labelConfigController-exportConfigPlanComData",operation="exportConfigPlanComData",message="导出标签配置plancom信息")
    @JalorOperation(code = "exportConfigPlanComData", desc = "导出标签配置plancom信息")
    @Override
    public CommonResult exportConfigPlanComData(HttpServletResponse response,
        LabelConfigQueryRequest labelConfigRequest) {
        try {
            vailArrayParamLimitSize(labelConfigRequest);
            labelConfigurationService.exportPlanComData(response, labelConfigRequest);
            return CommonResult.success("成功");
        } catch (CommonApplicationException ex) {
            logger.error("导出计委包表信息：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, ex.getMessage());
        }
    }

    @Audit(module="labelConfigController-exportConfigIctData",operation="exportConfigIctData",message="导出标签配置ict信息")
    @JalorOperation(code = "exportConfigIctData", desc = "导出标签配置ict信息")
    @Override
    public CommonResult exportConfigIctData(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest) {
        try {
            vailArrayParamLimitSize(labelConfigRequest);
            labelConfigurationService.exportIctData(response, labelConfigRequest);
            return CommonResult.success("成功");
        } catch (CommonApplicationException ex) {
            logger.error("导出ict表信息：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, ex.getMessage());
        }
    }

    @Audit(module="labelConfigController-getCoaConfigInfoByPage",operation="getCoaConfigInfoByPage",message="查询标签配置coa信息列表")
    @JalorOperation(code = "getCoaConfigInfoByPage", desc = "查询标签配置coa信息列表")
    @Override
    public CommonResult getCoaConfigInfoByPage(LabelConfigQueryRequest request) {
        try {
            vailArrayParamLimitSize(request);
            PagedResult<CoaConfigDataVO> pagedResult = labelConfigurationService.findCoaDataByPage(request);
            Map result = new LinkedHashMap();
            // 获取表头信息进行封装 HeadList<>
            result.put("headList", HeaderTitleUtils.creatImportHeaderList(LabelConfigEnum.COA.getCode()));
            result.put("result", pagedResult.getResult());
            result.put("pageVo", pagedResult.getPageVO());
            result.put("fullImportFlag",iLabelProductionService.checkUserPermissionAllProd(request.getRoleId()));
            result.putAll(iFcstProfitsCommonService.validateSumbit());
            return CommonResult.success(result);
        } catch (CommonApplicationException ex) {
            logger.error("获取getCoaConfigInfoByPage异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Audit(module="labelConfigController-getPlanComConfigInfoByPage",operation="getPlanComConfigInfoByPage",message="查询标签配置信息plancom列表")
    @JalorOperation(code = "getPlanComConfigInfoByPage", desc = "查询标签配置信息plancom列表")
    @Override
    public CommonResult getPlanComConfigInfoByPage(LabelConfigQueryRequest request) {
        try {
            vailArrayParamLimitSize(request);
            PagedResult<PlanComConfigDataVO> pagedResult = labelConfigurationService.findPlanComDataByPage(request);
            Map result = new LinkedHashMap();
            // 获取表头信息进行封装 HeadList<>
            result.put("headList", HeaderTitleUtils.creatImportHeaderList(LabelConfigEnum.PLAN_COM.getCode()));
            result.put("result", pagedResult.getResult());
            result.put("pageVo", pagedResult.getPageVO());
            result.put("fullImportFlag",iLabelProductionService.checkUserPermissionAllProd(request.getRoleId()));
            result.putAll(iFcstProfitsCommonService.validateSumbit());
            return CommonResult.success(result);
        } catch (CommonApplicationException ex) {
            logger.error("获取getPlanComConfigInfoByPage异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Audit(module="labelConfigController-getIctConfigInfoByPage",operation="getIctConfigInfoByPage",message="查询标签配置信息ict列表")
    @JalorOperation(code = "getIctConfigInfoByPage", desc = "查询标签配置信息ict列表")
    @Override
    public CommonResult getIctConfigInfoByPage(LabelConfigQueryRequest request) {
        try {
            vailArrayParamLimitSize(request);
            PagedResult<HolisticViewConfigDataVO> pagedResult = labelConfigurationService.findIctDataByPage(request);
            Map result = new LinkedHashMap();
            // 获取表头信息进行封装 HeadList<>
            result.put("headList", HeaderTitleUtils.creatImportHeaderList(LabelConfigEnum.HOLISTIC_VIEW.getCode()));
            result.put("result", pagedResult.getResult());
            result.put("pageVo", pagedResult.getPageVO());
            result.put("fullImportFlag",iLabelProductionService.checkUserPermissionAllProd(request.getRoleId()));
            result.putAll(iFcstProfitsCommonService.validateSumbit());
            return CommonResult.success(result);
        } catch (CommonApplicationException ex) {
            logger.error("获取getIctConfigInfoByPage异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Audit(module="labelConfigController-getCoaProductionNames",operation="getCoaProductionNames",message="查询标签配置产业coa列表")
    @JalorOperation(code = "getCoaProductionNames", desc = "查询标签配置产业coa列表")
    @Override
    public CommonResult getCoaProductionNames(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getCoaProductionNames(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询Coa产业名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询Coa产业名称失败");
        }
    }

    @JalorOperation(code = "getCoaL1Names", desc = "查询标签配置l1coa列表")
    @Override
    public CommonResult getCoaL1Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getCoaL1Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询CoaL1名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询CoaL1名称失败");
        }
    }

    @JalorOperation(code = "getCoaL2Names", desc = "查询标签配置l2coa列表")
    @Override
    public CommonResult getCoaL2Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getCoaL2Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询CoaL2名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询CoaL2名称失败");
        }
    }

    @JalorOperation(code = "getCoaL3Names", desc = "查询标签配置l3coa列表")
    @Override
    public CommonResult getCoaCodes(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getCoaCodes(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询Coa产业名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询Coa产业名称失败");
        }
    }

    @JalorOperation(code = "getPlanComLv1Names", desc = "查询标签配置产业plancom列表")
    @Override
    public CommonResult getPlanComLv1Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getPlanComLv1Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询一级计委包名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询一级计委包名称失败");
        }
    }

    @JalorOperation(code = "getPlanComLv2Names", desc = "查询标签配置lv2plancom列表")
    @Override
    public CommonResult getPlanComLv2Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getPlanComLv2Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询二级计委包名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询二级计委包名称失败");
        }
    }

    @JalorOperation(code = "getPlanComLv3Names", desc = "查询标签配置lv3plancom列表")
    @Override
    public CommonResult getPlanComLv3Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getPlanComLv3Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询三级计委包名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询三级计委包名称失败");
        }
    }

    @JalorOperation(code = "getBusiLv4Names", desc = "查询标签配置lv4plancom列表")
    @Override
    public CommonResult getBusiLv4Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getBusiLv4Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询四级业务包名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询四级业务包名称失败");
        }
    }

    @JalorOperation(code = "getPlanComL1Names", desc = "查询标签配置l1plancom列表")
    @Override
    public CommonResult getPlanComL1Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getPlanComL1Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询计委包L1名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询计委包L1名称失败");
        }
    }

    @JalorOperation(code = "getPlanComL2Names", desc = "查询标签配置l2plancom列表")
    @Override
    public CommonResult getPlanComL2Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getPlanComL2Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询计委包L2名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询计委包L2名称失败");
        }
    }

    @JalorOperation(code = "getIctLv1Names", desc = "查询标签配置产业ict列表")
    @Override
    public CommonResult getIctLv1Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getIctLv1Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询IctLv1名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询IctLv1名称失败");
        }
    }

    @JalorOperation(code = "getIctLv2Names", desc = "查询标签配置lv2ict列表")
    @Override
    public CommonResult getIctLv2Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getIctLv2Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询IctLv2名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询IctLv2名称失败");
        }
    }

    @JalorOperation(code = "getIctLv3Names", desc = "查询标签配置lv3ict列表")
    @Override
    public CommonResult getIctLv3Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getIctLv3Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询IctLv3名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询IctLv3名称失败");
        }
    }

    @JalorOperation(code = "getIctL1Names", desc = "查询标签配置l1ict列表")
    @Override
    public CommonResult getIctL1Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getIctL1Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询IctL1名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询IctL1名称失败");
        }
    }

    @JalorOperation(code = "getIctArticulationFlagNames", desc = "查询标签配置勾稽标识ict列表")
    @Override
    public CommonResult getIctArticulationFlagNames(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getIctArticulationFlagNames(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询Ict勾稽标识名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询Ict勾稽标识名称失败");
        }
    }

    @Audit(module="labelConfigController-getObjConfigInfoByPage",operation="getObjConfigInfoByPage",message="导入标签配置object信息")
    @JalorOperation(code = "getObjConfigInfoByPage", desc = "导入标签配置object信息")
    @Override
    public CommonResult getObjConfigInfoByPage(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            PagedResult<ObjectConfigDataVO> pagedResult = labelConfigurationService.findObjectDataByPage(requestVO);
            Map result = new LinkedHashMap();
            // 获取表头信息进行封装 HeadList<>
            result.put("headList", HeaderTitleUtils.creatImportHeaderList(LabelConfigEnum.CONFIG.getCode()));
            result.put("result", pagedResult.getResult());
            result.put("pageVo", pagedResult.getPageVO());
            result.put("fullImportFlag",iLabelProductionService.checkUserPermissionAllProd(requestVO.getRoleId()));
            result.putAll(iFcstProfitsCommonService.validateSumbit());
            return CommonResult.success(result);
        } catch (CommonApplicationException ex) {
            logger.error("获取getObjConfigInfoByPage异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Audit(module="labelConfigController-importConfigObjectData",operation="importConfigObjectData",message="导入标签配置object信息")
    @JalorOperation(code = "importConfigObjectData", desc = "导入标签配置object信息")
    @Override
    public CommonResult importConfigObjectData(Attachment file, String roleId) {
        try {
            CommonResult<Object> checkTip = getObjectCommonResult();
            if (null != checkTip) {
                return checkTip;
            }
            return labelConfigurationService.importObjectData(file, roleId);
        } catch (CommonApplicationException e) {
            logger.error("导入标签配置信息失败：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    @JalorOperation(code = "getObjectLv1Names", desc = "查询标签配置产业object列表")
    @Override
    public CommonResult getObjectLv1Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getObjectLv1Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询对象维表Lv1名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询对象维表Lv1名称失败");
        }
    }

    @JalorOperation(code = "getObjectL1Names", desc = "查询标签配置l1object列表")
    @Override
    public CommonResult getObjectL1Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getObjectL1Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询对象维表L1名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询对象维表L1名称失败");
        }
    }

    @JalorOperation(code = "getObjectL2Names", desc = "查询标签配置l2object列表")
    @Override
    public CommonResult getObjectL2Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getObjectL2Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询对象维表L2名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询对象维表L2名称失败");
        }
    }

    @JalorOperation(code = "getObjectL3Names", desc = "查询标签配置l3object列表")
    @Override
    public CommonResult getObjectL3Names(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getObjectL3Names(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询对象维表L3名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询对象维表L3名称失败");
        }
    }

    @JalorOperation(code = "getIctUpdatedNames", desc = "Ict最后更新人筛选")
    @Override
    public CommonResult getIctUpdatedNames(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(labelConfigurationService.getIctUpdatedNames(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询Ict最后更新人筛选：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询Ict最后更新人筛选");
        }
    }

    @JalorOperation(code = "getPlanComUpdatedNames", desc = "查询计委包最后更新人筛选")
    @Override
    public CommonResult getPlanComUpdatedNames(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getPlanComUpdatedNames(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询计委包最后更新人筛选：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询计委包最后更新人筛选");
        }
    }

    @JalorOperation(code = "getCoaUpdatedNames", desc = "查询Coa最后更新人筛选")
    @Override
    public CommonResult getCoaUpdatedNames(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getCoaUpdatedNames(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询Coa最后更新人筛选：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询Coa最后更新人筛选");
        }
    }

    @JalorOperation(code = "getObjectUpdatedNames", desc = "查询Object最后更新人筛选")
    @Override
    public CommonResult getObjectUpdatedNames(LabelConfigQueryRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(labelConfigurationService.getObjectUpdatedNames(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询Object最后更新人筛选：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询Object最后更新人筛选");
        }
    }

    @Audit(module="labelConfigController-submitConfigCoaData",operation="submitConfigCoaData",message="提交Coa数据")
    @JalorOperation(code = "submitConfigCoaData", desc = "提交Coa数据")
    @Override
    public CommonResult submitConfigCoaData(LabelConfigQueryRequest requestVO) {
        try {
            CommonResult<Object> checkTip = getObjectCommonResult();
            if (null != checkTip) {
                return checkTip;
            }
            return labelConfigurationService.submitCoaData(requestVO);
        } catch (CommonApplicationException ex) {
            logger.error("提交Coa数据：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "提交Coa数据");
        }
    }

    @Audit(module="labelConfigController-submitConfigPlanComData",operation="submitConfigPlanComData",message="提交PlanCom数据")
    @JalorOperation(code = "submitConfigPlanComData", desc = "提交PlanCom数据")
    @Override
    public CommonResult submitConfigPlanComData(LabelConfigQueryRequest requestVO) {
        try {
            CommonResult<Object> checkTip = getObjectCommonResult();
            if (null != checkTip) {
                return checkTip;
            }
            return labelConfigurationService.submitPlanComData(requestVO);
        } catch (CommonApplicationException ex) {
            logger.error("提交PlanCom数据：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "提交PlanCom数据");
        }
    }

    @Audit(module="labelConfigController-submitConfigIctData",operation="submitConfigIctData",message="提交Ict数据")
    @JalorOperation(code = "submitConfigIctData", desc = "提交Ict数据")
    @Override
    public CommonResult submitConfigIctData(LabelConfigQueryRequest requestVO) throws ApplicationException {
        try {
            CommonResult<Object> checkTip = getObjectCommonResult();
            if (null != checkTip) {
                return checkTip;
            }
            return labelConfigurationService.submitConfigIctData(requestVO);
        } catch (CommonApplicationException ex) {
            logger.error("提交Ict数据：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "提交Ict数据");
        }finally {
            long startTm = System.currentTimeMillis();
            logger.info("[clearIctConfig execute, begin.]");
            String query = MessageFormat.format("type={0}", CacheEvent.EVICT_ALL_NAME);
            cluster.invoke("services/configManagement/initIctConfigCache", query);
            logger.info("[clearIctConfig execute, end. use time is : {}]", System.currentTimeMillis() - startTm);
        }
    }

    /**
     * 服务器内部调用，cluster.invoke("services/configManagement/initIctConfigCache", query);
     */
    @JalorOperation(policy = SecurityPolicy.JalorInternal)
    @Override
    public void initIctConfigCache() {
        logger.info("find config data.");
        List<HolisticViewConfigDataVO> articulationConfig = iLabelConfigDao.findArticulationConfig();
        logger.info("find config data.{}",articulationConfig.stream().collect(Collectors.toMap(HolisticViewConfigDataVO::getL1Name, Objects::toString)));
        if (CollectionUtils.isNotEmpty(articulationConfig)) {
            FcstGlobalParameterUtil.cleanConfigValue();
            FcstGlobalParameterUtil.putConfigValue(articulationConfig.stream()
                    .collect(Collectors.groupingBy(HolisticViewConfigDataVO::getArticulationFlag, Collectors.mapping(HolisticViewConfigDataVO::getL1Name, Collectors.toList())))
            );
        }
    }

    @Audit(module="labelConfigController-submitConfigObjectData",operation="submitConfigObjectData",message="提交Object数据")
    @JalorOperation(code = "submitConfigObjectData", desc = "提交Object数据")
    @Override
    public CommonResult submitConfigObjectData(LabelConfigQueryRequest requestVO) {
        try {
            CommonResult<Object> checkTip = getObjectCommonResult();
            if (null != checkTip) {
                return checkTip;
            }
            return labelConfigurationService.submitConfigObjectData(requestVO);
        } catch (CommonApplicationException ex) {
            logger.error("提交Object数据：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "提交Object数据");
        }
    }

    @JalorOperation(code = "getPlanComProductionNames", desc = "查询计委包产业名称")
    @Override
    public CommonResult getPlanComProductionNames(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(labelConfigurationService.getPlanComProductionNames(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("查询计委包产业名称失败：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询计委包产业名称失败");
        }
    }

    @Audit(module="labelConfigController-queryIctProductInfoByPage",operation="queryIctProductInfoByPage",message="产品维度查询")
    @JalorOperation(code = "queryIctProductInfoByPage", desc = "产品维度查询")
    @Override
    public CommonResult queryIctProductInfoByPage(LabelConfigQueryRequest requestVO) {
        // 带分页
        try {
            PagedResult<HolisticViewProdInfoVO> pagedResult = iLabelProductionService.queryIctProductInfoByPage(requestVO);
            Map result = new LinkedHashMap();
            result.put("result", pagedResult.getResult());
            result.put("pageVo", pagedResult.getPageVO());
            return CommonResult.success(result);
        } catch (CommonApplicationException ex) {
            logger.error("获取getCoaConfigInfoByPage异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }
    @Audit(module="labelConfigController-exportIctProductInfo",operation="exportIctProductInfo",message="产品维度导出")
    @JalorOperation(code = "exportIctProductInfo", desc = "产品维度导出")
    @Override
    public CommonResult exportIctProductInfo(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest) {
        try {
            iLabelProductionService.exportIctProductInfo(response, labelConfigRequest);
            return CommonResult.success("成功");
        } catch (CommonApplicationException ex) {
            logger.error("产品维度导出异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, ex.getMessage());
        }
    }
    @Audit(module="labelConfigController-queryCoaProductInfoByPage",operation="queryCoaProductInfoByPage",message="产品COA信息查询")
    @JalorOperation(code = "queryCoaProductInfoByPage", desc = "产品COA信息查询")
    @Override
    public CommonResult queryCoaProductInfoByPage(LabelConfigQueryRequest requestVO) {
        // 带分页
        try {
            PagedResult<CoaProdInfoVO> pagedResult = iLabelProductionService.queryCoaProductInfoByPage(requestVO);
            Map result = new LinkedHashMap();
            result.put("result", pagedResult.getResult());
            result.put("pageVo", pagedResult.getPageVO());
            return CommonResult.success(result);
        } catch (CommonApplicationException ex) {
            logger.error("获取getCoaConfigInfoByPage异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }
    @Audit(module="labelConfigController-exportCoaProductInfo",operation="exportCoaProductInfo",message="产品COA信息导出")
    @JalorOperation(code = "exportCoaProductInfo", desc = "产品COA信息导出")
    @Override
    public CommonResult exportCoaProductInfo(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest) {
        try {
            iLabelProductionService.exportCoaProductInfo(response, labelConfigRequest);
            return CommonResult.success("成功");
        } catch (CommonApplicationException ex) {
            logger.error("产品COA信息导出异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, ex.getMessage());
        }
    }
    @Audit(module="labelConfigController-queryPlanComProductInfoByPage",operation="queryPlanComProductInfoByPage",message="计委包维度查询")
    @JalorOperation(code = "queryPlanComProductInfoByPage", desc = "计委包维度查询")
    @Override
    public CommonResult queryPlanComProductInfoByPage(LabelConfigQueryRequest requestVO) {
        // 带分页
        try {
            PagedResult<PlanComProdInfoVo> pagedResult = iLabelProductionService.queryPlanComProductInfoByPage(requestVO);
            Map result = new LinkedHashMap();
            result.put("result", pagedResult.getResult());
            result.put("pageVo", pagedResult.getPageVO());
            return CommonResult.success(result);
        } catch (CommonApplicationException ex) {
            logger.error("获取getCoaConfigInfoByPage异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Audit(module="labelConfigController-exportPlanComProductInfo",operation="exportPlanComProductInfo",message="计委包维度导出")
    @JalorOperation(code = "exportPlanComProductInfo", desc = "计委包维度导出")
    @Override
    public CommonResult exportPlanComProductInfo(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest) {
        try {
            iLabelProductionService.exportPlanComProductInfo(response, labelConfigRequest);
            return CommonResult.success("成功");
        } catch (CommonApplicationException ex) {
            logger.error("计委包维度导出异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, ex.getMessage());
        }
    }

    @JalorOperation(code = "getIctMonitorCondition", desc = "列表条件全查(ICT全景图-维度监控)")
    @Override
    public CommonResult getIctMonitorCondition(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getIctMonitorCondition(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("列表条件全查(ICT全景图-维度监控)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "列表条件全查(ICT全景图-维度监控)");
        }
    }
    @JalorOperation(code = "getIctMonitorVersion", desc = "版本信息获取(ICT全景图-维度监控)")
    @Override
    public CommonResult getIctMonitorVersion(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getIctMonitorVersion(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("版本信息获取(ICT全景图-维度监控)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "版本信息获取(ICT全景图-维度监控)");
        }
    }
    @JalorOperation(code = "getIctMonitorLastUpdated", desc = "最后更新人查询(ICT全景图-维度监控)")
    @Override
    public CommonResult getIctMonitorLastUpdated(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getIctMonitorLastUpdated(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("最后更新人查询(ICT全景图-维度监控)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "最后更新人查询(ICT全景图-维度监控)");
        }
    }

    @JalorOperation(code = "getIctConfigCondition", desc = "列表条件全查(ICT表配置页)")
    @Override
    public CommonResult getIctConfigCondition(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getIctConfigCondition(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("列表条件全查(ICT表配置页)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "列表条件全查(ICT表配置页)");
        }
    }
    @JalorOperation(code = "getPlanComMonitorCondition", desc = "列表条件全查(计委包-维度监控)")
    @Override
    public CommonResult getPlanComMonitorCondition(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getPlanComMonitorCondition(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("列表条件全查(计委包-维度监控)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "列表条件全查(计委包-维度监控)");
        }
    }
    @JalorOperation(code = "getPlanComMonitorVersion", desc = "版本信息获取(计委包-维度监控)")
    @Override
    public CommonResult getPlanComMonitorVersion(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getPlanComMonitorVersion(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("版本信息获取(计委包-维度监控)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "版本信息获取(计委包-维度监控)");
        }
    }
    @JalorOperation(code = "getPlanComMonitorLastUpdated", desc = "最后更新人查询(计委包-维度监控)")
    @Override
    public CommonResult getPlanComMonitorLastUpdated(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getPlanComMonitorLastUpdated(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("最后更新人查询(计委包-维度监控)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "最后更新人查询(计委包-维度监控)");
        }
    }
    @JalorOperation(code = "getCoaMonitorCondition", desc = "列表条件全查(COA维表-维度监控)")
    @Override
    public CommonResult getCoaMonitorCondition(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getCoaMonitorCondition(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("列表条件全查(COA维表-维度监控)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "列表条件全查(COA维表-维度监控)");
        }
    }
    @JalorOperation(code = "getCoaMonitorVersion", desc = "版本信息获取(COA维表-维度监控)")
    @Override
    public CommonResult getCoaMonitorVersion(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getCoaMonitorVersion(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("版本信息获取(COA维表-维度监控)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "版本信息获取(COA维表-维度监控)");
        }
    }
    @JalorOperation(code = "getCoaMonitorProductName", desc = "产品名称信息获取(COA维表-维度监控)")
    @Override
    public CommonResult getCoaMonitorProductName(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getCoaMonitorProductName(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("产品名称信息获取(COA维表-维度监控)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "产品名称信息获取(COA维表-维度监控)");
        }
    }
    @JalorOperation(code = "getCoaMonitorLastUpdated", desc = "最后更新人查询(COA维表-维度监控)")
    @Override
    public CommonResult getCoaMonitorLastUpdated(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getCoaMonitorLastUpdated(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("最后更新人查询(COA维表-维度监控)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "最后更新人查询(COA维表-维度监控)");
        }
    }
    @JalorOperation(code = "getObjectAuditCondition", desc = "列表条件全查(对象维表-标签审视)")
    @Override
    public CommonResult getObjectAuditCondition(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getObjectAuditCondition(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("列表条件全查(对象维表-标签审视)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "列表条件全查(对象维表-标签审视)");
        }
    }
    @JalorOperation(code = "getObjectConfigCondition", desc = "列表条件全查(对象维表配置页)")
    @Override
    public CommonResult getObjectConfigCondition(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getObjectConfigCondition(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("列表条件全查(对象维表配置页)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "列表条件全查(对象维表配置页)");
        }
    }
    @JalorOperation(code = "getPlanComConfigCondition", desc = "列表条件全查(计委包表配置页)")
    @Override
    public CommonResult getPlanComConfigCondition(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getPlanComConfigCondition(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("列表条件全查(计委包表配置页)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询Object最后更新人筛选");
        }
    }
    @JalorOperation(code = "getCoaConfigCondition", desc = "列表条件全查(COA表配置页)")
    @Override
    public CommonResult getCoaConfigCondition(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getCoaConfigCondition(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("列表条件全查(COA表配置页)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "列表条件全查(COA表配置页)");
        }
    }
    @JalorOperation(code = "getPlanComSopInfo", desc = "计委包查询S&OP信息列表(计委包表配置页)")
    @Override
    public CommonResult getPlanComSopInfo(LabelConfigQueryRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getPlanComSopInfo(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("计委包查询S&OP信息列表(计委包表配置页)：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "计委包查询S&OP信息列表(计委包表配置页)");
        }
    }

    @Audit(module="labelConfigController-importConfigObjectAllData",operation="importConfigObjectAllData",message="导入标签配置对象维表(全量导入)")
    @JalorOperation(code = "importConfigObjectAllData", desc = "导入标签配置对象维表(全量导入)")
    @Override
    public CommonResult importConfigObjectAllData(Attachment file, String roleId) {
        try {
            CommonResult<Object> status = CommUtils.getImportStatusResult(iFcstProfitsCommonService, CommonConstant.IMPORT_RESTRICTION);
            if (status != null) {
                return status;
            }
            return labelConfigurationService.importObjectFullData(file, roleId);
        } catch (CommonApplicationException e) {
            logger.error("导入异常：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    @Audit(module="labelConfigController-importCoaConfigAllData",operation="importCoaConfigAllData",message="导入标签配置Coa信息(全量导入)")
    @JalorOperation(code = "importCoaConfigAllData", desc = "导入标签配置Coa信息(全量导入)")
    @Override
    public CommonResult importCoaConfigAllData(Attachment file, String roleId) {
        try {
            CommonResult<Object> status = CommUtils.getImportStatusResult(iFcstProfitsCommonService, CommonConstant.IMPORT_RESTRICTION);
            if (status != null) {
                return status;
            }
            return labelConfigurationService.importCoaFullData(file, roleId);
        } catch (CommonApplicationException e) {
            logger.error("导入异常：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    @Audit(module="labelConfigController-importPlanComConfigAllData",operation="importPlanComConfigAllData",message="导入标签配置计委包信息(全量导入)")
    @JalorOperation(code = "importPlanComConfigAllData", desc = "导入标签配置计委包信息(全量导入)")
    @Override
    public CommonResult importPlanComConfigAllData(Attachment file, String roleId) {
        try {
            CommonResult<Object> status = CommUtils.getImportStatusResult(iFcstProfitsCommonService, CommonConstant.IMPORT_RESTRICTION);
            if (status != null) {
                return status;
            }
            return labelConfigurationService.importPlanComFullData(file, roleId);
        } catch (CommonApplicationException e) {
            logger.error("导入异常：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    @Audit(module="labelConfigController-importIctConfigAllData",operation="importIctConfigAllData",message="导入标签配置Ict信息(全量导入)")
    @JalorOperation(code = "importIctConfigAllData", desc = "导入标签配置Ict信息(全量导入)")
    @Override
    public CommonResult importIctConfigAllData(Attachment file, String roleId) {
        try {
            CommonResult<Object> status = CommUtils.getImportStatusResult(iFcstProfitsCommonService, CommonConstant.IMPORT_RESTRICTION);
            if (status != null) {
                return status;
            }
            return labelConfigurationService.importIctFullData(file, roleId);
        } catch (CommonApplicationException e) {
            logger.error("导入异常：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    private void vailArrayParamLimitSize(LabelConfigQueryRequest request) throws CommonApplicationException {
        checkParmaSize(request);
        if (!CollectionUtil.isNullOrEmpty(request.getPlanComLv3()) && request.getPlanComLv3().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getArticulationFlag()) && request.getArticulationFlag().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getLastUpdatedBy()) && request.getLastUpdatedBy().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getCoaCode()) && request.getCoaCode().length > 5000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getPlanComLv1()) && request.getPlanComLv1().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getPlanComLv2()) && request.getPlanComLv2().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getPlanComLv3()) && request.getPlanComLv3().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getArticulationFlag()) && request.getArticulationFlag().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
    }

    private void checkParmaSize(LabelConfigQueryRequest request) throws CommonApplicationException {
        if (!CollectionUtil.isNullOrEmpty(request.getLv1Name()) && request.getLv1Name().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getLv2Name()) && request.getLv2Name().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getLv3Name()) && request.getLv3Name().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getL1Name()) && request.getL1Name().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getL2Name()) && request.getL2Name().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getL3Name()) && request.getL3Name().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getBusiLv4()) && request.getBusiLv4().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getLv1Names()) && request.getLv1Names().size() > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
    }
}
