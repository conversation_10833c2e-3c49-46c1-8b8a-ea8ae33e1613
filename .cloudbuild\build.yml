---
version: 2.0

#构建环境
env:
  label: BPIT_Build_Default
  cache:
    - type: maven
      path: ./mvn_repository

#构建参数，构建命令可通过环境变量使用
params:
  - name: product
    value: cloudbuild2.0

#构建步骤
steps:
  PRE_BUILD: # 构建准备:下载代码
    - checkout
  BUILD: # 构建执行
    - build_execute:
        # 构建命令，如sh build.sh 或 make
        command: sh opt.fcst.profits.parent/build.sh
        check:
          mode: sync
          buildcheck:
            project_dir: opt.fcst.profits.parent  # 工程根目录
            exclude_dir: opt.fcst.profits.parent/opt.fcst.profits.impl/src/main/resources/template/;opt.fcst.profits.parent/opt.fcst.profits.impl/target/classes/template/;
            exclude_cfg_file: opt.fcst.profits.parent/exclude_file.cfg
          sourcecheck:  # 构建来源检查，非必配项，不配时不检查
            - project_type: maven  # 必配项。当前只支持检查maven仓库来源
              project_dir: ./opt.fcst.profits.parent  # 工程根目录，必配项。如maven为pom文件所在目录
          dependency:
            #必配项，选择对应的编译类型
            - tool_type: maven
              #必配项。如maven为pom文件所在目录
              project_dir: ./opt.fcst.profits.parent
              # 非必配项，默认为false。配置为true时，不进行插件依赖检查，当前插件检查依赖只支持Maven类型
              settings_xml: $WORKSPACE/opt.fcst.profits.parent/settings.xml
              skip_plugin: false
  POST_BUILD:  # 构建后
    - sh:
        command: | #数字签名配置
          signclient "opt.fcst.profits.parent/opt.fcst.profits.start/target/fcst_profits_service_*.jar"  #不同签名包之间用分号分隔
    - artget: #从云龙上传构建cloudArtifact仓库 需要云龙流水线传入serviceId,serviceName,isRelease参数
        artifact_type: cloudartifact  # 仓库类型
        action: push #选填。默认值为push,当上传包数量超过10个时，必填。
        file_path: "package,opt.fcst.profits.parent/opt.fcst.profits.start/target/fcst_profits_service_*.jar;cms,opt.fcst.profits.parent/opt.fcst.profits.start/target/fcst_profits_service_*.jar.cms"  #上传包的路径是相对路径，相对于workspace
        version_output_path: .
    - version_set # 记录version_set
    #如果上传CMC还在使用upload_cloud_artifact需要修改替换为artget的方式（注意：如果换成artget上传包需要把原upload_cloud_artifact部分去掉）。
    - compile_report:
        complier: javac
        rules:
          - 'error /.*\[ERROR\].*/'
          - 'warning /.*\[WARNING\].*(?!Checksum validation failed).*\n.*(?!No processor claimed any of these annotations).*\n.*(?!The metadata).*\n.*(?!is invalid: input contained no data).*\n.*(?!Maven will be executed).*\n.*(?!Ignoring unrecognized).*\n.*(?!No processor claimed any of these annotations).*\n.*(?!Failed to read extensions descriptor).*/'
    - version_set #记录云龙version_set(云龙部署专用)