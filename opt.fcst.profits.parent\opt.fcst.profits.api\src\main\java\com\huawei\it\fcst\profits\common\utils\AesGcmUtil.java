/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.utils;

import com.huawei.it.jalor5.core.util.StringUtil;
import com.sun.javafx.binding.StringFormatter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * java 端加密数据
 *
 * <AUTHOR>
 * @since 2023/3/20
 */
@Slf4j
public class AesGcmUtil {

    private static final int DEF_RANDOM = 32;

    /**
     * 实例化随机数对象
     */
    private static volatile SecureRandom RANDOM;

    static {
        try {
            RANDOM = SecureRandom.getInstanceStrong();
        } catch (NoSuchAlgorithmException ex) {
            log.error("get instance SecureRandom error {0}", ex.getMessage());
        }
    }

    /**
     * 获取安全随机数
     *
     * @return int 值
     */
    public static int getSecureRandom() {
        // 随机数初始失败
        if (RANDOM == null) {
            try {
                RANDOM = SecureRandom.getInstanceStrong();
            } catch (NoSuchAlgorithmException ex) {
                log.error("get instance SecureRandom error {0}", ex.getMessage());
                return DEF_RANDOM;
            }
        }
        int num = RANDOM.nextInt(65535);
        if (num < 10) {
            num = DEF_RANDOM;
        }
        return num;
    }

    /**
     * 扰动函数
     *
     * @param random 安全随机数
     * @param value  value
     * @return 扰动后的值
     */
    public static String dataShuffle(int random, String value) {
        BigDecimal temp;
        if (StringUtil.isNullOrEmpty(value)) {
            temp = BigDecimal.ZERO;
        } else {
            temp = new BigDecimal(value);
        }
        return temp.divide(new BigDecimal(random), 10, 5).toPlainString();
    }

    /**
     * 扰动函数
     *
     * @param random 安全随机数
     * @param value  value
     * @return 扰动后的值
     */
    public static BigDecimal dataShuffle(int random, BigDecimal value) {
        if (value == null) {
            value = BigDecimal.ZERO;
        }
        return value.divide(new BigDecimal(random), 10, 5);
    }

    /**
     * 扰动函数
     *
     * @param value value
     * @return 扰动后的值
     */
    public static String dataShuffle(String value) {
        BigDecimal curr;
        if (StringUtil.isNullOrEmpty(value)) {
            curr = BigDecimal.ZERO;
        } else {
            curr = new BigDecimal(value);
        }
        BigDecimal bigDecimal = curr.multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
        BigDecimal add = bigDecimal.add(BigDecimal.ONE);
        BigDecimal subtract;
        if (BigDecimal.ONE.compareTo(bigDecimal) == -1) {
            subtract = bigDecimal.subtract(BigDecimal.ONE);
        } else {
            subtract = BigDecimal.ZERO;
        }
        return StringFormatter.format("%s,%s", subtract, add).getValue();
    }
}
