/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.common.utils;

import java.io.IOException;
import java.math.BigDecimal;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

/**
 * 处理BigDecimal序列化前段展示问题，例如：0.********** - 0E-10
 * <AUTHOR>
 * @since 2023/03/02.
 */
public class FcstJsonFormatVisitorWrapper extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal value, JsonGenerator jgen, SerializerProvider provider) throws IOException {
        // 设置转换后的结果
        jgen.writeString(value.toPlainString());
    }
}