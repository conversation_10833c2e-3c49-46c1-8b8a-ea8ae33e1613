/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * The Entity of LabelCountVO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-17 19:38:39
 */
@Getter
@Setter
public class LabelCountVO {
    /**
     * 产业
     **/
    private String lv1Name;

    private Integer totalNumber;

    private Integer modifyNumber;

    private BigDecimal modifyRate;

    /**
     * 版本
     **/
    private String periodId;

    private List<LabelCountVO> labelRecountVOList;

}
