/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.request;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * The Entity of LabelConfigRequest
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-12 11:07:53
 */
@Getter
@Setter
public class LabelConfigRequest extends PageRequest {
    /**
     * 会计期
     **/
    private Long periodId;

    /**
     * LV1编码
     **/
    private String[] lv1Name;

    /**
     * L1名称
     **/
    private String[] l1Name;

    /**
     * L2名称
     **/
    private String[] l2Name;

    /**
     * L3名称
     **/
    private String[] l3Name;

    private String l1Val;

    private String l2Val;

    private String l3Val;

    /**
     * 数据类型（Manual 历史、AI 新增）
     **/
    private String[] dataType;

    private List<String> lv1List;

    private List<String> l1List;

    private List<String> l2List;

    private List<String> l3List;

    private List<String> dataTypeList;

    private String status;

    private Integer roleId;

    private Long lastUpdatedBy;

    private List<Long> lastUpdatedBys;

    private Long createdBy;

    // type: config(标签配置)、review(标签审视)
    private String queryType;

    private List<String> statusList;
}
