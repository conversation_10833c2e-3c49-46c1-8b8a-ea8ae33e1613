/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The Entity of SpartInfoVo
 *
 * <AUTHOR>
 * @since 2022-10-18 09:26:09
 */
@Getter
@Setter
public class SpartInfoVo {

    /**
     * spartCode
     **/
    private String spartCode;

    /**
     * spartDesc
     **/
    private String spartDesc;

    /**
     * revPercent
     **/
    private BigDecimal revPercent;

    /**
     * 成本占比 = Spart对价前成本额/L2对价前成本金额 ,涉密字段，通过kms 加密后
     **/
    private String costPercent;
}
