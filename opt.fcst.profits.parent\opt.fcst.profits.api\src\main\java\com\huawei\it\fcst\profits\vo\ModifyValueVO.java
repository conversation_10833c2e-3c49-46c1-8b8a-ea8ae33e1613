/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The Entity of ModifyValueVO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-17 19:38:39
 */
@Getter
@Setter
public class ModifyValueVO {
    /**
     * 产业
     **/
    @JsonProperty("lv1_name")
    private String lv1Name;

    /**
     * l1名称
     **/
    @JsonProperty("l1_name")
    private String l1Name;

    /**
     * 版本
     **/
    @JsonProperty("period_id")
    private Long periodId;

    @JsonProperty("item_code")
    private String itemCode;

    /**
     * L1系数
     **/
    @JsonProperty("l1_coefficient")
    private BigDecimal l1Coefficient;

    /**
     * L2系数
     **/
    @JsonProperty("l2_coefficient")
    private BigDecimal l2Coefficient;

    /**
     * L3系数
     **/
    @JsonProperty("l3_coefficient")
    private BigDecimal l3Coefficient;

    /**
     * l1名称
     **/
    @JsonProperty("l2_name")
    private String l2Name;

    /**
     * l1名称
     **/
    @JsonProperty("l2_name")
    private String l3Name;

}
