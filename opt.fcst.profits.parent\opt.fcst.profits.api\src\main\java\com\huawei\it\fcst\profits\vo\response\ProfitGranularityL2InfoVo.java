/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.huawei.it.fcst.profits.common.utils.FcstJsonFormatVisitorWrapper;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The Entity of ProfitGranularityL2InfoVo
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:26:09
 */
@Getter
@Setter
public class ProfitGranularityL2InfoVo {

    /**
     * 重量级团队LV1编码
     **/
    private String lv1Code;

    /**
     * BG编码
     **/
    private String bgCode;

    /**
     * L1名称
     **/
    private String l1Name;

    /**
     * L2名称
     **/
    private String l2Name;

    /**
     * 重量级团队LV1描述
     **/
    private String lv1Name;

    /**
     * BG名称
     **/
    private String bgName;

    /**
     * 场景
     **/
    private String targetPeriod;

    /**
     * 币种
     **/
    private String currency;

    /**
     * 会计期
     **/
    private Long periodId;

    /**
     * 重量级团队LV2编码
     **/
    private String lv2Code;

    /**
     * 重量级团队LV2名称
     **/
    private String lv2Name;

    /**
     * 单位成本
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitCost;

    /**
     * 单位价格
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitPrice;

    /**
     * 收入占比
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal revPercent;

    /**
     * 制毛率
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal mgpRatio;

    /**
     * 设备收入本年累计
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal equipCostConsBefore;

    /**
     * 设备收入本年累计
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal equipRevConsBefore;

    /**
     * 发货量（S&NOP本年)
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal planQty;

    /**
     * 发货量
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal shipQty;

    /**
     * 收入量
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal spartQty;

    private String overseaDesc;

    // phaseDate新增
    private String phaseDate;

    private String groupKey;

}
