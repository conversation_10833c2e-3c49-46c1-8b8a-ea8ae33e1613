﻿<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
  http://www.springframework.org/schema/beans/spring-beans.xsd"
	default-lazy-init="true">

	<beans>
		<!-- 业务服务扫描rpc服务 -->
		<import resource="classpath*:/config/jalor.rpc.proxy.xml"/>
	    <import resource="classpath*:/config/jalor.rpc.stub.publish.xml"/>
	    <import resource="classpath*:/config/*.overrides.xml" />
	</beans>
</beans>