#!/usr/bin/env sh
export CI_ROOT=/data01/app
export CODEDEX_PATH=$CI_ROOT/CodeDEX_V3  # 标准目录
export PROJECT_SOURCE=#SRC_ROOT#  # 源码目录
export INTER_DIR_SET=#inter_dir#  # 中间文件生成目录
export POM_DIR=$PROJECT_SOURCE/opt.fcst.profits.parent  # pom文件所在路径-修改成自己项目的目录
#########################################################################

##########################################################################


function compile_env() {  # 如果编译命令有相关环境设置请修改此函数
    echo "compile env set"
}

function use_user_set() {  # 如需扫描请设置该函数
    compile_env
    export coverity_version="2019.03"  # coverity版本设置
    export fortify_version="17.20"  # fortify版本设置
    export compile_command="mvn clean package -U -Dmaven.test.skip=true -P!dev,online"  # 编译命令
    export PROJECT_NAME=#PROJECT_NAME#  # codedex上的工程名
    export isJDK11=""  # 如果是JDK11请置为非空字符串即可，如"yes"

}

function use_env() {  # set env
    export FORTIFY_HOME=$CODEDEX_PATH/tool/tools/fortify_${fortify_version}
    export COVERITY_HOME=$CODEDEX_PATH/tool/tools/coverity_${coverity_version}
    export SecMissile_HOME=$CODEDEX_PATH/tool/tools/secmissile_19.10/
    export CODEMARS_HOME=$CODEDEX_PATH/tool/tools/codemars_Newest
    export ZA_HOME=$CODEDEX_PATH/tool/7za/Linux
    export PATH=$FORTIFY_HOME/bin:$COVERITY_HOME/bin:$CODEMARS_HOME/bin:$PATH
    export FORTIFY_BUILD_ID=fortify_mbs
    export language=java
    export inter_dir=$INTER_DIR_SET
    export OUT_DIR_S=$inter_dir
    export codemars_tmp_dir=$inter_dir/codemars_tmp
    export cov_tmp_dir=$inter_dir/cov_tmp
    export for_tmp_dir=$inter_dir/for_tmp
    export project_root=$PROJECT_SOURCE
    export SOURCE_DIR_S=$project_root
    rm -rf $inter_dir
    mkdir -p $inter_dir
}

function use_config() {  # config tools

    # install mvn plugin
    mvn install:install-file -Dfile=$CODEDEX_PATH/tool/lib/compiler-maven-plugin-1.0.jar -DgroupId=com.huawei.codedex -DartifactId=compiler-maven-plugin -Dversion=1.0 -Dpackaging=maven-plugin

    if [[ ! -n $exclude_dir ]];then
        echo "98kar unset exclude dir"
    else
        export coverity_exclude="-Dcoverity.sca.Exclude=${exclude_dir}"
        export fortify_exclude="-Dfortify.sca.Exclude=${exclude_dir}"
        export codemars_exclude="${exclude_dir}"
    fi

    if [[ ! -n $isJDK11 ]];then
        echo "98kar JDK is not 11"
    else
        export source_version="-Dfortify.sca.SourceVersion=9"
    fi

}

function use_compile() {  # compile command
    cd $POM_DIR
    ${compile_command}
}

function use_coverity() { # coverity
    cd $POM_DIR
    mvn com.huawei.codedex:compiler-maven-plugin:1.0:coverityBuild -Dcoverity.sca.Interdirectory=$cov_tmp_dir ${coverity_exclude}
    cd $cov_tmp_dir
    $ZA_HOME/7za a -tzip coverity.zip * -r
    mv coverity.zip "$inter_dir"
}

function use_fortify() {  # fortify
    cd $POM_DIR
    mvn com.huawei.codedex:compiler-maven-plugin:1.0:fortifyBuild -Dfortify.sca.ProjectRoot=$for_tmp_dir -Dfortify.sca.BuildId=$FORTIFY_BUILD_ID ${source_version} ${fortify_exclude}
    cd $inter_dir
    sourceanalyzer -b $FORTIFY_BUILD_ID -Dcom.fortify.sca.ProjectRoot=$for_tmp_dir -export-build-session $FORTIFY_BUILD_ID.mbs
    cd $inter_dir
    ${ZA_HOME}/7za a -tzip fortify.zip $FORTIFY_BUILD_ID.mbs
    # mv fortify.zip "$inter_dir"
}

function use_codemars() {  # codemars
    cd $CODEMARS_HOME
    sh CodeMars.sh -j -source $project_root -output $codemars_tmp_dir/CodeMars.json -excludePath ${codemars_exclude}
    cd $codemars_tmp_dir
    zip CodeMars.zip CodeMars.json
    mv CodeMars.zip $inter_dir
}

function use_secmissile() {   # secmissile
    cd $SecMissile_HOME
    sh SecMissile.sh "projectname=$PROJECT_NAME&&sourcepath=$SOURCE_DIR_S&&indexpackagepath=$OUT_DIR_S&&ipaddress=127.0.0.1&&multisourcepath=&&pythonfiledir=$SecMissile_HOME&&tool=hi"
}

function main() {  # 功能的选取请设置
    use_user_set
    use_env
    use_config
    use_codemars
    use_compile
    use_fortify
    use_secmissile
}

main