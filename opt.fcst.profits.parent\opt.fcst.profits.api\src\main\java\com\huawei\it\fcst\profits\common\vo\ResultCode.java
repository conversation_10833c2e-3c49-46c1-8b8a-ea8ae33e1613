/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.vo;

/**
 * <AUTHOR>
 * @since 2020年11月23日
 */
public enum ResultCode implements IErrorCode {
    SUCCESS(200, "操作成功"),
    FAILED(500, "操作失败"),
    CHECK_TIP(233, "操作失败"),
    FAILED_TIP(5001, "操作失败"),
    VALIDATE_FAILED(404, "服务不存在"),
    REQUIRE_FAILED(402, "服务验证不通过"),
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    FORBIDDEN(403, "没有相关权限");
    private long code;

    private String message;

    private ResultCode(
        long code, String message) {
        this.code = code;
        this.message = message;
    }

    private ResultCode(String message) {
        this.code = 233;
        this.message = message;
    }

    public long getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
