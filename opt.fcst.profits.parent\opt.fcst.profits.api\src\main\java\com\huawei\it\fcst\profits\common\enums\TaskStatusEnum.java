/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;

/**
 * 任务状态
 *
 * <AUTHOR>
 * @since 202404
 */
public enum TaskStatusEnum {
    INIT("初始化", "TASK_INIT"),
    TASK_SUCCESS("任务成功", "TASK_SUCCESS"),
    TASK_FAIL("任务失败", "TASK_FAIL");

    @Getter
    private String desc;

    @Getter
    private String value;

    TaskStatusEnum(String desc, String value) {
        this.desc = desc;
        this.value = value;
    }
}

