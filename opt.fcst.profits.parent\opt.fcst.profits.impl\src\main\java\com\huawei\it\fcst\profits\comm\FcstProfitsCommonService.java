/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.comm;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

import javax.inject.Named;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.it.fcst.profits.common.utils.JsonUtils;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.service.IFcstProfitsCommonService;
import com.huawei.it.fcst.profits.vo.request.LabelConfigRequest;
import com.huawei.it.jalor5.registry.RegistryVO;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;

import cn.hutool.core.date.format.FastDateFormat;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年04月12日
 */
@Slf4j
@Named("fcstProfitsCommonService")
public class FcstProfitsCommonService implements IFcstProfitsCommonService {

    private static final String LABEL_AUDIT_TIME_CONTROL = "App.Config.Profits.LabelAudit.LabelAuditTimeControl";

    /**
     * 标签审视时间开关配置
     */
    private static final String LABEL_AUDIT_TIME_SWITCH = "App.Config.Profits.LabelAudit.LabelAuditSwitch";
    private static final Map<String, Integer> INIT_DATE = new HashMap<>();

    static {
        INIT_DATE.put("startDay", 7);
        INIT_DATE.put("endDay", 20);
    }

    @Autowired
    private IRegistryQueryService iRegistryQueryService;

    @Override
    public Map<String, Object> validateSumbit(LabelConfigRequest requestVO) {
        Map<String, Object> result = new HashMap<>();
        if (null != requestVO && !StringUtils.equals(String.valueOf(requestVO.getPeriodId()), TimeUtils.getCurYear())) {
            result.put("isSubmit", Boolean.FALSE);
            result.put("desc", "仅支持修改最新版本");
            return result;
        }
        // 开关打开了。控制的时间范围就不受控制，不展示说明
        RegistryVO registrySwitch = iRegistryQueryService.findRegistryByPathNoAssert(LABEL_AUDIT_TIME_SWITCH, true);
        if (registrySwitch != null && StringUtils.equalsIgnoreCase("open", registrySwitch.getValue())) {
            result.put("isSubmit", Boolean.TRUE);
            result.put("desc", "");
        } else {
            // 获取时间区间配置
            RegistryVO registryVO = iRegistryQueryService.findRegistryByPathNoAssert(LABEL_AUDIT_TIME_CONTROL, true);
            Map<String, Integer> map = stringToInteger(registryVO);
            result.put("isSubmit", validateSumbitFlag(map));
            result.put("desc", String.format(Locale.ROOT, "每月%s号00:00:00 ~ %s号(中午)12:00:00支持提交数据", map.get("startDay"), map.get("endDay")));
            result.putAll(map);
        }
        return result;
    }

    @Override
    public Map<String, Object> validateSumbit() {
        return validateSumbit(null);
    }


    private Map<String, Integer> stringToInteger(RegistryVO registryVO) {
        // 没有配置数据字典或者失效数据给默认值7-21号
        if (Objects.isNull(registryVO) || StringUtils.isEmpty(registryVO.getValue())) {
            return INIT_DATE;
        }
        Map<String, Integer> date = new HashMap<>();
        try {
            Map<String, Object> dayTime = JsonUtils.stringToObject(registryVO.getValue(), Map.class);
            for (Map.Entry<String, Object> entry : dayTime.entrySet()) {
                date.put(entry.getKey(), Integer.valueOf(String.valueOf(entry.getValue())));
            }
            return date;
        } catch (NumberFormatException | IOException exception) {
            log.error("Data dictionary configuration error :{}", registryVO.getValue());
            return INIT_DATE;
        }
    }

    private boolean validateSumbitFlag(Map<String, Integer> map) {
        int day = TimeUtils.getDayOfMonth();
        int startDay = map.get("startDay");
        int endDay = map.get("endDay");
        Long hHmmss = Long.valueOf(FastDateFormat.getInstance("HHmmss").format(new Date()));
        if (day < startDay || day > endDay) {
            return false;
        } else if (day < endDay || (day == endDay && (hHmmss >= Long.valueOf(0) && hHmmss <= Long.valueOf(120000)))) {
            return true;
        } else {
            return false;
        }
    }
}
