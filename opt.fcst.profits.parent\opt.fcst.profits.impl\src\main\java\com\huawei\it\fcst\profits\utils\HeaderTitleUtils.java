/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.utils;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.LabelConfigEnum;
import com.huawei.it.fcst.profits.common.vo.TableHeaderVo;

import java.util.ArrayList;
import java.util.List;

public class HeaderTitleUtils {

    // COA表头构造
    private static final String COA_CONFIG_HEADER_TITLE_ITEM1 = "产业";
    private static final String COA_CONFIG_HEADER_TITLE_ITEM2 = "L1名称";
    private static final String COA_CONFIG_HEADER_TITLE_ITEM3 = "L2名称";
    private static final String COA_CONFIG_HEADER_TITLE_ITEM4 = "产品编码";
    private static final String COA_CONFIG_HEADER_TITLE_ITEM7 = "最后更新人";
    private static final String COA_CONFIG_HEADER_TITLE_ITEM8 = "最后更新时间";
    private static final String COA_CONFIG_HEADER_TITLE_ITEM9 = "状态";

    private static final String COA_CONFIG_HEADER_FIELD_ITEM1 = "lv1Name";
    private static final String COA_CONFIG_HEADER_FIELD_ITEM2 = "l1Name";
    private static final String COA_CONFIG_HEADER_FIELD_ITEM3 = "l2Name";
    private static final String COA_CONFIG_HEADER_FIELD_ITEM4 = "coaCode";
    private static final String COA_CONFIG_HEADER_FIELD_ITEM7 = "lastUpdatedByName";
    private static final String COA_CONFIG_HEADER_FIELD_ITEM8 = "lastUpdateDate";
    private static final String COA_CONFIG_HEADER_FIELD_ITEM9 = "status";

    // 集团分析师预测结果表头
    private static final String GROUP_ANALYST_HEADER_TITLE_ITEM1 = "版本";
    private static final String GROUP_ANALYST_HEADER_TITLE_ITEM2 = "LV1名称";
    private static final String GROUP_ANALYST_HEADER_TITLE_ITEM3 = "LV2名称";
    private static final String GROUP_ANALYST_HEADER_TITLE_ITEM4 = "设备收入";
    private static final String GROUP_ANALYST_HEADER_TITLE_ITEM5 = "制毛率";
    private static final String GROUP_ANALYST_HEADER_TITLE_ITEM6 = "预测步长";
    private static final String GROUP_ANALYST_HEADER_TITLE_ITEM7 = "最后更新人";
    private static final String GROUP_ANALYST_HEADER_TITLE_ITEM8 = "最后更新时间";

    private static final String GROUP_ANALYST_HEADER_FIELD_ITEM1 = "versionCode";
    private static final String GROUP_ANALYST_HEADER_FIELD_ITEM2 = "lv1Name";
    private static final String GROUP_ANALYST_HEADER_FIELD_ITEM3 = "lv2Name";
    private static final String GROUP_ANALYST_HEADER_FIELD_ITEM4 = "equipRevAmt";
    private static final String GROUP_ANALYST_HEADER_FIELD_ITEM5 = "mgpRatio";
    private static final String GROUP_ANALYST_HEADER_FIELD_ITEM6 = "targetDesc";
    private static final String GROUP_ANALYST_HEADER_FIELD_ITEM7 = "lastUpdatedByName";
    private static final String GROUP_ANALYST_HEADER_FIELD_ITEM8 = "lastUpdateDate";

    private static final String PLAN_COM_CONFIG_HEADER_TITLE_ITEM0 = "产业";
    private static final String PLAN_COM_CONFIG_HEADER_TITLE_ITEM1 = "一级计委包";
    private static final String PLAN_COM_CONFIG_HEADER_TITLE_ITEM2 = "二级计委包";
    private static final String PLAN_COM_CONFIG_HEADER_TITLE_ITEM3 = "三级计委包";
    private static final String PLAN_COM_CONFIG_HEADER_TITLE_ITEM4 = "四级业务包";
    private static final String PLAN_COM_CONFIG_HEADER_TITLE_ITEM5 = "L1名称";
    private static final String PLAN_COM_CONFIG_HEADER_TITLE_ITEM6 = "L2名称";
    private static final String PLAN_COM_CONFIG_HEADER_TITLE_ITEM7 = "L2系数";
    private static final String PLAN_COM_CONFIG_HEADER_TITLE_ITEM10 = "最后更新人";
    private static final String PLAN_COM_CONFIG_HEADER_TITLE_ITEM11 = "最后更新时间";

    private static final String PLAN_COM_CONFIG_HEADER_FIELD_ITEM0 = "lv1Name";
    private static final String PLAN_COM_CONFIG_HEADER_FIELD_ITEM1 = "planComLv1";
    private static final String PLAN_COM_CONFIG_HEADER_FIELD_ITEM2 = "planComLv2";
    private static final String PLAN_COM_CONFIG_HEADER_FIELD_ITEM3 = "planComLv3";
    private static final String PLAN_COM_CONFIG_HEADER_FIELD_ITEM4 = "busiLv4";
    private static final String PLAN_COM_CONFIG_HEADER_FIELD_ITEM5 = "l1Name";
    private static final String PLAN_COM_CONFIG_HEADER_FIELD_ITEM6 = "l2Name";
    private static final String PLAN_COM_CONFIG_HEADER_FIELD_ITEM7 = "l2Coefficient";
    private static final String PLAN_COM_CONFIG_HEADER_FIELD_ITEM10 = "lastUpdatedByName";
    private static final String PLAN_COM_CONFIG_HEADER_FIELD_ITEM11 = "lastUpdateDate";

    private static final String ICT_CONFIG_HEADER_TITLE_ITEM2 = "重量级团队LV1中文名";
    private static final String ICT_CONFIG_HEADER_TITLE_ITEM4 = "重量级团队LV2中文名";
    private static final String ICT_CONFIG_HEADER_TITLE_ITEM6 = "重量级团队LV3中文名";
    private static final String ICT_CONFIG_HEADER_TITLE_ITEM7 = "L1名称";
    private static final String ICT_CONFIG_HEADER_TITLE_ITEM8 = "预测场景";
    private static final String ICT_CONFIG_HEADER_TITLE_ITEM12 = "最后更新人";
    private static final String ICT_CONFIG_HEADER_TITLE_ITEM13 = "最后更新时间";
    private static final String ICT_CONFIG_HEADER_TITLE_ITEM14 = "分析场景";

    private static final String ICT_CONFIG_HEADER_FIELD_ITEM2 = "lv1Name";
    private static final String ICT_CONFIG_HEADER_FIELD_ITEM4 = "lv2Name";
    private static final String ICT_CONFIG_HEADER_FIELD_ITEM6 = "lv3Name";
    private static final String ICT_CONFIG_HEADER_FIELD_ITEM7 = "l1Name";
    private static final String ICT_CONFIG_HEADER_FIELD_ITEM8 = "articulationFlag";
    private static final String ICT_CONFIG_HEADER_FIELD_ITEM12 = "lastUpdatedByName";
    private static final String ICT_CONFIG_HEADER_FIELD_ITEM13 = "lastUpdateDate";
    private static final String ICT_CONFIG_HEADER_FIELD_ITEM14 = "analysisFlag";

    private static final String OBJECT_CONFIG_HEADER_TITLE_ITEM1 = "产业";
    private static final String OBJECT_CONFIG_HEADER_TITLE_ITEM2 = "L1名称";
    private static final String OBJECT_CONFIG_HEADER_TITLE_ITEM3 = "L2名称";
    private static final String OBJECT_CONFIG_HEADER_TITLE_ITEM4 = "L3名称";
    private static final String OBJECT_CONFIG_HEADER_TITLE_ITEM7 = "最后更新人";
    private static final String OBJECT_CONFIG_HEADER_TITLE_ITEM8 = "最后更新时间";

    private static final String OBJECT_CONFIG_HEADER_FIELD_ITEM1 = "lv1Name";
    private static final String OBJECT_CONFIG_HEADER_FIELD_ITEM2 = "l1Name";
    private static final String OBJECT_CONFIG_HEADER_FIELD_ITEM3 = "l2Name";
    private static final String OBJECT_CONFIG_HEADER_FIELD_ITEM4 = "l3Name";
    private static final String OBJECT_CONFIG_HEADER_FIELD_ITEM7 = "lastUpdatedByName";
    private static final String OBJECT_CONFIG_HEADER_FIELD_ITEM8 = "lastUpdateDate";

    private static final String ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM0 = "重量级团队LV0";
    private static final String ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM0 = "lv0Name";

    private static final String ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM1 = "重量级团队LV1";
    private static final String ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM1 = "lv1Name";
    private static final String ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM2 = "重量级团队LV2";
    private static final String ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM2 = "lv2Name";
    private static final String ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM3 = "重量级团队LV3";
    private static final String ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM3 = "lv3Name";
    private static final String ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM4 = "L1名称";
    private static final String ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM4 = "l1Name";
    private static final String ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM5 = "预测场景";
    private static final String ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM5 = "articulationFlag";
    private static final String ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM6 = "最后更新人";
    private static final String ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM6 = "lastUpdatedByName";
    private static final String ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM7 = "最后更新时间";
    private static final String ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM7 = "lastUpdateDate";

    private static final String COA_CONFIG_MONITOR_HEADER_TITLE_ITEM1 = "重量级团队LV1";
    private static final String COA_CONFIG_MONITOR_HEADER_FIELD_ITEM1 = "lv1Name";
    private static final String COA_CONFIG_MONITOR_HEADER_TITLE_ITEM2 = "重量级团队LV2";
    private static final String COA_CONFIG_MONITOR_HEADER_FIELD_ITEM2 = "lv2Name";
    private static final String COA_CONFIG_MONITOR_HEADER_TITLE_ITEM3 = "重量级团队LV3";
    private static final String COA_CONFIG_MONITOR_HEADER_FIELD_ITEM3 = "lv3Name";

    private static final String COA_CONFIG_MONITOR_HEADER_TITLE_ITEM4 = "L1名称";
    private static final String COA_CONFIG_MONITOR_HEADER_FIELD_ITEM4 = "l1Name";
    private static final String COA_CONFIG_MONITOR_HEADER_TITLE_ITEM5 = "L2名称";
    private static final String COA_CONFIG_MONITOR_HEADER_FIELD_ITEM5 = "l2Name";
    private static final String COA_CONFIG_MONITOR_HEADER_TITLE_ITEM6 = "产品编码";
    private static final String COA_CONFIG_MONITOR_HEADER_FIELD_ITEM6 = "coaCode";
    private static final String COA_CONFIG_MONITOR_HEADER_TITLE_ITEM7 = "产品中文名";
    private static final String COA_CONFIG_MONITOR_HEADER_FIELD_ITEM7 = "prodName";
    private static final String COA_CONFIG_MONITOR_HEADER_TITLE_ITEM8 = "最后更新人";
    private static final String COA_CONFIG_MONITOR_HEADER_FIELD_ITEM8 = "lastUpdatedByName";
    private static final String COA_CONFIG_MONITOR_HEADER_TITLE_ITEM9 = "最后更新时间";
    private static final String COA_CONFIG_MONITOR_HEADER_FIELD_ITEM9 = "lastUpdateDate";

    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM0 = "重量级团队LV1";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM0 = "lv1Name";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM1 = "重量级团队LV2";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM1 = "lv2Name";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM2 = "重量级团队LV3";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM2 = "lv3Name";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM3 = "一级计委包";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM3 = "planComLv1";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM4 = "二级计委包";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM4 = "planComLv2";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM5 = "三级计委包";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM5 = "planComLv3";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM6 = "四级业务包";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM6 = "busiLv4";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM7 = "L1名称";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM7 = "l1Name";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM8 = "L2名称";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM8 = "l2Name";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM9 = "L2系数";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM9 = "l2Coefficient";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM10 = "最后更新人";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM10 = "lastUpdatedByName";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM11 = "最后更新时间";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM11 = "lastUpdateDate";

    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM12 = "区域";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM12 = "overseaDesc";

    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM13 = "S&OP期次";
    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM13 = "phaseDate";

    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM14 = "预测/预算";

    private static final String PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM14 = "phaseDateType";

    // 导入模板解析所需
    public static List<TableHeaderVo> creatImportHeaderList(String dataType) {
        List<TableHeaderVo> headerVoList = new ArrayList<>();
        if (dataType.equals(LabelConfigEnum.COA.getCode())) {
            setImportCoaList(headerVoList);
        } else if (dataType.equals(LabelConfigEnum.PLAN_COM.getCode())) {
            setImportPlanComList(headerVoList);
        } else if (dataType.equals(LabelConfigEnum.HOLISTIC_VIEW.getCode())) {
            setImportIctList(headerVoList);
        } else if (dataType.equals(LabelConfigEnum.CONFIG.getCode())) {
            setImportObjectList(headerVoList);
        }
        return headerVoList;
    }

    // 导出模板解析所需
    public static List<TableHeaderVo> creatErrorHeaderList(String dataType) {
        List<TableHeaderVo> headerVoList = new ArrayList<>();
        headerVoList.add(new TableHeaderVo().builder()
                .title("异常原因")
                .field("errorMsg")
                .build());
        headerVoList.addAll(creatImportHeaderList(dataType));
        return headerVoList;
    }

    // 个人中心-我的修改 展示内容控制
    public static List<TableHeaderVo> creatMyEditedDataHeaderList(String dataType) {
        List<TableHeaderVo> headerVoList = new ArrayList<>();
        if (dataType.equals(LabelConfigEnum.COA.getCode())) {
            setEditedCoaList(headerVoList);
        } else if (dataType.equals(LabelConfigEnum.PLAN_COM.getCode())) {
            setEditedPlanComList(headerVoList);
        } else if (dataType.equals(LabelConfigEnum.HOLISTIC_VIEW.getCode())) {
            setEditedIctList(headerVoList);
        } else if (dataType.equals(LabelConfigEnum.CONFIG.getCode())) {
            setEditedObjectList(headerVoList);
        } else if (dataType.equals(LabelConfigEnum.GROUP_ANALYST.getCode())) {
            setHeaderVoList(headerVoList);
        }
        return headerVoList;
    }

    private static void setEditedCoaList(List<TableHeaderVo> headerVoList) {
        TableHeaderVo headerVo = new TableHeaderVo(COA_CONFIG_HEADER_TITLE_ITEM1, COA_CONFIG_HEADER_FIELD_ITEM1, null);
        headerVoList.add(headerVo);
        headerVo = new TableHeaderVo(COA_CONFIG_HEADER_TITLE_ITEM2, COA_CONFIG_HEADER_FIELD_ITEM2, null);
        headerVoList.add(headerVo);
        headerVo = new TableHeaderVo(COA_CONFIG_HEADER_TITLE_ITEM3, COA_CONFIG_HEADER_FIELD_ITEM3, null);
        headerVoList.add(headerVo);
        headerVo = new TableHeaderVo(COA_CONFIG_HEADER_TITLE_ITEM4, COA_CONFIG_HEADER_FIELD_ITEM4, null);
        headerVoList.add(headerVo);
        headerVo = new TableHeaderVo(COA_CONFIG_HEADER_TITLE_ITEM7, COA_CONFIG_HEADER_FIELD_ITEM7, null);
        headerVoList.add(headerVo);
        headerVo = new TableHeaderVo(COA_CONFIG_HEADER_TITLE_ITEM8, COA_CONFIG_HEADER_FIELD_ITEM8, null);
        headerVoList.add(headerVo);
    }

    private static void setHeaderVoList(List<TableHeaderVo> headerVoList) {
        headerVoList.add(new TableHeaderVo(GROUP_ANALYST_HEADER_TITLE_ITEM1, GROUP_ANALYST_HEADER_FIELD_ITEM1, null));
        headerVoList.add(new TableHeaderVo(GROUP_ANALYST_HEADER_TITLE_ITEM2, GROUP_ANALYST_HEADER_FIELD_ITEM2, null));
        headerVoList.add(new TableHeaderVo(GROUP_ANALYST_HEADER_TITLE_ITEM3, GROUP_ANALYST_HEADER_FIELD_ITEM3, null));
        headerVoList.add(new TableHeaderVo(GROUP_ANALYST_HEADER_TITLE_ITEM4, GROUP_ANALYST_HEADER_FIELD_ITEM4, null));
        headerVoList.add(new TableHeaderVo(GROUP_ANALYST_HEADER_TITLE_ITEM5, GROUP_ANALYST_HEADER_FIELD_ITEM5, null));
        headerVoList.add(new TableHeaderVo(GROUP_ANALYST_HEADER_TITLE_ITEM6, GROUP_ANALYST_HEADER_FIELD_ITEM6, null));
        headerVoList.add(new TableHeaderVo(GROUP_ANALYST_HEADER_TITLE_ITEM7, GROUP_ANALYST_HEADER_FIELD_ITEM7, null));
        headerVoList.add(new TableHeaderVo(GROUP_ANALYST_HEADER_TITLE_ITEM8, GROUP_ANALYST_HEADER_FIELD_ITEM8, null));
    }

    private static void setEditedPlanComList(List<TableHeaderVo> headerVoList) {
        TableHeaderVo headerVo = new TableHeaderVo(PLAN_COM_CONFIG_HEADER_TITLE_ITEM0, PLAN_COM_CONFIG_HEADER_FIELD_ITEM0, null);
        headerVoList.add(headerVo);
        headerVoList.add(new TableHeaderVo(PLAN_COM_CONFIG_HEADER_TITLE_ITEM1, PLAN_COM_CONFIG_HEADER_FIELD_ITEM1, null));
        headerVo = new TableHeaderVo(PLAN_COM_CONFIG_HEADER_TITLE_ITEM2, PLAN_COM_CONFIG_HEADER_FIELD_ITEM2, null);
        headerVoList.add(headerVo);
        headerVoList.add(new TableHeaderVo(PLAN_COM_CONFIG_HEADER_TITLE_ITEM3, PLAN_COM_CONFIG_HEADER_FIELD_ITEM3, null));
        headerVoList.add(new TableHeaderVo(PLAN_COM_CONFIG_HEADER_TITLE_ITEM4, PLAN_COM_CONFIG_HEADER_FIELD_ITEM4, null));
        headerVoList.add(new TableHeaderVo(PLAN_COM_CONFIG_HEADER_TITLE_ITEM5, PLAN_COM_CONFIG_HEADER_FIELD_ITEM5, null));
        headerVoList.add(new TableHeaderVo(PLAN_COM_CONFIG_HEADER_TITLE_ITEM6, PLAN_COM_CONFIG_HEADER_FIELD_ITEM6, null));
        headerVoList.add(new TableHeaderVo(PLAN_COM_CONFIG_HEADER_TITLE_ITEM7, PLAN_COM_CONFIG_HEADER_FIELD_ITEM7, null));
        headerVo = new TableHeaderVo(PLAN_COM_CONFIG_HEADER_TITLE_ITEM10, PLAN_COM_CONFIG_HEADER_FIELD_ITEM10, null);
        headerVoList.add(headerVo);
        headerVo = new TableHeaderVo(PLAN_COM_CONFIG_HEADER_TITLE_ITEM11, PLAN_COM_CONFIG_HEADER_FIELD_ITEM11, null);
        headerVoList.add(headerVo);
    }

    private static void setEditedIctList(List<TableHeaderVo> headerVoList) {
        TableHeaderVo headerVo = new TableHeaderVo(ICT_CONFIG_HEADER_TITLE_ITEM2, ICT_CONFIG_HEADER_FIELD_ITEM2, null);
        headerVoList.add(headerVo);
        headerVo = new TableHeaderVo(ICT_CONFIG_HEADER_TITLE_ITEM4, ICT_CONFIG_HEADER_FIELD_ITEM4, null);
        headerVoList.add(headerVo);
        headerVoList.add(new TableHeaderVo().builder()
                .title(ICT_CONFIG_HEADER_TITLE_ITEM6)
                .field(ICT_CONFIG_HEADER_FIELD_ITEM6)
                .build());
        headerVo = new TableHeaderVo(ICT_CONFIG_HEADER_TITLE_ITEM7, ICT_CONFIG_HEADER_FIELD_ITEM7, null);
        headerVoList.add(headerVo);
        headerVo = new TableHeaderVo(ICT_CONFIG_HEADER_TITLE_ITEM8, ICT_CONFIG_HEADER_FIELD_ITEM8, null);
        headerVoList.add(headerVo);
        headerVoList.add(new TableHeaderVo().builder()
                .title(ICT_CONFIG_HEADER_TITLE_ITEM12)
                .field(ICT_CONFIG_HEADER_FIELD_ITEM12)
                .build());
        headerVo = new TableHeaderVo(ICT_CONFIG_HEADER_TITLE_ITEM13, ICT_CONFIG_HEADER_FIELD_ITEM13, null);
        headerVoList.add(headerVo);
    }

    private static void setEditedObjectList(List<TableHeaderVo> headerVoList) {
        TableHeaderVo headerVo = new TableHeaderVo(OBJECT_CONFIG_HEADER_TITLE_ITEM1, OBJECT_CONFIG_HEADER_FIELD_ITEM1, null);
        headerVoList.add(headerVo);
        headerVoList.add(new TableHeaderVo().builder()
                .field(OBJECT_CONFIG_HEADER_FIELD_ITEM2)
                .title(OBJECT_CONFIG_HEADER_TITLE_ITEM2)
                .build());
        headerVo = new TableHeaderVo(OBJECT_CONFIG_HEADER_TITLE_ITEM3, OBJECT_CONFIG_HEADER_FIELD_ITEM3, null);
        headerVoList.add(headerVo);
        headerVoList.add(new TableHeaderVo().builder()
                .field(OBJECT_CONFIG_HEADER_FIELD_ITEM4)
                .title(OBJECT_CONFIG_HEADER_TITLE_ITEM4)
                .build());
        headerVo = new TableHeaderVo(OBJECT_CONFIG_HEADER_TITLE_ITEM7, OBJECT_CONFIG_HEADER_FIELD_ITEM7, null);
        headerVoList.add(headerVo);
        headerVoList.add(new TableHeaderVo().builder()
                .title(OBJECT_CONFIG_HEADER_TITLE_ITEM8)
                .field(OBJECT_CONFIG_HEADER_FIELD_ITEM8)
                .build());
    }

    public static List<TableHeaderVo> creatExportHeaderList(String dataType) {
        List<TableHeaderVo> headerVoList = new ArrayList<>();
        if (dataType.equals(LabelConfigEnum.COA.getCode())) {
            setExportCoaList(headerVoList);
        } else if (dataType.equals(LabelConfigEnum.PLAN_COM.getCode())) {
            setExportPlanComList(headerVoList);
        } else if (dataType.equals(LabelConfigEnum.HOLISTIC_VIEW.getCode())) {
            setExportIctList(headerVoList);
        } else if (dataType.equals(LabelConfigEnum.CONFIG.getCode())) {
            setExportObjectList(headerVoList);
        } else if (dataType.equals(CommonConstant.LABEL_CONFIG_MONITOR_TYPE1)) {
            setExportIctProdList(headerVoList);
        } else if (dataType.equals(CommonConstant.LABEL_CONFIG_MONITOR_TYPE2)) {
            setExportCoaProdList(headerVoList);
        } else if (dataType.equals(CommonConstant.LABEL_CONFIG_MONITOR_TYPE3)) {
            setExportPlamComProdList(headerVoList);
        }
        return headerVoList;
    }

    public static void setExportPlamComProdList(List<TableHeaderVo> headerVoList) {
        TableHeaderVo build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM14)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM14)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM13)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM13)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM0)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM0)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM1)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM1)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM2)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM2)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM3)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM3)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM4)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM4)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM5)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM5)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM6)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM6)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM7)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM7)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM8)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM8)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM9)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM9)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM10)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM10)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_MONITOR_HEADER_TITLE_ITEM11)
                .field(PLAN_COM_CONFIG_MONITOR_HEADER_FIELD_ITEM11)
                .build();
        headerVoList.add(build);
    }

    public static void setExportCoaProdList(List<TableHeaderVo> headerVoList) {
        TableHeaderVo build = new TableHeaderVo().builder()
                .field(COA_CONFIG_MONITOR_HEADER_FIELD_ITEM1)
                .title(COA_CONFIG_MONITOR_HEADER_TITLE_ITEM1)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .field(COA_CONFIG_MONITOR_HEADER_FIELD_ITEM2)
                .title(COA_CONFIG_MONITOR_HEADER_TITLE_ITEM2)
                .build();
        headerVoList.add(build);
        headerVoList.add(new TableHeaderVo().builder()
                .field(COA_CONFIG_MONITOR_HEADER_FIELD_ITEM3)
                .title(COA_CONFIG_MONITOR_HEADER_TITLE_ITEM3)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(COA_CONFIG_MONITOR_HEADER_FIELD_ITEM4)
                .title(COA_CONFIG_MONITOR_HEADER_TITLE_ITEM4)
                .build());
        build = new TableHeaderVo().builder()
                .field(COA_CONFIG_MONITOR_HEADER_FIELD_ITEM5)
                .title(COA_CONFIG_MONITOR_HEADER_TITLE_ITEM5)
                .build();
        headerVoList.add(build);
        headerVoList.add(new TableHeaderVo().builder()
                .field(COA_CONFIG_MONITOR_HEADER_FIELD_ITEM6)
                .title(COA_CONFIG_MONITOR_HEADER_TITLE_ITEM6)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(COA_CONFIG_MONITOR_HEADER_TITLE_ITEM7)
                .field(COA_CONFIG_MONITOR_HEADER_FIELD_ITEM7)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(COA_CONFIG_MONITOR_HEADER_TITLE_ITEM8)
                .field(COA_CONFIG_MONITOR_HEADER_FIELD_ITEM8)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(COA_CONFIG_MONITOR_HEADER_FIELD_ITEM9)
                .title(COA_CONFIG_MONITOR_HEADER_TITLE_ITEM9)
                .build());
    }

    public static void setExportIctProdList(List<TableHeaderVo> headerVoList) {
        TableHeaderVo build = new TableHeaderVo().builder()
                .field(ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM0)
                .title(ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM0)
                .build();
        headerVoList.add(build);
        headerVoList.add(new TableHeaderVo().builder()
                .field(ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM1)
                .title(ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM1)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM2)
                .title(ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM2)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM3)
                .title(ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM3)
                .build());
        build = new TableHeaderVo().builder()
                .field(ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM4)
                .title(ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM4)
                .build();
        headerVoList.add(build);
        build = new TableHeaderVo().builder()
                .field(ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM5)
                .title(ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM5)
                .build();
        headerVoList.add(build);
        headerVoList.add(new TableHeaderVo().builder()
                .field(ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM6)
                .title(ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM6)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(ICT_CONFIG_MONITOR_HEADER_FIELD_ITEM7)
                .title(ICT_CONFIG_MONITOR_HEADER_TITLE_ITEM7)
                .build());
    }

    private static void setImportObjectList(List<TableHeaderVo> headerVoList) {
        headerVoList.add(new TableHeaderVo().builder()
                .field(OBJECT_CONFIG_HEADER_FIELD_ITEM1)
                .title(OBJECT_CONFIG_HEADER_TITLE_ITEM1)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(OBJECT_CONFIG_HEADER_FIELD_ITEM2)
                .title(OBJECT_CONFIG_HEADER_TITLE_ITEM2)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(OBJECT_CONFIG_HEADER_FIELD_ITEM3)
                .title(OBJECT_CONFIG_HEADER_TITLE_ITEM3)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(OBJECT_CONFIG_HEADER_FIELD_ITEM4)
                .title(OBJECT_CONFIG_HEADER_TITLE_ITEM4)
                .build());
    }

    private static void setExportObjectList(List<TableHeaderVo> headerVoList) {
        setImportObjectList(headerVoList);
        headerVoList.add(new TableHeaderVo().builder()
                .title(COA_CONFIG_HEADER_TITLE_ITEM9)
                .field(COA_CONFIG_HEADER_FIELD_ITEM9)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(OBJECT_CONFIG_HEADER_TITLE_ITEM7)
                .field(OBJECT_CONFIG_HEADER_FIELD_ITEM7)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(OBJECT_CONFIG_HEADER_TITLE_ITEM8)
                .field(OBJECT_CONFIG_HEADER_FIELD_ITEM8)
                .build());
    }

    private static void setImportIctList(List<TableHeaderVo> headerVoList) {
        headerVoList.add(new TableHeaderVo().builder()
                .title(ICT_CONFIG_HEADER_TITLE_ITEM2)
                .field(ICT_CONFIG_HEADER_FIELD_ITEM2)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(ICT_CONFIG_HEADER_TITLE_ITEM4)
                .field(ICT_CONFIG_HEADER_FIELD_ITEM4)
                .build());
        setIctList(headerVoList);
        headerVoList.add(new TableHeaderVo().builder()
                .title(ICT_CONFIG_HEADER_TITLE_ITEM14)
                .field(ICT_CONFIG_HEADER_FIELD_ITEM14)
                .build());
    }

    private static void setIctList(List<TableHeaderVo> headerVoList) {
        headerVoList.add(new TableHeaderVo().builder()
                .title(ICT_CONFIG_HEADER_TITLE_ITEM6)
                .field(ICT_CONFIG_HEADER_FIELD_ITEM6)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(ICT_CONFIG_HEADER_TITLE_ITEM7)
                .field(ICT_CONFIG_HEADER_FIELD_ITEM7)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(ICT_CONFIG_HEADER_TITLE_ITEM8)
                .field(ICT_CONFIG_HEADER_FIELD_ITEM8)
                .build());
    }

    private static void setExportIctList(List<TableHeaderVo> headerVoList) {
        setImportIctList(headerVoList);
        headerVoList.add(new TableHeaderVo().builder()
                .title(COA_CONFIG_HEADER_TITLE_ITEM9)
                .field(COA_CONFIG_HEADER_FIELD_ITEM9)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(ICT_CONFIG_HEADER_TITLE_ITEM12)
                .field(ICT_CONFIG_HEADER_FIELD_ITEM12)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(ICT_CONFIG_HEADER_TITLE_ITEM13)
                .field(ICT_CONFIG_HEADER_FIELD_ITEM13)
                .build());
    }

    private static void setImportPlanComList(List<TableHeaderVo> headerVoList) {
        setPlanComList(headerVoList);
    }

    private static void setPlanComList(List<TableHeaderVo> headerVoList) {
        headerVoList.add(new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_HEADER_TITLE_ITEM0)
                .field(PLAN_COM_CONFIG_HEADER_FIELD_ITEM0)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_HEADER_TITLE_ITEM1)
                .field(PLAN_COM_CONFIG_HEADER_FIELD_ITEM1)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_HEADER_TITLE_ITEM2)
                .field(PLAN_COM_CONFIG_HEADER_FIELD_ITEM2)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_HEADER_TITLE_ITEM3)
                .field(PLAN_COM_CONFIG_HEADER_FIELD_ITEM3)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_HEADER_TITLE_ITEM4)
                .field(PLAN_COM_CONFIG_HEADER_FIELD_ITEM4)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(PLAN_COM_CONFIG_HEADER_FIELD_ITEM5)
                .title(PLAN_COM_CONFIG_HEADER_TITLE_ITEM5)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_HEADER_TITLE_ITEM6)
                .field(PLAN_COM_CONFIG_HEADER_FIELD_ITEM6)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_HEADER_TITLE_ITEM7)
                .field(PLAN_COM_CONFIG_HEADER_FIELD_ITEM7)
                .build());
    }

    private static void setExportPlanComList(List<TableHeaderVo> headerVoList) {
        setPlanComList(headerVoList);
        headerVoList.add(new TableHeaderVo().builder()
                .title(COA_CONFIG_HEADER_TITLE_ITEM9)
                .field(COA_CONFIG_HEADER_FIELD_ITEM9)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_HEADER_TITLE_ITEM10)
                .field(PLAN_COM_CONFIG_HEADER_FIELD_ITEM10)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(PLAN_COM_CONFIG_HEADER_TITLE_ITEM11)
                .field(PLAN_COM_CONFIG_HEADER_FIELD_ITEM11)
                .build());
    }

    private static void setImportCoaList(List<TableHeaderVo> headerVoList) {
        headerVoList.add(new TableHeaderVo().builder()
                .title(COA_CONFIG_HEADER_TITLE_ITEM1)
                .field(COA_CONFIG_HEADER_FIELD_ITEM1)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(COA_CONFIG_HEADER_FIELD_ITEM2)
                .title(COA_CONFIG_HEADER_TITLE_ITEM2)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(COA_CONFIG_HEADER_FIELD_ITEM3)
                .title(COA_CONFIG_HEADER_TITLE_ITEM3)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(COA_CONFIG_HEADER_TITLE_ITEM4)
                .field(COA_CONFIG_HEADER_FIELD_ITEM4)
                .build());
    }

    private static void setExportCoaList(List<TableHeaderVo> headerVoList) {
        setImportCoaList(headerVoList);
        headerVoList.add(new TableHeaderVo().builder()
                .title(COA_CONFIG_HEADER_TITLE_ITEM9)
                .field(COA_CONFIG_HEADER_FIELD_ITEM9)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field(COA_CONFIG_HEADER_FIELD_ITEM7)
                .title(COA_CONFIG_HEADER_TITLE_ITEM7)
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title(COA_CONFIG_HEADER_TITLE_ITEM8)
                .field(COA_CONFIG_HEADER_FIELD_ITEM8)
                .build());
    }


    public static List<TableHeaderVo> setExportL1() {
        List<TableHeaderVo> headerVoList = new ArrayList<>();
        headerVoList.add(new TableHeaderVo().builder()
                .field("fcstStep")
                .title("时间颗粒度")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("预测方法")
                .field("fcstType")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field("lv1Name")
                .title("产业")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field("lv2Name")
                .title("LV2")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field("l1Name")
                .title("L1名称")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field("targetPeriod")
                .title("会计期")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("平均成本")
                .field("unitCost")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("平均价格")
                .field("unitPrice")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("量价到损益制毛调整率")
                .field("mgpAdjustRatio")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("对价等转换系数")
                .field("mcaAdjustRatio")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("发货量到收入转化率")
                .field("carryoverRatio")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("损益制毛率")
                .field("mgpRateAfter")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("损益收入额")
                .field("equipRevAfter")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("SOP期次")
                .field("phaseDate")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .field("periodId")
                .title("版本")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("BG")
                .field("bgName")
                .build());
        headerVoList.add(new TableHeaderVo().builder()
                .title("区域")
                .field("overseaDesc")
                .build());
        // 量价到损益制毛调整率,发货量到收入转化率,对价等转换系数,平均价格趋势，平均成本趋势
        return headerVoList;
    }


    /**
     * l2导出列表
     *
     * @return list
     */
    public static List<TableHeaderVo> setExportL2() {
        List<TableHeaderVo> l2headerVoList = new ArrayList<>();
        l2headerVoList.add(new TableHeaderVo().builder().field("fcstStep").title("时间颗粒度").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("预测方法").field("fcstType").build());
        l2headerVoList.add(new TableHeaderVo().builder().field("lv1Name").title("产业").build());
        l2headerVoList.add(new TableHeaderVo().builder().field("lv2Name").title("LV2").build());
        l2headerVoList.add(new TableHeaderVo().builder().field("l1Name").title("L1名称").build());
        l2headerVoList.add(new TableHeaderVo().builder().field("l2Name").title("L2名称").build());
        l2headerVoList.add(new TableHeaderVo().builder().field("targetPeriod").title("会计期").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("平均成本").field("unitCost").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("平均价格").field("unitPrice").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("L2收入结构").field("revPercent").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("发货量（实际&SOP）").field("shipQty").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("结转量").field("carryoverAmount").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("量价制毛率").field("mgpRateBefore").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("设备收入额（对价前）").field("equipRevBefore").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("设备成本额（对价前）").field("equipCostBefore").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("SOP期次").field("phaseDate").build());
        l2headerVoList.add(new TableHeaderVo().builder().field("periodId").title("版本").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("BG").field("bgName").build());
        l2headerVoList.add(new TableHeaderVo().builder().title("区域").field("overseaDesc").build());
        return l2headerVoList;
    }

}
