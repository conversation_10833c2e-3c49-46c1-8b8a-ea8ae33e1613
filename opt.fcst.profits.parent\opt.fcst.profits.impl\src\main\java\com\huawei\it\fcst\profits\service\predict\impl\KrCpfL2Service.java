/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.predict.impl;

import com.huawei.it.fcst.profits.comm.AbstractService;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.BgEnum;
import com.huawei.it.fcst.profits.common.enums.BoolEnum;
import com.huawei.it.fcst.profits.common.enums.ModuleEnum;
import com.huawei.it.fcst.profits.common.enums.OptTypeEnum;
import com.huawei.it.fcst.profits.common.enums.PredictionEnum;
import com.huawei.it.fcst.profits.common.enums.RecStsEnum;
import com.huawei.it.fcst.profits.common.enums.SaveTypeEnum;
import com.huawei.it.fcst.profits.common.utils.CalcUtils;
import com.huawei.it.fcst.profits.common.utils.DateUtil;
import com.huawei.it.fcst.profits.common.utils.GenerateUtils;
import com.huawei.it.fcst.profits.common.utils.UserHandle;
import com.huawei.it.fcst.profits.common.utils.biz.DataHelperUtils;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.common.vo.TableHeaderVo;
import com.huawei.it.fcst.profits.dao.IKrCpfL1Dao;
import com.huawei.it.fcst.profits.dao.IKrCpfL2Dao;
import com.huawei.it.fcst.profits.service.IExportService;
import com.huawei.it.fcst.profits.service.IKrCpfL2Service;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.utils.HeaderUtils;
import com.huawei.it.fcst.profits.vo.request.ForecastsParamWrapper;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.response.AxisVO;
import com.huawei.it.fcst.profits.vo.response.GroupL2GraphVO;
import com.huawei.it.fcst.profits.vo.response.KrCpfL1Response;
import com.huawei.it.fcst.profits.vo.response.KrCpfL2Response;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import cn.hutool.core.bean.BeanUtil;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.inject.Inject;
import javax.servlet.http.HttpServletResponse;

/**
 * L2展示相关接口
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:26:10
 */
@Service
public class KrCpfL2Service extends AbstractService implements IKrCpfL2Service {

    private static final String DATA_TYPE_ACT = "act";
    private static final String FCST_STEP_YTD = "月度YTD";
    private static final String OTHER = "其他";

    private static final Set<String> MONTH_DATE = Stream.of("01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12").collect(Collectors.toSet());
    private static final Logger logger = LoggerFactory.getLogger(KrCpfL2Service.class);

    private static final int START_PAGE = 1;

    private static final int MAX_PAGE = 1000;
    @Autowired
    private IExportService iExportService;
    @Inject
    private IKrCpfL2Dao iKrCpfL2Dao;

    @Inject
    private IKrCpfL1Dao iKrCpfL1Dao;

    private static boolean orderFlag;

    private static String orderField;

    private static String orderPeriod;


    /**
     * 获取L2数据名称
     *
     * @param forecastsRequest 请求信息
     * @return List
     */
    @Override
    public List<String> getL2Names(ForecastsRequest forecastsRequest) {
        List<String> l2Names = null;
        if (StringUtils.equals("1", forecastsRequest.getIncludeFlag())) {
            l2Names = iKrCpfL2Dao.getL2Names(forecastsRequest);
        } else {
            String lastPeriod = forecastsRequest.getActPeriods().get(forecastsRequest.getActPeriods().size() - 1);
            forecastsRequest.setFcstPeriods(Arrays.asList(lastPeriod));
            l2Names = iKrCpfL2Dao.getHisL2Names(forecastsRequest);
        }
        if (CollectionUtils.isEmpty(l2Names)) {
            return new ArrayList<>();
        }
        return l2Names;
    }

    /**
     * 分页查找数据
     *
     * @param forecastsRequest 请求信息
     * @return PagedResult分页结果
     * @throws CommonApplicationException 异常信息
     */
    @Override
    public PagedResult<KrCpfL2Response> findDataByPage(ForecastsRequest forecastsRequest) throws CommonApplicationException {
        if(checkLv1Result(forecastsRequest)){
            return new PagedResult<>();
        }
        PageVO pageVO = new PageVO();
        // 全排序改造
        if (forecastsRequest.getPageIndex() != START_PAGE && forecastsRequest.getPageSize() < MAX_PAGE) {
            pageVO.setPageSize(forecastsRequest.getPageSize());
        } else {
            pageVO.setPageSize(MAX_PAGE);
        }
        pageVO.setCurPage(START_PAGE);
        // 设置预测步长查询条件
        forecastsRequest.buildFcstStepQueryParam();
        forecastsRequest.withOverseaDescValue();
        // 查询预测或者预算数据，确定展示那些指标项
        PagedResult<KrCpfL2Response> l2PagedResult = iKrCpfL2Dao.getDataByPage(forecastsRequest, pageVO);
        if (CollectionUtils.isEmpty(l2PagedResult.getResult())) {
            return l2PagedResult;
        }
        List<KrCpfL2Response> resultList = new ArrayList<>();
        LinkedHashMap<String, KrCpfL2Response> linkedHashMap = new LinkedHashMap();
        l2PagedResult.getResult().stream().forEach(row -> {
            String groupKey = GenerateUtils.buildTempKey(row.getLv1Code(), row.getLv2Code(), row.getL1Name(), row.getL2Name());
            row.setGroupKey(groupKey);
            linkedHashMap.put(groupKey, row);
        });
        buildRows(linkedHashMap, forecastsRequest, resultList);
        setOderOptions(forecastsRequest);
        sorts(resultList);
        CalcUtils.setPageResult(forecastsRequest, resultList, l2PagedResult);
        return l2PagedResult;
    }

    private void sorts(List<KrCpfL2Response> resultList) {
        Collections.sort(resultList, (formerItems, latterItems) -> {
            BigDecimal obj1 = BigDecimal.ZERO;
            BigDecimal obj2 = BigDecimal.ZERO;
            if (!orderPeriod.equals("")) {
                Field targetField = ReflectionUtils.findField(formerItems.getClass(), orderPeriod);
                ReflectionUtils.makeAccessible(targetField);
                KrCpfL2Response targetPeriod1 = (KrCpfL2Response) ReflectionUtils.getField(targetField, formerItems);
                KrCpfL2Response targetPeriod2 = (KrCpfL2Response) ReflectionUtils.getField(targetField, latterItems);
                if (null != targetPeriod1 && null != targetPeriod2) {
                    if (orderField.equals("revPercent")) {
                        obj1 = null == targetPeriod1.getRevPercent() ? BigDecimal.ZERO : targetPeriod1.getRevPercent();
                        obj2 = null == targetPeriod2.getRevPercent() ? BigDecimal.ZERO : targetPeriod2.getRevPercent();
                    } else if (orderField.equals("mgpRateBefore")) {
                        obj1 = null == targetPeriod1.getMgpRateBefore() ? BigDecimal.ZERO : targetPeriod1.getMgpRateBefore();
                        obj2 = null == targetPeriod2.getMgpRateBefore() ? BigDecimal.ZERO : targetPeriod2.getMgpRateBefore();
                    }
                    if (orderFlag) {
                        return obj1.compareTo(obj2);
                    } else {
                        return obj2.compareTo(obj1);
                    }
                } else if (null == targetPeriod1 && null != targetPeriod2) {
                    return -1 * (orderFlag ? 1 : -1);
                } else if (null != targetPeriod1 && null == targetPeriod2) {
                    return 1 * (orderFlag ? 1 : -1);
                } else {
                    return 0;
                }
            }
            return 0;
        });
    }

    /**
     * 排序设置相关参数
     *
     * @param forecastsRequest forecastsRequest
     */
    private void setOderOptions(ForecastsRequest forecastsRequest) {
        CalcUtils.setL2OrderPrepartion(forecastsRequest);
        orderFlag = "asc".equals(forecastsRequest.getOrderType()) ? true : false;
        orderField = CalcUtils.getSuffixVal(forecastsRequest.getOrderField());
        orderPeriod = CalcUtils.getPrefixVal(forecastsRequest.getOrderField());
    }

    /**
     * 组装数据
     *
     * @param linkedHashMap    列信息
     * @param forecastsRequest 请求信息
     * @param resultList       结果集合
     * @throws CommonApplicationException 异常信息
     */
    public void buildRows(LinkedHashMap<String, KrCpfL2Response> linkedHashMap, ForecastsRequest forecastsRequest, List<KrCpfL2Response> resultList) throws CommonApplicationException {
        Map<String, List<KrCpfL2Response>> groupResultMap = groupByKey(forecastsRequest, BoolEnum.TRUE.getCode(), BoolEnum.TRUE.getCode(), BoolEnum.TRUE.getCode());
        linkedHashMap.entrySet().stream().forEach(entry -> {
            List<KrCpfL2Response> fullRecord = new ArrayList<>();
            if (Objects.nonNull(groupResultMap) && groupResultMap.containsKey(entry.getKey())) {
                fullRecord.addAll(groupResultMap.get(entry.getKey()));
            }
            KrCpfL2Response lv2Row = new KrCpfL2Response();
            buildRowData(lv2Row, entry);
            fullRecord.stream().forEach(idenity -> {
                setModuleData(forecastsRequest, idenity, lv2Row);
            });
            resultList.add(lv2Row);
        });
    }


    /**
     * 构建行数据
     *
     * @param lv1Row lv1
     * @param entry  l2
     */
    public void buildRowData(KrCpfL2Response lv1Row, Map.Entry<String, KrCpfL2Response> entry) {
        lv1Row.setBgCode(entry.getValue().getBgCode());
        lv1Row.setLv1Code(entry.getValue().getLv1Code());
        lv1Row.setLv1Name(entry.getValue().getLv1Name());
        lv1Row.setLv2Code(entry.getValue().getLv2Code());
        lv1Row.setLv2Name(entry.getValue().getLv2Name());
        lv1Row.setL1Name(entry.getValue().getL1Name());
        lv1Row.setL2Name(entry.getValue().getL2Name());
        lv1Row.setGroupKey(entry.getKey());
    }

    /**
     * 按key分类
     *
     * @param forecastsRequest 结果集合
     * @param lv2Code          l2代码
     * @param l1Name           l1名称
     * @param l2Name           l2名称
     * @return Map处理后集合
     */
    public Map<String, List<KrCpfL2Response>> groupByKey(ForecastsRequest forecastsRequest, String lv2Code, String l1Name, String l2Name) throws CommonApplicationException {
        // 修改查询 添加group by 分组，sum 指标值，原因是因为在ytd法 包含了（ytd法，时序法）两种数据
        List<KrCpfL2Response> targetData = iKrCpfL2Dao.getDataByDimension(forecastsRequest);
        return targetData.stream().collect(Collectors.groupingBy(act -> {
            String finalLv2Code = null;
            String finalL1Name = null;
            String finalL2Name = null;
            if (Objects.nonNull(lv2Code)) {
                finalLv2Code = act.getLv2Code();
            }
            if (Objects.nonNull(l1Name)) {
                finalL1Name = act.getL1Name();
            }
            if (Objects.nonNull(l2Name)) {
                finalL2Name = act.getL2Name();
            }
            return GenerateUtils.buildTempKey(act.getLv1Code(), finalLv2Code, finalL1Name, finalL2Name);
        }));
    }

    /**
     * 获取L2分组比率信息
     *
     * @param l2TotalData l2所有信息
     * @param l2GroupData l2分组数据
     * @param tempList    暂存List
     * @return void
     */
    public void calcRatio(ForecastsRequest forecastsRequest ,Map<String, List<KrCpfL2Response>> l2TotalData, Map<String, List<KrCpfL2Response>> l2GroupData, List<KrCpfL2Response> tempList) {
        if (Objects.isNull(l2TotalData)) {
            return;
        }
        if (Objects.isNull(l2GroupData)) {
            return;
        }
        l2TotalData.entrySet().stream().forEach(entry -> {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                return;
            }
            // 计算不包含其他收入占比值求和；
            BigDecimal sum = entry.getValue()
                    .stream()
                    .filter(item -> !OTHER.equalsIgnoreCase(item.getL2Name()))
                    .collect(Collectors.toList())
                    .stream()
                    .map(KrCpfL2Response::getRevPercent)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (l2GroupData.containsKey(entry.getKey()) && !CollectionUtils.isEmpty(l2GroupData.get(entry.getKey()))) {
                // 获取L1对象做占比计算
                KrCpfL2Response krCpfL2Responses =
                        l2GroupData.get(entry.getKey()).stream().findFirst().orElse(new KrCpfL2Response());
                entry.getValue().stream().forEach(rowData -> {
                    rowData.setRatioCarryoverAmount(CalcUtils.div(rowData.getCarryoverAmount(),
                            krCpfL2Responses.getCarryoverAmount()));
                    rowData.setRatioShipQty(
                            CalcUtils.div(rowData.getShipQty(), krCpfL2Responses.getShipQty()));
                    // 其他逻辑计算，ran 占比数据组计算，其他占l1占比java 计算
                    if (OTHER.equalsIgnoreCase(rowData.getL2Name())) {
                        rowData.setRatioEquipRev(BigDecimal.ONE.subtract(sum));
                        rowData.setRevPercentRan(rowData.getRevPercent());
                    } else {
                        rowData.setRatioEquipRev(rowData.getRevPercent());
                    }
                    tempList.add(rowData);
                });
            }
        });
    }


    /**
     * 获取L2柱状图信息
     *
     * @param forecastsRequest 预测请求
     * @return GroupL2GraphVO l2组别信息
     */
    @Override
    public GroupL2GraphVO getL2GrapInfo(ForecastsRequest forecastsRequest) throws CommonApplicationException {
        GroupL2GraphVO groupL2VO = new GroupL2GraphVO();
        if(checkLv1Result(forecastsRequest)){
            return groupL2VO;
        }
        // 区域值设置
        forecastsRequest.withOverseaDescValue();
        ForecastsParamWrapper.ForceastParamEnum enumType = ForecastsParamWrapper
                .ForceastParamEnum.getByCode(ForecastsParamWrapper.getSplicedPredictionTabGraphValue(forecastsRequest));
        if(Objects.isNull(enumType)){
            logger.error("请检查请求参数");
            return groupL2VO;
        }
        // 通过枚举获取当前参数构建逻辑；
        enumType.beforeBuildParam(forecastsRequest);
        // 年度，展示auto列
        Map<String, List<KrCpfL2Response>> l2TotalInfo = getL2TotalInfo(forecastsRequest);
        Map<String, List<KrCpfL2Response>> l2GroupMap = getL2GroupInfo(forecastsRequest);
        List<KrCpfL2Response> tempList = new ArrayList<>();
        // 计算比率
        calcRatio(forecastsRequest, l2TotalInfo, l2GroupMap, tempList);
        Map<String, AxisVO> axisVOTMap = enumType.afterBuildParam(forecastsRequest);
        Map<String, LinkedList<KrCpfL2Response>> axisData = new LinkedHashMap<>();
        LinkedList<AxisVO> axisHeader = new LinkedList<>();
        axisVOTMap.forEach((key,value) ->{
            axisData.put(key,new LinkedList<>());
            axisHeader.add(value);
        });
        Set<String> l2Names = new HashSet<>();
        // X-轴设值
        tempList.stream().forEach(temp -> {
            l2Names.add(temp.getL2Name());
            String key = temp.getTargetPeriod() + temp.getDataType();
            if (axisData.containsKey(key)) {
                axisData.get(key).add(temp);
            }
        });
        groupL2VO.setAxisData(axisData);
        groupL2VO.setAxisHeader(axisHeader);
        groupL2VO.setL2Names(Arrays.asList(l2Names.toArray(new String[0])));
        groupL2VO.setCurYear(forecastsRequest.getCurYear());
        return groupL2VO;
    }

    /**
     * 设置组别key
     *
     * @param l2GroupInfo L2分组信息
     */
    public void setGroupKey(List<KrCpfL2Response> l2GroupInfo) {
        l2GroupInfo.stream().forEach(l2 -> {
            l2.setGroupKey(GenerateUtils.buildKey(l2.getLv1Code(), l2.getLv2Code(), l2.getL1Name(), l2.getTargetPeriod(),l2.getDataType()));
        });
    }

    /**
     * 设置组别key
     *
     * @param l1GroupInfo L2分组信息
     */
    public void setL1GroupKey(List<KrCpfL1Response> l1GroupInfo) {
        l1GroupInfo.stream().forEach(l1 -> {
            l1.setGroupKey(GenerateUtils.buildKey(l1.getLv1Code(), l1.getLv2Code(), l1.getL1Name(), l1.getTargetPeriod(),l1.getDataType()));
        });
    }

    /**
     * 获取L2业务折线信息
     *
     * @param forecastsRequest 预测请求
     * @return L2PolylineVO 业务折线信息
     */
    @Override
    public GroupL2GraphVO getL2PolylineInfo(ForecastsRequest forecastsRequest) throws CommonApplicationException {
        if(checkLv1Result(forecastsRequest)){
            return new GroupL2GraphVO();
        }
        GroupL2GraphVO groupL2VO = new GroupL2GraphVO();
        // 区域值设置
        forecastsRequest.withOverseaDescValue();
        ForecastsParamWrapper.ForceastParamEnum enumType = ForecastsParamWrapper
                .ForceastParamEnum
                .getByCode(ForecastsParamWrapper.getSplicedPredictionTabGraphValue(forecastsRequest));
        if(Objects.isNull(enumType)){
            logger.error("请检查请求参数");
            return groupL2VO;
        }
        // 通过枚举获取当前参数构建逻辑；
        ForecastsRequest buildParam = enumType.beforeBuildParam(forecastsRequest);
        List<KrCpfL2Response> tempList = getL2Info(buildParam);
        Set<String> excludeSet = new HashSet<>();
        Map<String, AxisVO> axisVOTMap = enumType.afterBuildParam(forecastsRequest);
        Map<String, LinkedList<KrCpfL2Response>> axisData = new LinkedHashMap<>();
        LinkedList<AxisVO> axisHeader = new LinkedList<>();
        axisVOTMap.forEach((key,value) ->{
            axisData.put(key,new LinkedList<>());
            axisHeader.add(value);
        });
        tempList.stream().forEach(act -> {
                String articulationVal = getArticulationVal(act.getLv1Name(), act.getLv2Name(), act.getL1Name());
                // 场景二的名为“其他”的L2不展示均价、均本
                if (StringUtils.equals("2", articulationVal) && StringUtils.equals("其他", act.getL2Name())) {
                    excludeSet.add(act.getL2Name());
                    return;
                }
                String targetPeriod = act.getTargetPeriod() + act.getDataType();
                if (axisData.containsKey(targetPeriod)) {
                    axisData.get(targetPeriod).add(act);
                }
            });
        groupL2VO.setAxisData(axisData);
        groupL2VO.setAxisHeader(axisHeader);
        groupL2VO.setL2Names(buildL2Names(buildParam, excludeSet));
        groupL2VO.setCurYear(buildParam.getCurYear());
        return groupL2VO;
    }

    /**
     * 构建展示的L2名称
     *
     * @param forecastsRequest
     * @return
     */
    public List<String> buildL2Names(ForecastsRequest forecastsRequest, Set<String> excludeSet) {
        List<String> resultList = forecastsRequest.getL2Names();
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = new ArrayList<>();
        }
        List<String> l2Names = getL2Names(forecastsRequest);
        for (String l2 : l2Names) {
            if (!resultList.contains(l2)) {
                resultList.add(l2);
            }
        }
        List<String> topList = getTop5L2Name(forecastsRequest);
        if (CollectionUtils.isEmpty(topList)) {
            return resultList;
        }
        LinkedHashSet<String> tempSet = new LinkedHashSet<>();
        topList.stream().forEach(top -> {
            if (excludeSet.contains(top)) {
                return;
            }
            tempSet.add(top);
        });
        for (String l2 : resultList) {
            if (excludeSet.contains(l2)) {
                continue;
            }
            tempSet.add(l2);
        }
        return Arrays.asList(tempSet.toArray(new String[0]));
    }

    /**
     * 获取L2数据TOP5名称
     *
     * @param forecastsRequest l1信息
     * @return List
     */

    public List<String> getTop5L2Name(ForecastsRequest forecastsRequest) {
        List<String> top5L2Names = null;
        if (StringUtils.equals("1", forecastsRequest.getIncludeFlag())) {
            top5L2Names = iKrCpfL2Dao.getTop5L2Name(forecastsRequest);
        } else {
            top5L2Names = iKrCpfL2Dao.getHisTop5L2Name(forecastsRequest);
        }
        if (CollectionUtils.isEmpty(top5L2Names)) {
            return new ArrayList<>();
        }
        return top5L2Names;
    }

    /**
     * 导出l2数据
     *
     * @param forecastsRequest 预测请求
     * @param response         响应信息
     */
    @Override
    public CommonResult exportL2Data(ForecastsRequest forecastsRequest, HttpServletResponse response) throws CommonApplicationException {
        forecastsRequest.setPageSize(CommonConstant.MAX_PAGE_SIZE);
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        // 结构及制毛率导出
        int l2 = exportStructureAndRate(workbook, forecastsRequest);
        if (0 == l2) {
            return CommonResult.success("暂无数据");
        }
        String fileName = getFileNameByBg(forecastsRequest, "2");
        // 上传
        Map<String, Object> map = new HashMap<>();
        map.put("fileName", fileName);
        excelUtil.downloadAndUploadS3Excel(response, workbook, map);
        creatRecord(fileName, Long.valueOf(String.valueOf(map.get("fileSize") != null ? map.get("fileSize") : "0")), SaveTypeEnum.SAVE.getCode(), "", l2, String.valueOf(forecastsRequest.getPeriodId()), String.valueOf(map.get("fileKey")), null, OptTypeEnum.EXPORT.getCode(), RecStsEnum.SUCCESS.getCode(), ModuleEnum.MODULE_FORECASTS.getDesc());
        return CommonResult.success("ok");
    }

    @Override
    public CommonResult exportFactorByLv1(ForecastsRequest forecastsRequest, HttpServletResponse response) throws CommonApplicationException {
        if (StringUtils.equals(CommonConstant.TAB_YEAR, forecastsRequest.getPredictionType())) {
            int year = TimeUtils.getCurYear(forecastsRequest.getPeriodId());
            int month = TimeUtils.getCurMonth(forecastsRequest.getPeriodId());
            forecastsRequest.setCurYear(year);
            forecastsRequest.setCurMonth(month);
            // 设置lookupCode
            forecastsRequest.setFcstType("YEAR_YTD");
            // 转换前段传值预测步长方法映射值
            CalcUtils.setFcstTypeValue(forecastsRequest);
            // 年度导出全球20231207
            forecastsRequest.withOverseaDescValue();
            // 设置历史
            List<String> actPeriods = DataHelperUtils.getFullHisMonth(forecastsRequest.getCurYear(), forecastsRequest.getCurMonth()).stream().map(str -> {
                return str + "YTD";
            }).collect(Collectors.toList());
            actPeriods.addAll(DataHelperUtils.getFullHisYear(forecastsRequest.getCurYear(), forecastsRequest.getCurMonth(), ""));
            // 设置预测
            List<String> fcstPeriods = DataHelperUtils.getFullPredictMonth(forecastsRequest.getCurYear(), forecastsRequest.getCurMonth());
            fcstPeriods.addAll(Arrays.asList(String.valueOf(forecastsRequest.getCurYear()), String.valueOf(forecastsRequest.getCurYear() + 1)));
            forecastsRequest.setFcstPeriods(fcstPeriods);
            forecastsRequest.setActPeriods(actPeriods);
        }else{
            // 月度预测设置过滤Q季度，4，5，6 Q2；例如202304 在Q2区间，202307季度才有Q2季度的数据
            forecastsRequest.setActPeriods(Arrays.asList(DateUtil.getQuarterTime(String.valueOf(forecastsRequest.getPeriodId()))));
            // 月度预测导出所有区域的数据
            forecastsRequest.setOverseaDesc(null);
        }
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        int countL1 = iKrCpfL1Dao.exportL1FactorCount(forecastsRequest);
        int countL2 = iKrCpfL2Dao.exportL2FactorCount(forecastsRequest);
        if (countL1 > 100000 || countL2 > 100000) {
            CommonResult.failed("数据量超过10万限制,请选择产业导出");
        }
        // l1页签数据输出
        int l1num = exportL1Factor(workbook, forecastsRequest);
        // l2页签导出
        int l2num = exportL2Factor(workbook, forecastsRequest);
        String fileName = getExportFactorByLv1FileName(forecastsRequest);
        // 上传
        Map<String, Object> map = new HashMap<>();
        map.put("fileName", fileName);
        excelUtil.downloadAndUploadS3Excel(response, workbook, map);
        creatRecord(fileName, Long.valueOf(String.valueOf(map.get("fileSize") != null ? map.get("fileSize") : "0")), SaveTypeEnum.SAVE.getCode(), "", l1num + l2num, String.valueOf(forecastsRequest.getPeriodId()), String.valueOf(map.get("fileKey")), null, OptTypeEnum.EXPORT.getCode(), RecStsEnum.SUCCESS.getCode(), ModuleEnum.MODULE_FORECASTS.getDesc());
        return CommonResult.success("ok");
    }

    private String getExportFactorByLv1FileName(ForecastsRequest forecastsRequest){
        List<String> lv1Names = Optional.ofNullable(forecastsRequest.getLv1Names()).orElse(new ArrayList<>());
        StringJoiner joiner = null;
        if (StringUtils.equals(CommonConstant.TAB_YEAR, forecastsRequest.getPredictionType())) {
            joiner = new StringJoiner("&","价格成本预测因子-","-" + forecastsRequest.getPeriodId() + PredictionEnum.YEAR.getDesc());
        }else{
            joiner = new StringJoiner("&" ,"价格成本预测因子-","-" + forecastsRequest.getPeriodId()+ PredictionEnum.MONTH.getDesc());
        }
        // 导出文件名称拼接，限制超出表字符串长度；
        Integer length = 150;
        for (String name :lv1Names) {
            length = length - name.getBytes(StandardCharsets.UTF_8).length;
            if(length > 0){
                joiner.add(name);
            }else {
                break;
            }
        }
        return joiner.toString();
    }

    private int exportStructureAndRate(SXSSFWorkbook workbook, ForecastsRequest forecastsRequest) throws CommonApplicationException {
        PagedResult<KrCpfL2Response> pagedResult = findDataByPage(forecastsRequest);
        if (CollectionUtils.isEmpty(pagedResult.getResult())) {
            return 0;
        }
        String fileName = getFileNameByBg(forecastsRequest, "2");
        workbook.createSheet(fileName);
        String l1Name = pagedResult.getResult().get(0).getL1Name();
        List<String> bgCodes = Arrays.asList(forecastsRequest.getBgCode());
        String bgCode = CommUtils.getBgCode(bgCodes);
        List<TableHeaderVo> lv1Header = HeaderUtils.buildL2Header(l1Name, bgCode, forecastsRequest);
        // 增加L2小计数据
        KrCpfL2Response pagedResultPart = getSubTotalInfo(forecastsRequest);
        mergeL2RequestInfo(pagedResult, pagedResultPart);
        Map<String, Object> params = new ConcurrentHashMap<>();
        params.put("userId", UserHandle.getUserId());
        iExportService.exportL2Data(workbook, pagedResult.getResult(), lv1Header, params);
        // 当l2层级导出时会记录l2小计数据多余了1,在此时剔除删除
        int rowNum = pagedResult.getResult().size();
        if (rowNum != 0) {
            rowNum--;
        }
        return rowNum;
    }

    private int exportL1Factor(SXSSFWorkbook workbook, ForecastsRequest forecastsRequest) throws CommonApplicationException {
        workbook.createSheet("L1信息下载表");
        List<Map<String, Object>> formatResultList = new ArrayList<>();
        List<KrCpfL1Response> resultResult = iKrCpfL1Dao.exportL1Factor(forecastsRequest);
        final List<KrCpfL1Response> buildData = resultResult.stream().map(item -> {
            item.setBgName(setBgName(item.getBgCode()));
            item.setFcstStep(setTargetPeriodName(item.getTargetPeriod(), item.getGroupKey(), item.getFcstType()));
            // 历史数据设置
            if (DATA_TYPE_ACT.equalsIgnoreCase(item.getGroupKey()) && Optional.ofNullable(item.getTargetPeriod()).orElse("").contains("YTD")) {
                String targetPeriod = item.getTargetPeriod();
                item.setTargetPeriod(targetPeriod.substring(0, 6));
            }
            // 预测表中预测方法null 设置分月法
            if (!DATA_TYPE_ACT.equalsIgnoreCase(item.getGroupKey()) ) {
                if(Objects.isNull(item.getFcstType())) {
                    item.setFcstType("分月法");
                }
                if(CommonConstant.FCST_TYPE_TIME.equalsIgnoreCase(item.getFcstType())){
                    item.setFcstType("YTD法");
                }
            }
            return item;
        }).collect(Collectors.toList());
        buildData.forEach(resultObject -> formatResultList.add(BeanUtil.beanToMap(resultObject)));
        Map<String, Object> params = new ConcurrentHashMap<>();
        params.put("userId", UserHandle.getUserId());
        iExportService.exportL1Factor(workbook, formatResultList, params);
        return resultResult.size();
    }

    private int exportL2Factor(SXSSFWorkbook workbook, ForecastsRequest forecastsRequest) throws CommonApplicationException {
        workbook.createSheet("L2信息下载表");
        final List<KrCpfL2Response> buildData = iKrCpfL2Dao.exportL2Factor(forecastsRequest).stream().map(l2Response -> {
            l2Response.setBgName(setBgName(l2Response.getBgCode()));
            l2Response.setFcstStep(setTargetPeriodName(l2Response.getTargetPeriod(), l2Response.getGroupKey(), l2Response.getFcstType()));
            if (DATA_TYPE_ACT.equalsIgnoreCase(l2Response.getGroupKey()) && Optional.ofNullable(l2Response.getTargetPeriod()).orElse("").contains("YTD")) {
                String targetPeriod = l2Response.getTargetPeriod();
                l2Response.setTargetPeriod(targetPeriod.substring(0, 6));
            }
            // 预测表中预测方法null 设置分月法
            if (!DATA_TYPE_ACT.equalsIgnoreCase(l2Response.getGroupKey())) {
                if (Objects.isNull(l2Response.getFcstType())) {
                    l2Response.setFcstType("分月法");
                }
                if (CommonConstant.FCST_TYPE_TIME.equalsIgnoreCase(l2Response.getFcstType())) {
                    l2Response.setFcstType("YTD法");
                }
            }
            return l2Response;
        }).collect(Collectors.toList());
        List<Map<String, Object>> formatResultList = new ArrayList<>();
        buildData.forEach(krCpfL2Response -> formatResultList.add(BeanUtil.beanToMap(krCpfL2Response)));
        Map<String, Object> params = new ConcurrentHashMap<>();
        params.put("userId", UserHandle.getUserId());
        iExportService.exportL2Factor(workbook, formatResultList, params);
        return buildData.size();
    }

    private String setBgName(String bgCode) {
        if (BgEnum.EBG.getCode().equalsIgnoreCase(bgCode)) {
            return BgEnum.EBG.getDesc();
        }
        if (BgEnum.CNBG.getCode().equalsIgnoreCase(bgCode)) {
            return BgEnum.CNBG.getDesc();
        }
        if (BgEnum.GROUP.getCode().equalsIgnoreCase(bgCode)) {
            return BgEnum.GROUP.getDesc();
        }
        return "";
    }

    private String setTargetPeriodName(String targetPeriod, String dataType, String fcstType) {
        if (Objects.isNull(targetPeriod)) {
            return "";
        }
        if (targetPeriod.length() == 4) {
            return CommonConstant.FCST_YEAR_STEP;
        }
        if (targetPeriod.contains(CommonConstant.TAB_HALF_YEAR)) {
            return CommonConstant.FCST_HALF_YEAR_STEP;
        }
        if (targetPeriod.contains(CommonConstant.TAB_HALF_YEAR_QUARTER)) {
            return CommonConstant.FCST_QUARTER_STEP;
        }
        if (targetPeriod.contains(CommonConstant.X_AXIOS_YTD)) {
            return FCST_STEP_YTD;
        }
        if (targetPeriod.length() == 6 && MONTH_DATE.contains(targetPeriod.substring(4, 6))) {
            // 预测修改
            if (DATA_TYPE_ACT.equalsIgnoreCase(dataType)) {
                return "月度";
            } else {
                if (Objects.nonNull(fcstType)) {
                    return FCST_STEP_YTD;
                } else {
                    return "月度";
                }
            }
        }
        return targetPeriod;
    }

    /**
     * 合并L2小计数据
     *
     * @param pagedResult     pagedResult
     * @param pagedResultPart pagedResultPart
     */
    private void mergeL2RequestInfo(PagedResult<KrCpfL2Response> pagedResult, KrCpfL2Response pagedResultPart) {
        List<KrCpfL2Response> infolists = pagedResult.getResult();
        pagedResultPart.setL2Name("L2小计");
        infolists.add(pagedResultPart);
        pagedResult.setResult(infolists);
    }

    /**
     * 统计小计数据信息
     *
     * @param forecastsRequest 预测请求
     * @return KrCpfL2Response
     */
    @Override
    public KrCpfL2Response getSubTotalInfo(ForecastsRequest forecastsRequest) {
        KrCpfL2Response tailRow = new KrCpfL2Response();
        Map<String, List<KrCpfL1Response>> l1GroupInfo = getL1GroupInfo(forecastsRequest);
        List<KrCpfL2Response> l2SumInfo = getSumL2Info(forecastsRequest);
        if (CollectionUtils.isEmpty(l2SumInfo)) {
            return tailRow;
        }
        l2SumInfo.stream().forEach(l2 -> {
            if (Objects.nonNull(l1GroupInfo) && l1GroupInfo.containsKey(l2.getGroupKey()) && Objects.nonNull(l1GroupInfo.get(l2.getGroupKey()))) {
                l2.setMcaAdjustRatio(l1GroupInfo.get(l2.getGroupKey()).get(0).getMcaAdjustRatio());
                l2.setMgpAdjustRatio(l1GroupInfo.get(l2.getGroupKey()).get(0).getMgpAdjustRatio());
            }
            setModuleData(forecastsRequest, l2, tailRow);
        });
        return tailRow;
    }

    /**
     * 获取l2所有数据信息
     *
     * @param forecastsRequest 预测请求
     * @return List
     */
    public Map<String, List<KrCpfL2Response>> getL2TotalInfo(ForecastsRequest forecastsRequest) {
        List<KrCpfL2Response> l2TotalInfo = iKrCpfL2Dao.getL2TotalInfo(forecastsRequest);
        if (CollectionUtils.isEmpty(l2TotalInfo)) {
            return null;
        }
        setGroupKey(l2TotalInfo);
        return l2TotalInfo.stream().collect(Collectors.groupingBy(l2 -> l2.getGroupKey()));
    }

    /**
     * 获取l1分组信息
     *
     * @param forecastsRequest 预测请求
     * @return List
     */

    public Map<String, List<KrCpfL1Response>> getL1GroupInfo(ForecastsRequest forecastsRequest) {
        List<KrCpfL1Response> l1TotalList = iKrCpfL1Dao.getL1TotalInfo(forecastsRequest);
        if (CollectionUtils.isEmpty(l1TotalList)) {
            return null;
        }
        setL1GroupKey(l1TotalList);
        return l1TotalList.stream().collect(Collectors.groupingBy(l1 -> l1.getGroupKey()));
    }

    /**
     * 获取l2组别信息
     *
     * @param forecastsRequest 预测请求
     * @return List
     */
    public Map<String, List<KrCpfL2Response>> getL2GroupInfo(ForecastsRequest forecastsRequest) {
        List<KrCpfL2Response> groupList = iKrCpfL2Dao.getL2GroupInfo(forecastsRequest);
        if (CollectionUtils.isEmpty(groupList)) {
            return null;
        }
        setGroupKey(groupList);
        return groupList.stream().collect(Collectors.groupingBy(l2 -> l2.getGroupKey()));
    }

    /**
     * 获取l2组别信息
     *
     * @param forecastsRequest 预测请求
     * @return List
     */
    public List<KrCpfL2Response> getSumL2Info(ForecastsRequest forecastsRequest) {
        String[] l2names = forecastsRequest.getL2Name();
        forecastsRequest.setL2Names(Arrays.asList(l2names));
        List<KrCpfL2Response> l2ResponseList = iKrCpfL2Dao.getL2GroupInfo(forecastsRequest);
        if (CollectionUtils.isEmpty(l2ResponseList)) {
            return null;
        }
        setGroupKey(l2ResponseList);
        return l2ResponseList;
    }

    /**
     * 获取l2所有数据信息
     *
     * @param forecastsRequest 预测请求
     * @return List
     */
    public List<KrCpfL2Response> getL2Info(ForecastsRequest forecastsRequest) {
        return iKrCpfL2Dao.getL2TotalInfo(forecastsRequest);
    }
}
