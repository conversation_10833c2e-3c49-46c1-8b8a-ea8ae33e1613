/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huawei.it.fcst.profits.common.annotations.ExportAttribute;
import com.huawei.it.fcst.profits.vo.request.PageRequest;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

/**
 * 预算预测 GroupAnalystsVO
 */
@Getter
@Setter
@ExcelIgnoreUnannotated
public class GroupAnalystsVO extends PageRequest {

    @ExportAttribute(sort = 0)
    @ExcelProperty(value = "版本")
    private String versionCode;

    @ExcelIgnore
    private String lv1Code;

    @ExcelProperty(value = "LV1名称")
    @ExportAttribute(sort = 1)
    private String lv1Name;

    @ExcelIgnore
    private String lv2Code;

    @ExcelProperty(value = "LV2名称")
    @ExportAttribute(sort = 2)
    private String lv2Name;

    @ExcelIgnore
    private List<String> lv1Names;

    @ExcelIgnore
    private List<String> lv1Codes;

    @ExcelIgnore
    private List<String> lv2Names;

    @ExcelIgnore
    private List<String> lv2Codes;

    @ExcelIgnore
    private List<GroupAnalystsVO> children;

    @ExcelProperty(value = "设备收入")
    @ExportAttribute(sort = 3)
    private String equipRevAmt;

    @ExcelProperty(value = "制毛率")
    @ExportAttribute(sort = 4)
    private String mgpRatio;

    @ExcelIgnore
    private String targetCode;

    @ExcelProperty(value = "预测步长")
    @ExportAttribute(sort = 5)
    private String targetDesc;

    @ExcelIgnore
    private String errorMsg;

    @ExcelIgnore
    private String status;

    @ExcelIgnore
    private String remark;

    @ExcelIgnore
    private String yearEquipRevAmt;

    @ExcelIgnore
    private String yearMgpRatio;

    @ExcelIgnore
    private String monthEquipRevAmt;

    @ExcelIgnore
    private String monthMgpRatio;

    @ExcelIgnore
    private String quartEquipRevAmt;

    @ExcelIgnore
    private String quartMgpRatio;

    @ExcelIgnore
    private String halfYearEquipRevAmt;

    @ExcelIgnore
    private String halfYearMgpRatio;

    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    // 创建时间
    private Timestamp creationDate;

    // 创建人
    @ExcelIgnore
    private Long createdBy;

    // 修改人
    @ExcelIgnore
    private Long lastUpdatedBy;

    @ExcelIgnore
    private String lastUpdatedByName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    // 修改时间
    private Timestamp lastUpdateDate;

    // 全量导入标识
    @ExcelIgnore
    private boolean fullImportFlag;

    @Override
    public String toString() {
        return "GroupAnalystsVO{" +
                "versionCode='" + versionCode + '\'' +
                ", lv1Name='" + lv1Name + '\'' +
                ", lv2Name='" + lv2Name + '\'' +
                ", equipRevAmt='" + equipRevAmt + '\'' +
                ", mgpRatio='" + mgpRatio + '\'' +
                ", targetDesc='" + targetDesc + '\'' +
                '}';
    }
}
