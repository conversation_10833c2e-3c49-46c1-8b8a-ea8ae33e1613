/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.service.lts.impl;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import com.alibaba.fastjson.JSON;
import com.huawei.it.fcst.profits.common.utils.FcstGlobalParameterUtil;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.dao.ILtsTaskIntegrationDao;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.vo.CellFuncParamVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.registry.RegistryVO;
import com.huawei.it.jalor5.security.UserVO;
import com.huawei.it.jalor5.ws.RestResponse;
import com.huawei.it.jalor5.ws.service.IRestClientService;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({FcstGlobalParameterUtil.class, CommUtils.class})
class LtsTaskIntegrationServiceTest {

    @InjectMocks
    private LtsTaskIntegrationService ltsTaskIntegrationServiceUnderTest;

    @Mock
    private IRestClientService iRestClientService;
    @Mock
    private ILtsTaskIntegrationDao iLtsTaskIntegrationDao;

    @Mock
    private HttpServletRequest httpServletRequest;
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        testRequestContext.setUserIp("**********");
        Map<String,Object> header = new HashMap<>();
        header.put("jobId","jobId");
        header.put("callback","callback");
        testRequestContext.setItems(header);
        RequestContextManager.setCurrent(testRequestContext);
        setRegistryValue();
    }

    private static void setRegistryValue() {
        RegistryVO task = new RegistryVO();
        task.setName("TASK_FOP_SPART_TO_REFRESH_MANUAL");
        task.setValue("{ \"source\": \"lts\", \"w3c\": \"l00521248\", \"itemName\":\"FOE_MANUAL\", \"taskGroupName\":\"GROUP_FOP_DEV2\", \"TASK_FOP_SPART_TO_REFRESH_MANUAL_DEV2\":\"\", \"url\":\"http://lts.biz.his-beta.huawei.com/lts/service/v2/task\" }");
        task.setParentPath("App.Config.Profits.externalServicesUrl");

        RegistryVO task1 = new RegistryVO();
        task1.setName("TASK_FOP_SPART_KR");
        task1.setValue("{ \"source\": \"lts\", \"w3c\": \"l00521248\", \"itemName\":\"FOE_MANUAL\", \"taskGroupName\":\"GROUP_FOP_DEV2\", \"TASK_FOP_SPART_KR_DEV2\":\"\", \"url\":\"http://lts.biz.his-beta.huawei.com/lts/service/v2/task\" }");
        task1.setParentPath("App.Config.Profits.externalServicesUrl");

        RegistryVO task2 = new RegistryVO();
        task2.setName("ltsStartTaskInfo");
        task2.setValue("{     \"taskId\": \"690021\",     \"source\": \"lts\",     \"w3c\": \"l00521248\",\"url\":\"http://lts.biz.his-beta.huawei.com/lts/service/v1/task/start\" }");
        task2.setParentPath("App.Config.Profits.externalServicesUrl");
        FcstGlobalParameterUtil.putRegistryValue("App.Config.Profits.externalServicesUrl.TASK_FOP_SPART_TO_REFRESH_MANUAL",task);
        FcstGlobalParameterUtil.putRegistryValue("App.Config.Profits.externalServicesUrl.TASK_FOP_SPART_KR",task1);
        FcstGlobalParameterUtil.putRegistryValue("App.Config.Profits.externalServicesUrl.ltsStartTaskInfo",task2);
    }

    @Test
    void testTriggerTask(){
        final CellFuncParamVO functionPattern = new CellFuncParamVO();
        functionPattern.setFuncName("funcName");
        functionPattern.setStatus("---");
        functionPattern.setKey("key");
        functionPattern.setParams(new HashMap<>());
        Map<String,String> result = new HashMap<>();
        result.put("x_success_flag","1");
        when(iLtsTaskIntegrationDao.cellFunction(anyMap())).thenReturn(result);
        final CellFuncParamVO cellFuncParamVO = ltsTaskIntegrationServiceUnderTest.triggerTask(functionPattern);
        Assertions.assertNotNull(cellFuncParamVO.getStatus());
    }

    @Test
    void testTaskStart() throws Exception {
        // Configure IRestClientService.doSecurityGet(...).
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("success",true);
        responseEntity.put("msg","");
        responseEntity.put("data",JSON.parse("{\"name\":\"xxx\",\"lastStatus\":\"正常结束\"}"));
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(200);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityGet(anyString(), any())).thenReturn(restResponse);
        // Run the test
        final CommonResult result = ltsTaskIntegrationServiceUnderTest.taskStart();
        Assertions.assertEquals(result.getHttpCode(),200);
        // Verify the results
    }
    @Test
    void testTaskStart_ApplicationException() throws Exception {
        // Setup
        when(iRestClientService.doSecurityGet(anyString(), any())).thenThrow(CommonApplicationException.class);
        // Run the test
        final CommonResult result = ltsTaskIntegrationServiceUnderTest.taskStart();
        Assertions.assertEquals(result.getHttpCode(),500);
        // Run the test
    }

    @Test
    void testTaskStart_StatusError01() throws Exception {
        // Setup
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("success",true);
        responseEntity.put("msg","");
        responseEntity.put("data",JSON.parse("{\"name\":\"xxx\",\"lastStatus\":\"运行中\"}"));
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(200);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityGet(anyString(), any())).thenReturn(restResponse);
        // Run the test
        final CommonResult result = ltsTaskIntegrationServiceUnderTest.taskStart();
        Assertions.assertEquals(result.getHttpCode(),500);
        // Verify the results
    }

    @Test
    void testTaskStart_StatusError02() throws Exception {
        // Setup
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("success",true);
        responseEntity.put("msg","");
        responseEntity.put("data",JSON.parse("{\"name\":\"xxx\",\"lastStatus\":\"运行中\"}"));
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(500);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityGet(anyString(), any())).thenReturn(restResponse);
        // Run the test
        final CommonResult result = ltsTaskIntegrationServiceUnderTest.taskStart();
        Assertions.assertEquals(result.getHttpCode(),500);
        // Verify the results
    }

    @Test
    void testTaskStart_StatusError03() throws Exception {
        // Setup
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("success",false);
        responseEntity.put("msg","");
        responseEntity.put("data",JSON.parse("{\"name\":\"xxx\",\"lastStatus\":\"运行中\"}"));
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(200);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityGet(anyString(), any())).thenReturn(restResponse);
        // Run the test
        final CommonResult result = ltsTaskIntegrationServiceUnderTest.taskStart();
        Assertions.assertEquals(result.getHttpCode(),500);
        // Verify the results
    }

    @Test
    void testFindTaskStatusError1() throws Exception {
        // Setup
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("success",false);
        responseEntity.put("msg","");
        responseEntity.put("data",JSON.parse("{\"name\":\"xxx\",\"lastStatus\":\"运行中\"}"));
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(200);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityGet(anyString(), any())).thenReturn(restResponse);
        // Run the test
        final CommonResult result = ltsTaskIntegrationServiceUnderTest.findTaskStatus();
        Assertions.assertEquals(result.getHttpCode(),500);
        // Run the test
    }

    @Test
    void testFindTaskStatusError2() throws Exception {
        // Setup
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("success",true);
        responseEntity.put("msg","");
        responseEntity.put("data",JSON.parse("{\"name\":\"xxx\",\"lastStatus\":\"运行中\"}"));
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(200);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityGet(anyString(), any())).thenReturn(restResponse);
        // Run the test
        final CommonResult result = ltsTaskIntegrationServiceUnderTest.findTaskStatus();
        Assertions.assertEquals(result.getHttpCode(),500);
        // Run the test
    }
    @Test
    void testFindTaskStatusError3() throws Exception {
        // Setup
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("success",true);
        responseEntity.put("msg","");
        responseEntity.put("data",JSON.parse("{\"name\":\"xxx\",\"lastStatus\":\"运行中\"}"));
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(500);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityGet(anyString(), any())).thenReturn(restResponse);
        // Run the test
        final CommonResult result = ltsTaskIntegrationServiceUnderTest.findTaskStatus();
        Assertions.assertEquals(result.getHttpCode(),500);
        // Run the test
    }

    @Test
    void testFindTaskStatusError4() throws Exception {
        // Setup
        when(iRestClientService.doSecurityGet(anyString(), any())).thenThrow(CommonApplicationException.class);
        // Run the test
        final CommonResult result = ltsTaskIntegrationServiceUnderTest.findTaskStatus();
        Assertions.assertEquals(result.getHttpCode(),500);
        // Run the test
    }

    @Test
    void testFindTaskStatus() throws Exception {
        // Setup
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("success",true);
        responseEntity.put("msg","");
        responseEntity.put("data",JSON.parse("{\"name\":\"xxx\",\"lastStatus\":\"执行完成\"}"));
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(200);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityGet(anyString(), any())).thenReturn(restResponse);
        // Run the test
        final CommonResult result = ltsTaskIntegrationServiceUnderTest.findTaskStatus();
        Assertions.assertEquals(result.getHttpCode(),200);
        // Run the test
    }

    @Test
    void testExecute() throws Exception {
        Map<String ,String> parma = new HashMap<>();
        parma.put("x_success_flag","1");
        when(iLtsTaskIntegrationDao.cellFunction(anyMap())).thenReturn(parma);
        Whitebox.invokeMethod(ltsTaskIntegrationServiceUnderTest,"execute",parma);
        Assertions.assertNotNull(parma);
    }
    @Test
    void testExecute1() throws Exception {
        Map<String ,String> parma = new HashMap<>();
        parma.put("x_success_flag","2001");
        when(iLtsTaskIntegrationDao.cellFunction(anyMap())).thenReturn(parma);
        Whitebox.invokeMethod(ltsTaskIntegrationServiceUnderTest,"execute",parma);
        Assertions.assertNotNull(parma);
    }
}
