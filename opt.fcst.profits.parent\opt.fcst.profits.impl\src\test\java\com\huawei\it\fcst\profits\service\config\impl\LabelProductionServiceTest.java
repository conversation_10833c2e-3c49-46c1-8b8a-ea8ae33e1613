package com.huawei.it.fcst.profits.service.config.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.huawei.it.fcst.profits.common.user.JalorUserTools;
import com.huawei.it.fcst.profits.common.utils.FileProcessUtis;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.dao.ILabelConfigDao;
import com.huawei.it.fcst.profits.dao.ILabelProductionDao;
import com.huawei.it.fcst.profits.vo.CoaProdInfoVO;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.fcst.profits.vo.HolisticViewProdInfoVO;
import com.huawei.it.fcst.profits.vo.ObjectConfigDataVO;
import com.huawei.it.fcst.profits.vo.PlanComProdInfoVo;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.request.LabelConfigQueryRequest;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityL1InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityL2InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityL3InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityLv1InfoVo;
import com.huawei.it.fcst.profits.vo.response.SpartInfoVo;
import com.huawei.it.jalor5.core.base.FilterVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;
import com.huawei.it.jalor5.security.service.IUserQueryService;

import org.junit.Rule;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.rule.PowerMockRule;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

@PrepareForTest({FileProcessUtis.class})
class LabelProductionServiceTest {

    @Rule
    public PowerMockRule rule = new PowerMockRule();

    @Mock
    private ILabelProductionDao mockILabelProductionDao;

    @Mock
    private ILabelConfigDao mockILabelConfigDao;

    @Mock
    private JalorUserTools mockJalorUserTools;

    @Mock
    private IUserQueryService mockIUserQueryService;

    @Mock
    private HolisticViewConfigDataExporter mockHolisticViewConfigDataExporter;

    @InjectMocks
    private LabelProductionService labelProductionServiceUnderTest;
    @Mock
    private SpartInfoExporter spartInfoExporter;
    private AutoCloseable mockitoCloseable;

    @BeforeEach
    void setUp() {
        mockitoCloseable = openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
        // PowerMockito.suppress(PowerMockito.constructor(ConfigUtil.class));
        // PowerMockito.mockStatic(ConfigUtil.class);
    }

    @AfterEach
    void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    void testQueryIctProductInfoByPage() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        request.setPageIndex(0);
        request.setPageSize(0);
        // Configure ILabelProductionDao.findIctProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setCurPage(10);
        pageVO.setPageSize(0);
        HolisticViewProdInfoVO holisticViewProdInfoVO = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO.setLastUpdatedBy("1236547");
        holisticViewProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO.setArticulationFlag("");
        holisticViewProdInfoVO.setLv0Code("lv0Code");
        holisticViewProdInfoVO.setLv0Name("lv0Name");
        holisticViewProdInfoVO.setErrorFlag("errorFlag");
        holisticViewProdInfoVO.setVersionCode("versionCode");
        HolisticViewProdInfoVO holisticViewProdInfoVO1 = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO1.setArticulationFlag("");
        holisticViewProdInfoVO1.setLv0Code("lv0Code");
        holisticViewProdInfoVO1.setLv0Name("lv0Name");
        holisticViewProdInfoVO1.setErrorFlag("errorFlag");
        holisticViewProdInfoVO1.setVersionCode("versionCode");
        PagedResult<HolisticViewProdInfoVO> holisticViewProdInfoVOPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(holisticViewProdInfoVO, holisticViewProdInfoVO1));
        when(mockILabelProductionDao.findIctProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false))).thenReturn(holisticViewProdInfoVOPagedResult);
        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class))).thenReturn(Arrays.asList(userAccount));
        PagedResult<HolisticViewProdInfoVO> result = labelProductionServiceUnderTest.queryIctProductInfoByPage(request);
        Assertions.assertNotNull(result);
    }

    @Test
    void testQueryIctProductInfoByPageA() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        request.setPageIndex(0);
        request.setPageSize(0);
        // Configure ILabelProductionDao.findIctProductInfoByPage(...).
        PagedResult<HolisticViewProdInfoVO> result = labelProductionServiceUnderTest.queryIctProductInfoByPage(request);
        Assertions.assertNotNull(result);
    }

    @Test
    void testQueryIctProductInfoByPageB() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {"NULL"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        request.setPageIndex(0);
        request.setPageSize(0);
        // Configure ILabelProductionDao.findIctProductInfoByPage(...).
        PagedResult<HolisticViewProdInfoVO> result = labelProductionServiceUnderTest.queryIctProductInfoByPage(request);
        Assertions.assertNotNull(result);
    }

    @Test
    void testQueryIctProductInfoByPageC() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {"lv1Name", "Test"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        request.setPageIndex(0);
        request.setPageSize(0);
        // Configure ILabelProductionDao.findIctProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        HolisticViewProdInfoVO holisticViewProdInfoVO = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO.setLastUpdatedBy("1236547");
        holisticViewProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO.setArticulationFlag("");
        holisticViewProdInfoVO.setLv0Code("lv0Code");
        holisticViewProdInfoVO.setLv0Name("lv0Name");
        holisticViewProdInfoVO.setErrorFlag("errorFlag");
        holisticViewProdInfoVO.setVersionCode("versionCode");
        PagedResult<HolisticViewProdInfoVO> holisticViewProdInfoVOPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(holisticViewProdInfoVO));
        when(mockILabelProductionDao.findIctProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false))).thenReturn(holisticViewProdInfoVOPagedResult);
        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class))).thenReturn(Arrays.asList(userAccount));
        PagedResult<HolisticViewProdInfoVO> result = labelProductionServiceUnderTest.queryIctProductInfoByPage(request);
        Assertions.assertNotNull(result);
    }

    @Test
    void testQueryCoaProductInfoByPage() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findCoaProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        request.setPageIndex(0);
        request.setPageSize(0);
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final PagedResult<CoaProdInfoVO> coaProdInfoVOPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(coaProdInfoVO));
        when(mockILabelProductionDao.findCoaProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false))).thenReturn(coaProdInfoVOPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        // Run the test
        final PagedResult<CoaProdInfoVO> result = labelProductionServiceUnderTest.queryCoaProductInfoByPage(request);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testQueryCoaProductInfoByPageA() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        request.setPageIndex(0);
        request.setPageSize(0);
        // Configure ILabelProductionDao.findIctProductInfoByPage(...).
        final PagedResult<CoaProdInfoVO> result = labelProductionServiceUnderTest.queryCoaProductInfoByPage(request);
        Assertions.assertNotNull(result);
    }
    @Test
    void testQueryCoaProductInfoByPageB() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {"NULL"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        request.setPageIndex(0);
        request.setPageSize(0);
        // Configure ILabelProductionDao.findIctProductInfoByPage(...).
        final PagedResult<CoaProdInfoVO> result = labelProductionServiceUnderTest.queryCoaProductInfoByPage(request);
        Assertions.assertNotNull(result);
    }
    @Test
    void testQueryCoaProductInfoByPageC() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {"lv1Name","TEST"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findCoaProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        request.setPageIndex(0);
        request.setPageSize(0);
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final PagedResult<CoaProdInfoVO> coaProdInfoVOPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(coaProdInfoVO));
        when(mockILabelProductionDao.findCoaProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false))).thenReturn(coaProdInfoVOPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        // Run the test
        final PagedResult<CoaProdInfoVO> result = labelProductionServiceUnderTest.queryCoaProductInfoByPage(request);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    void testQueryPlanComProductInfoByPage() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findPlanComProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        request.setPageSize(0);
        request.setPageIndex(0);
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final PagedResult<PlanComProdInfoVo> planComProdInfoVoPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(planComProdInfoVo));
        when(mockILabelProductionDao.findPlanComProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false), eq(false))).thenReturn(planComProdInfoVoPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        // Run the test
        final PagedResult<PlanComProdInfoVo> result = labelProductionServiceUnderTest.queryPlanComProductInfoByPage(
            request);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testQueryPlanComProductInfoByPageA() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {"NULL"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findPlanComProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        request.setPageSize(0);
        request.setPageIndex(0);
        // Run the test
        final PagedResult<PlanComProdInfoVo> result = labelProductionServiceUnderTest.queryPlanComProductInfoByPage(
            request);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testQueryPlanComProductInfoByPageD() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findPlanComProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        request.setPageSize(0);
        request.setPageIndex(0);
        // Run the test
        final PagedResult<PlanComProdInfoVo> result = labelProductionServiceUnderTest.queryPlanComProductInfoByPage(
            request);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testQueryPlanComProductInfoByPageE() throws Exception {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {"lv1Name","TEST"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findPlanComProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        request.setPageSize(0);
        request.setPageIndex(0);
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final PagedResult<PlanComProdInfoVo> planComProdInfoVoPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(planComProdInfoVo));
        when(mockILabelProductionDao.findPlanComProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false), eq(false))).thenReturn(planComProdInfoVoPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        // Run the test
        final PagedResult<PlanComProdInfoVo> result = labelProductionServiceUnderTest.queryPlanComProductInfoByPage(
            request);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testExportIctProductInfo() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        // Configure ILabelProductionDao.findIctProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setStartIndex(0);
        pageVO.setEndIndex(0);
        pageVO.setTotalRows(0);
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        pageVO.setResultMode(0);
        pageVO.setOrderBy("orderBy");
        final FilterVO filterVO = new FilterVO();
        filterVO.setFn("fn");
        filterVO.setFt("ft");
        filterVO.setFv("fv");
        filterVO.setFr("fr");
        pageVO.setFilters(Arrays.asList(filterVO));
        pageVO.setFilterStr("filterStr");
        final HolisticViewProdInfoVO holisticViewProdInfoVO = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO.setLastUpdatedBy("123456");
        holisticViewProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO.setArticulationFlag("");
        holisticViewProdInfoVO.setLv0Code("lv0Code");
        holisticViewProdInfoVO.setLv0Name("lv0Name");
        holisticViewProdInfoVO.setErrorFlag("errorFlag");
        holisticViewProdInfoVO.setVersionCode("versionCode");
        final PagedResult<HolisticViewProdInfoVO> holisticViewProdInfoVOPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(holisticViewProdInfoVO));
        when(mockILabelProductionDao.findIctProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false))).thenReturn(holisticViewProdInfoVOPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        doThrow(CommonApplicationException.class).when(mockHolisticViewConfigDataExporter)
            .exportData(eq(new HashMap<>()), eq(Arrays.asList("value")), any(HttpServletResponse.class));

        // Run the test
        assertThatThrownBy(
            () -> labelProductionServiceUnderTest.exportIctProductInfo(mockResponse, labelConfigRequest))
            .isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportIctProductInfoA() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Run the test
        assertThatThrownBy(
            () -> labelProductionServiceUnderTest.exportIctProductInfo(mockResponse, labelConfigRequest))
            .isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportIctProductInfoB() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"NULL"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Run the test
        assertThatThrownBy(
            () -> labelProductionServiceUnderTest.exportIctProductInfo(mockResponse, labelConfigRequest))
            .isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportIctProductInfoC() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"lv1Name","TEST"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        // Configure ILabelProductionDao.findIctProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setStartIndex(0);
        pageVO.setEndIndex(0);
        pageVO.setTotalRows(0);
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        pageVO.setResultMode(0);
        pageVO.setOrderBy("orderBy");
        final FilterVO filterVO = new FilterVO();
        filterVO.setFn("fn");
        filterVO.setFt("ft");
        filterVO.setFv("fv");
        filterVO.setFr("fr");
        pageVO.setFilters(Arrays.asList(filterVO));
        pageVO.setFilterStr("filterStr");
        final HolisticViewProdInfoVO holisticViewProdInfoVO = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO.setLastUpdatedBy("123456");
        holisticViewProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO.setArticulationFlag("");
        holisticViewProdInfoVO.setLv0Code("lv0Code");
        holisticViewProdInfoVO.setLv0Name("lv0Name");
        holisticViewProdInfoVO.setErrorFlag("errorFlag");
        holisticViewProdInfoVO.setVersionCode("versionCode");
        final PagedResult<HolisticViewProdInfoVO> holisticViewProdInfoVOPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(holisticViewProdInfoVO));
        when(mockILabelProductionDao.findIctProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false))).thenReturn(holisticViewProdInfoVOPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        doThrow(CommonApplicationException.class).when(mockHolisticViewConfigDataExporter)
            .exportData(eq(new HashMap<>()), eq(Arrays.asList("value")), any(HttpServletResponse.class));

        // Run the test
        assertThatThrownBy(
            () -> labelProductionServiceUnderTest.exportIctProductInfo(mockResponse, labelConfigRequest))
            .isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportIctProductInfoD() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"lv1Name","TEST"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        // Configure ILabelProductionDao.findIctProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setStartIndex(0);
        pageVO.setEndIndex(0);
        pageVO.setTotalRows(0);
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        pageVO.setResultMode(0);
        pageVO.setOrderBy("orderBy");
        final FilterVO filterVO = new FilterVO();
        filterVO.setFn("fn");
        filterVO.setFt("ft");
        filterVO.setFv("fv");
        filterVO.setFr("fr");
        pageVO.setFilters(Arrays.asList(filterVO));
        pageVO.setFilterStr("filterStr");
        final HolisticViewProdInfoVO holisticViewProdInfoVO = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO.setLastUpdatedBy("123456");
        holisticViewProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO.setArticulationFlag("");
        holisticViewProdInfoVO.setLv0Code("lv0Code");
        holisticViewProdInfoVO.setLv0Name("lv0Name");
        holisticViewProdInfoVO.setErrorFlag("errorFlag");
        holisticViewProdInfoVO.setVersionCode("versionCode");
        final PagedResult<HolisticViewProdInfoVO> holisticViewProdInfoVOPagedResult = new PagedResult<>();
        when(mockILabelProductionDao.findIctProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false))).thenReturn(holisticViewProdInfoVOPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        doThrow(CommonApplicationException.class).when(mockHolisticViewConfigDataExporter)
            .exportData(eq(new HashMap<>()), eq(Arrays.asList("value")), any(HttpServletResponse.class));

        // Run the test
        assertThatThrownBy(
            () -> labelProductionServiceUnderTest.exportIctProductInfo(mockResponse, labelConfigRequest))
            .isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportCoaProductInfo() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findCoaProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setStartIndex(0);
        pageVO.setEndIndex(0);
        pageVO.setTotalRows(0);
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        pageVO.setResultMode(0);
        pageVO.setOrderBy("orderBy");
        final FilterVO filterVO = new FilterVO();
        filterVO.setFn("fn");
        filterVO.setFt("ft");
        filterVO.setFv("fv");
        filterVO.setFr("fr");
        pageVO.setFilters(Arrays.asList(filterVO));
        pageVO.setFilterStr("filterStr");
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final PagedResult<CoaProdInfoVO> coaProdInfoVOPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(coaProdInfoVO));
        when(mockILabelProductionDao.findCoaProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false))).thenReturn(coaProdInfoVOPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        doThrow(CommonApplicationException.class).when(mockHolisticViewConfigDataExporter)
            .exportData(eq(new HashMap<>()), eq(Arrays.asList("value")), any(HttpServletResponse.class));

        // Run the test
        assertThatThrownBy(
            () -> labelProductionServiceUnderTest.exportCoaProductInfo(mockResponse, labelConfigRequest))
            .isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportCoaProductInfoA() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Run the test
        assertThatThrownBy(
            () -> labelProductionServiceUnderTest.exportCoaProductInfo(mockResponse, labelConfigRequest))
            .isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportCoaProductInfoB() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"NULL"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Run the test
        assertThatThrownBy(
            () -> labelProductionServiceUnderTest.exportCoaProductInfo(mockResponse, labelConfigRequest))
            .isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportCoaProductInfoC() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"lv1Name","TEST"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findCoaProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setStartIndex(0);
        pageVO.setEndIndex(0);
        pageVO.setTotalRows(0);
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        pageVO.setResultMode(0);
        pageVO.setOrderBy("orderBy");
        final FilterVO filterVO = new FilterVO();
        filterVO.setFn("fn");
        filterVO.setFt("ft");
        filterVO.setFv("fv");
        filterVO.setFr("fr");
        pageVO.setFilters(Arrays.asList(filterVO));
        pageVO.setFilterStr("filterStr");
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final PagedResult<CoaProdInfoVO> coaProdInfoVOPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(coaProdInfoVO));
        when(mockILabelProductionDao.findCoaProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false))).thenReturn(coaProdInfoVOPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        doThrow(CommonApplicationException.class).when(mockHolisticViewConfigDataExporter)
            .exportData(eq(new HashMap<>()), eq(Arrays.asList("value")), any(HttpServletResponse.class));

        // Run the test
        assertThatThrownBy(
            () -> labelProductionServiceUnderTest.exportCoaProductInfo(mockResponse, labelConfigRequest))
            .isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportCoaProductInfoD() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"lv1Name","TEST"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findCoaProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setStartIndex(0);
        pageVO.setEndIndex(0);
        pageVO.setTotalRows(0);
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        pageVO.setResultMode(0);
        pageVO.setOrderBy("orderBy");
        final FilterVO filterVO = new FilterVO();
        filterVO.setFn("fn");
        filterVO.setFt("ft");
        filterVO.setFv("fv");
        filterVO.setFr("fr");
        pageVO.setFilters(Arrays.asList(filterVO));
        pageVO.setFilterStr("filterStr");
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final PagedResult<CoaProdInfoVO> coaProdInfoVOPagedResult = new PagedResult<>();
        when(mockILabelProductionDao.findCoaProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false))).thenReturn(coaProdInfoVOPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        doThrow(CommonApplicationException.class).when(mockHolisticViewConfigDataExporter)
            .exportData(eq(new HashMap<>()), eq(Arrays.asList("value")), any(HttpServletResponse.class));

        // Run the test
        assertThatThrownBy(
            () -> labelProductionServiceUnderTest.exportCoaProductInfo(mockResponse, labelConfigRequest))
            .isInstanceOf(CommonApplicationException.class);
    }

    @Test
    void testExportPlanComProductInfo()
        throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findPlanComProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setStartIndex(0);
        pageVO.setEndIndex(0);
        pageVO.setTotalRows(0);
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        pageVO.setResultMode(0);
        pageVO.setOrderBy("orderBy");
        final FilterVO filterVO = new FilterVO();
        filterVO.setFn("fn");
        filterVO.setFt("ft");
        filterVO.setFv("fv");
        filterVO.setFr("fr");
        pageVO.setFilters(Arrays.asList(filterVO));
        pageVO.setFilterStr("filterStr");
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final PagedResult<PlanComProdInfoVo> planComProdInfoVoPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(planComProdInfoVo));
        when(mockILabelProductionDao.findPlanComProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false), eq(false))).thenReturn(planComProdInfoVoPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        doThrow(CommonApplicationException.class).when(mockHolisticViewConfigDataExporter)
            .exportData(eq(new HashMap<>()), eq(Arrays.asList("value")), any(HttpServletResponse.class));

        // Run the test
        assertThatThrownBy(() -> labelProductionServiceUnderTest.exportPlanComProductInfo(mockResponse,
            labelConfigRequest)).isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportPlanComProductInfoA()
        throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        // Run the test
        assertThatThrownBy(() -> labelProductionServiceUnderTest.exportPlanComProductInfo(mockResponse,
            labelConfigRequest)).isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportPlanComProductInfoB()
        throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"NULL"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        // Run the test
        assertThatThrownBy(() -> labelProductionServiceUnderTest.exportPlanComProductInfo(mockResponse,
            labelConfigRequest)).isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportPlanComProductInfoC() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"TEST","SSSSS"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findPlanComProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setStartIndex(0);
        pageVO.setEndIndex(0);
        pageVO.setTotalRows(0);
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        pageVO.setResultMode(0);
        pageVO.setOrderBy("orderBy");
        final FilterVO filterVO = new FilterVO();
        filterVO.setFn("fn");
        filterVO.setFt("ft");
        filterVO.setFv("fv");
        filterVO.setFr("fr");
        pageVO.setFilters(Arrays.asList(filterVO));
        pageVO.setFilterStr("filterStr");
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final PagedResult<PlanComProdInfoVo> planComProdInfoVoPagedResult = new PagedResult<>(pageVO,
            Arrays.asList(planComProdInfoVo));
        when(mockILabelProductionDao.findPlanComProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false), eq(false))).thenReturn(planComProdInfoVoPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        doThrow(CommonApplicationException.class).when(mockHolisticViewConfigDataExporter)
            .exportData(eq(new HashMap<>()), eq(Arrays.asList("value")), any(HttpServletResponse.class));
        // Run the test
        assertThatThrownBy(() -> labelProductionServiceUnderTest.exportPlanComProductInfo(mockResponse,
            labelConfigRequest)).isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testExportPlanComProductInfoD() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final LabelConfigQueryRequest labelConfigRequest = new LabelConfigQueryRequest(0L, new String[] {"TEST","SSSSS"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure ILabelProductionDao.findPlanComProductInfoByPage(...).
        final PageVO pageVO = new PageVO();
        pageVO.setStartIndex(0);
        pageVO.setEndIndex(0);
        pageVO.setTotalRows(0);
        pageVO.setCurPage(0);
        pageVO.setPageSize(0);
        pageVO.setResultMode(0);
        pageVO.setOrderBy("orderBy");
        final FilterVO filterVO = new FilterVO();
        filterVO.setFn("fn");
        filterVO.setFt("ft");
        filterVO.setFv("fv");
        filterVO.setFr("fr");
        pageVO.setFilters(Arrays.asList(filterVO));
        pageVO.setFilterStr("filterStr");
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final PagedResult<PlanComProdInfoVo> planComProdInfoVoPagedResult = new PagedResult<>();
        when(mockILabelProductionDao.findPlanComProductInfoByPage(any(LabelConfigQueryRequest.class), any(PageVO.class),
            eq(false), eq(false), eq(false))).thenReturn(planComProdInfoVoPagedResult);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));
        doThrow(CommonApplicationException.class).when(mockHolisticViewConfigDataExporter)
            .exportData(eq(new HashMap<>()), eq(Arrays.asList("value")), any(HttpServletResponse.class));
        // Run the test
        assertThatThrownBy(() -> labelProductionServiceUnderTest.exportPlanComProductInfo(mockResponse,
            labelConfigRequest)).isInstanceOf(CommonApplicationException.class);
    }
    @Test
    void testGetIctMonitorCondition() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final HolisticViewProdInfoVO holisticViewProdInfoVO = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO.setLastUpdatedBy("123456");
        holisticViewProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO.setArticulationFlag("");
        holisticViewProdInfoVO.setLv0Code("lv0Code");
        holisticViewProdInfoVO.setLv0Name("lv0Name");
        holisticViewProdInfoVO.setErrorFlag("errorFlag");
        holisticViewProdInfoVO.setVersionCode("versionCode");
        final List<HolisticViewProdInfoVO> expectedResult = Arrays.asList(holisticViewProdInfoVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", true, false, "ALL",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        HashMap<String, HolisticViewConfigDataVO> objectObjectHashMap = new HashMap<>();
        HolisticViewConfigDataVO dataVO = new HolisticViewConfigDataVO();
        dataVO.setLv1Code("STRING");
        dataVO.setLv1Name("STRING");
        objectObjectHashMap.put("STRING",dataVO);
        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(objectObjectHashMap);

        // Configure ILabelProductionDao.getIctMonitorCondition(...).
        final HolisticViewProdInfoVO holisticViewProdInfoVO1 = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO1.setLastUpdatedBy("123456");
        holisticViewProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO1.setArticulationFlag("");
        holisticViewProdInfoVO1.setLv0Code("lv0Code");
        holisticViewProdInfoVO1.setLv0Name("lv0Name");
        holisticViewProdInfoVO1.setErrorFlag("errorFlag");
        holisticViewProdInfoVO1.setVersionCode("versionCode");
        final List<HolisticViewProdInfoVO> holisticViewProdInfoVOS = Arrays.asList(holisticViewProdInfoVO1);
        when(mockILabelProductionDao.getIctMonitorCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(holisticViewProdInfoVOS);

        // Run the test
        final List<HolisticViewProdInfoVO> result = labelProductionServiceUnderTest.getIctMonitorCondition(requestVO);
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetIctMonitorConditionA() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final HolisticViewProdInfoVO holisticViewProdInfoVO = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO.setLastUpdatedBy("123456");
        holisticViewProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO.setArticulationFlag("");
        holisticViewProdInfoVO.setLv0Code("lv0Code");
        holisticViewProdInfoVO.setLv0Name("lv0Name");
        holisticViewProdInfoVO.setErrorFlag("errorFlag");
        holisticViewProdInfoVO.setVersionCode("versionCode");
        final List<HolisticViewProdInfoVO> expectedResult = Arrays.asList(holisticViewProdInfoVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "ALL",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        HashMap<String, HolisticViewConfigDataVO> objectObjectHashMap = new HashMap<>();
        HolisticViewConfigDataVO dataVO = new HolisticViewConfigDataVO();
        dataVO.setLv1Code("STRING");
        dataVO.setLv1Name("STRING");
        objectObjectHashMap.put("STRING",dataVO);
        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(objectObjectHashMap);

        // Configure ILabelProductionDao.getIctMonitorCondition(...).
        final HolisticViewProdInfoVO holisticViewProdInfoVO1 = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO1.setLastUpdatedBy("123456");
        holisticViewProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO1.setArticulationFlag("");
        holisticViewProdInfoVO1.setLv0Code("lv0Code");
        holisticViewProdInfoVO1.setLv0Name("lv0Name");
        holisticViewProdInfoVO1.setErrorFlag("errorFlag");
        holisticViewProdInfoVO1.setVersionCode("versionCode");
        final List<HolisticViewProdInfoVO> holisticViewProdInfoVOS = Arrays.asList(holisticViewProdInfoVO1);
        when(mockILabelProductionDao.getIctMonitorCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(holisticViewProdInfoVOS);

        // Run the test
        final List<HolisticViewProdInfoVO> result = labelProductionServiceUnderTest.getIctMonitorCondition(requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetIctMonitorCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(mockILabelProductionDao.getIctMonitorCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<HolisticViewProdInfoVO> result = labelProductionServiceUnderTest.getIctMonitorCondition(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
    @Test
    void testGetIctMonitorLastUpdated_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        requestVO.setLastUpdatedBy(new Long[]{1250L,});
        when(mockILabelProductionDao.getIctMonitorLastUpdated(any(LabelConfigQueryRequest.class),eq(false),eq(false)))
            .thenReturn(Collections.emptyList());
        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));

        // Run the test
        final List<HolisticViewProdInfoVO> result = labelProductionServiceUnderTest.getIctMonitorLastUpdated(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetIctMonitorVersion() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final HolisticViewProdInfoVO holisticViewProdInfoVO = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO.setLastUpdatedBy("123456");
        holisticViewProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO.setArticulationFlag("");
        holisticViewProdInfoVO.setLv0Code("lv0Code");
        holisticViewProdInfoVO.setLv0Name("lv0Name");
        holisticViewProdInfoVO.setErrorFlag("errorFlag");
        holisticViewProdInfoVO.setVersionCode("versionCode");
        final List<HolisticViewProdInfoVO> expectedResult = Arrays.asList(holisticViewProdInfoVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", true, false, "ALL",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        HashMap<String, HolisticViewConfigDataVO> objectObjectHashMap = new HashMap<>();
        HolisticViewConfigDataVO dataVO = new HolisticViewConfigDataVO();
        dataVO.setLv1Code("STRING");
        dataVO.setLv1Name("STRING");
        objectObjectHashMap.put("STRING",dataVO);
        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(objectObjectHashMap);


        // Configure ILabelProductionDao.getIctMonitorVersion(...).
        final HolisticViewProdInfoVO holisticViewProdInfoVO1 = new HolisticViewProdInfoVO();
        holisticViewProdInfoVO1.setLastUpdatedBy("123456");
        holisticViewProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        holisticViewProdInfoVO1.setArticulationFlag("");
        holisticViewProdInfoVO1.setLv0Code("lv0Code");
        holisticViewProdInfoVO1.setLv0Name("lv0Name");
        holisticViewProdInfoVO1.setErrorFlag("errorFlag");
        holisticViewProdInfoVO1.setVersionCode("versionCode");
        final List<HolisticViewProdInfoVO> holisticViewProdInfoVOS = Arrays.asList(holisticViewProdInfoVO1);
        when(mockILabelProductionDao.getIctMonitorVersion(any(LabelConfigQueryRequest.class)))
            .thenReturn(holisticViewProdInfoVOS);

        // Run the test
        final List<HolisticViewProdInfoVO> result = labelProductionServiceUnderTest.getIctMonitorVersion(requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetIctMonitorVersion_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(mockILabelProductionDao.getIctMonitorVersion(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<HolisticViewProdInfoVO> result = labelProductionServiceUnderTest.getIctMonitorVersion(requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetIctConfigCondition() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final HolisticViewConfigDataVO dataVO = new HolisticViewConfigDataVO();
        dataVO.setLastUpdatedBy("123456");
        dataVO.setLastUpdatedByName("lastUpdatedByName");
        dataVO.setLv1Code("lv1Code");
        dataVO.setLv2Code("lv2Code");
        dataVO.setLv2Name("lv2Name");
        dataVO.setLv3Code("lv3Code");
        dataVO.setLv3Name("lv3Name");
        dataVO.setL1Name("l1Name");
        dataVO.setArticulationFlag("");
        dataVO.setIndustryType("industryType");
        final List<HolisticViewConfigDataVO> expectedResult = Arrays.asList(dataVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getIctConfigCondition(...).
        final HolisticViewConfigDataVO dataVO1 = new HolisticViewConfigDataVO();
        dataVO1.setLastUpdatedBy("123456");
        dataVO1.setLastUpdatedByName("lastUpdatedByName");
        dataVO1.setLv1Code("lv1Code");
        dataVO1.setLv2Code("lv2Code");
        dataVO1.setLv2Name("lv2Name");
        dataVO1.setLv3Code("lv3Code");
        dataVO1.setLv3Name("lv3Name");
        dataVO1.setL1Name("l1Name");
        dataVO1.setArticulationFlag("");
        dataVO1.setIndustryType("industryType");
        final List<HolisticViewConfigDataVO> holisticViewConfigDataVOS = Arrays.asList(dataVO1);
        when(mockILabelProductionDao.getIctConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(holisticViewConfigDataVOS);

        // Run the test
        final List<HolisticViewConfigDataVO> result = labelProductionServiceUnderTest.getIctConfigCondition(requestVO);
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetIctConfigConditionA() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final HolisticViewConfigDataVO dataVO = new HolisticViewConfigDataVO();
        dataVO.setLastUpdatedBy("123456");
        dataVO.setLastUpdatedByName("lastUpdatedByName");
        dataVO.setLv1Code("lv1Code");
        dataVO.setLv2Code("lv2Code");
        dataVO.setLv2Name("lv2Name");
        dataVO.setLv3Code("lv3Code");
        dataVO.setLv3Name("lv3Name");
        dataVO.setL1Name("l1Name");
        dataVO.setArticulationFlag("");
        dataVO.setIndustryType("industryType");
        final List<HolisticViewConfigDataVO> expectedResult = Arrays.asList(dataVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", true, false, "ALL",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getIctConfigCondition(...).
        final HolisticViewConfigDataVO dataVO1 = new HolisticViewConfigDataVO();
        dataVO1.setLastUpdatedBy("123456");
        dataVO1.setLastUpdatedByName("lastUpdatedByName");
        dataVO1.setLv1Code("lv1Code");
        dataVO1.setLv2Code("lv2Code");
        dataVO1.setLv2Name("lv2Name");
        dataVO1.setLv3Code("lv3Code");
        dataVO1.setLv3Name("lv3Name");
        dataVO1.setL1Name("l1Name");
        dataVO1.setArticulationFlag("");
        dataVO1.setIndustryType("industryType");
        final List<HolisticViewConfigDataVO> holisticViewConfigDataVOS = Arrays.asList(dataVO1);
        when(mockILabelProductionDao.getIctConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(holisticViewConfigDataVOS);

        // Run the test
        final List<HolisticViewConfigDataVO> result = labelProductionServiceUnderTest.getIctConfigCondition(requestVO);
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetIctConfigCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(mockILabelProductionDao.getIctConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<HolisticViewConfigDataVO> result = labelProductionServiceUnderTest.getIctConfigCondition(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
    @Test
    void testGetPlanComMonitorCondition() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> expectedResult = Arrays.asList(planComProdInfoVo);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getPlanComMonitorCondition(...).
        final PlanComProdInfoVo planComProdInfoVo1 = new PlanComProdInfoVo();
        planComProdInfoVo1.setLastUpdatedBy("123456");
        planComProdInfoVo1.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo1.setLv0Code("lv0Code");
        planComProdInfoVo1.setLv0Name("lv0Name");
        planComProdInfoVo1.setErrorFlag("errorFlag");
        planComProdInfoVo1.setLv2Name("lv2Name");
        planComProdInfoVo1.setLv3Name("lv3Name");
        planComProdInfoVo1.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> planComProdInfoVos = Arrays.asList(planComProdInfoVo1);
        when(mockILabelProductionDao.getPlanComMonitorCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(planComProdInfoVos);

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComMonitorCondition(requestVO);
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetPlanComMonitorConditionA() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> expectedResult = Arrays.asList(planComProdInfoVo);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", true, false, "ALL",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getPlanComMonitorCondition(...).
        final PlanComProdInfoVo planComProdInfoVo1 = new PlanComProdInfoVo();
        planComProdInfoVo1.setLastUpdatedBy("123456");
        planComProdInfoVo1.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo1.setLv0Code("lv0Code");
        planComProdInfoVo1.setLv0Name("lv0Name");
        planComProdInfoVo1.setErrorFlag("errorFlag");
        planComProdInfoVo1.setLv2Name("lv2Name");
        planComProdInfoVo1.setLv3Name("lv3Name");
        planComProdInfoVo1.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> planComProdInfoVos = Arrays.asList(planComProdInfoVo1);
        when(mockILabelProductionDao.getPlanComMonitorCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(planComProdInfoVos);

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComMonitorCondition(requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetPlanComMonitorCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(mockILabelProductionDao.getPlanComMonitorCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComMonitorCondition(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetPlanComMonitorLastUpdated() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> expectedResult = Arrays.asList(planComProdInfoVo);

        // Configure ILabelProductionDao.getPlanComMonitorLastUpdated(...).
        final PlanComProdInfoVo planComProdInfoVo1 = new PlanComProdInfoVo();
        planComProdInfoVo1.setLastUpdatedBy("123456");
        planComProdInfoVo1.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo1.setLv0Code("lv0Code");
        planComProdInfoVo1.setLv0Name("lv0Name");
        planComProdInfoVo1.setErrorFlag("errorFlag");
        planComProdInfoVo1.setLv2Name("lv2Name");
        planComProdInfoVo1.setLv3Name("lv3Name");
        planComProdInfoVo1.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> planComProdInfoVos = Arrays.asList(planComProdInfoVo1);
        when(mockILabelProductionDao.getPlanComMonitorLastUpdated(any(LabelConfigQueryRequest.class), eq(false), eq(false), eq(false)))
            .thenReturn(planComProdInfoVos);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComMonitorLastUpdated(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPlanComMonitorLastUpdated_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        when(mockILabelProductionDao.getPlanComMonitorLastUpdated(any(LabelConfigQueryRequest.class), eq(false), eq(false), eq(false)))
            .thenReturn(Collections.emptyList());
        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComMonitorLastUpdated(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetPlanComMonitorLastUpdated_IUserQueryServiceReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> expectedResult = Arrays.asList(planComProdInfoVo);

        // Configure ILabelProductionDao.getPlanComMonitorLastUpdated(...).
        final PlanComProdInfoVo planComProdInfoVo1 = new PlanComProdInfoVo();
        planComProdInfoVo1.setLastUpdatedBy("123456");
        planComProdInfoVo1.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo1.setLv0Code("lv0Code");
        planComProdInfoVo1.setLv0Name("lv0Name");
        planComProdInfoVo1.setErrorFlag("errorFlag");
        planComProdInfoVo1.setLv2Name("lv2Name");
        planComProdInfoVo1.setLv3Name("lv3Name");
        planComProdInfoVo1.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> planComProdInfoVos = Arrays.asList(planComProdInfoVo1);
        when(mockILabelProductionDao.getPlanComMonitorLastUpdated(any(LabelConfigQueryRequest.class), eq(false), eq(false), eq(false)))
            .thenReturn(planComProdInfoVos);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComMonitorLastUpdated(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPlanComMonitorVersion() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> expectedResult = Arrays.asList(planComProdInfoVo);

        // Configure ILabelProductionDao.getPlanComMonitorVersion(...).
        final PlanComProdInfoVo planComProdInfoVo1 = new PlanComProdInfoVo();
        planComProdInfoVo1.setLastUpdatedBy("123456");
        planComProdInfoVo1.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo1.setLv0Code("lv0Code");
        planComProdInfoVo1.setLv0Name("lv0Name");
        planComProdInfoVo1.setErrorFlag("errorFlag");
        planComProdInfoVo1.setLv2Name("lv2Name");
        planComProdInfoVo1.setLv3Name("lv3Name");
        planComProdInfoVo1.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> planComProdInfoVos = Arrays.asList(planComProdInfoVo1);
        when(mockILabelProductionDao.getPlanComMonitorVersion(any(LabelConfigQueryRequest.class)))
            .thenReturn(planComProdInfoVos);

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComMonitorVersion(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetPlanComMonitorVersion_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        when(mockILabelProductionDao.getPlanComMonitorVersion(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComMonitorVersion(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetCoaMonitorCondition() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> expectedResult = Arrays.asList(coaProdInfoVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getCoaMonitorCondition(...).
        final CoaProdInfoVO coaProdInfoVO1 = new CoaProdInfoVO();
        coaProdInfoVO1.setLastUpdatedBy("123456");
        coaProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO1.setLv2Name("lv2Name");
        coaProdInfoVO1.setVersionCode("versionCode");
        coaProdInfoVO1.setLv3Name("lv3Name");
        coaProdInfoVO1.setProdName("prodName");
        coaProdInfoVO1.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> coaProdInfoVOS = Arrays.asList(coaProdInfoVO1);
        when(mockILabelProductionDao.getCoaMonitorCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(coaProdInfoVOS);
        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaMonitorCondition(requestVO);
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetCoaMonitorConditionA() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> expectedResult = Arrays.asList(coaProdInfoVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", true, false, "ALL",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getCoaMonitorCondition(...).
        final CoaProdInfoVO coaProdInfoVO1 = new CoaProdInfoVO();
        coaProdInfoVO1.setLastUpdatedBy("123456");
        coaProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO1.setLv2Name("lv2Name");
        coaProdInfoVO1.setVersionCode("versionCode");
        coaProdInfoVO1.setLv3Name("lv3Name");
        coaProdInfoVO1.setProdName("prodName");
        coaProdInfoVO1.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> coaProdInfoVOS = Arrays.asList(coaProdInfoVO1);
        when(mockILabelProductionDao.getCoaMonitorCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(coaProdInfoVOS);
        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaMonitorCondition(requestVO);
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetCoaMonitorCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(mockILabelProductionDao.getCoaMonitorCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaMonitorCondition(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetCoaMonitorLastUpdated() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> expectedResult = Arrays.asList(coaProdInfoVO);

        // Configure ILabelProductionDao.getCoaMonitorLastUpdated(...).
        final CoaProdInfoVO coaProdInfoVO1 = new CoaProdInfoVO();
        coaProdInfoVO1.setLastUpdatedBy("123456");
        coaProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO1.setLv2Name("lv2Name");
        coaProdInfoVO1.setVersionCode("versionCode");
        coaProdInfoVO1.setLv3Name("lv3Name");
        coaProdInfoVO1.setProdName("prodName");
        coaProdInfoVO1.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> coaProdInfoVOS = Arrays.asList(coaProdInfoVO1);
        when(mockILabelProductionDao.getCoaMonitorLastUpdated(any(LabelConfigQueryRequest.class),eq(false) ,eq(false)))
            .thenReturn(coaProdInfoVOS);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));

        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaMonitorLastUpdated(requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetCoaMonitorLastUpdated_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        when(mockILabelProductionDao.getCoaMonitorLastUpdated(any(LabelConfigQueryRequest.class),eq(false) ,eq(false) ))
            .thenReturn(Collections.emptyList());
        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class)))
            .thenReturn(Arrays.asList(userAccount));

        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaMonitorLastUpdated(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetCoaMonitorLastUpdated_IUserQueryServiceReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> expectedResult = Arrays.asList(coaProdInfoVO);

        // Configure ILabelProductionDao.getCoaMonitorLastUpdated(...).
        final CoaProdInfoVO coaProdInfoVO1 = new CoaProdInfoVO();
        coaProdInfoVO1.setLastUpdatedBy("123456");
        coaProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO1.setLv2Name("lv2Name");
        coaProdInfoVO1.setVersionCode("versionCode");
        coaProdInfoVO1.setLv3Name("lv3Name");
        coaProdInfoVO1.setProdName("prodName");
        coaProdInfoVO1.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> coaProdInfoVOS = Arrays.asList(coaProdInfoVO1);
        when(mockILabelProductionDao.getCoaMonitorLastUpdated(any(LabelConfigQueryRequest.class),eq(false) ,eq(false) ))
            .thenReturn(coaProdInfoVOS);

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");
        userAccount.setUserId(123456789L);
        when(mockIUserQueryService.findUsersByMultiId(any(Long[].class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaMonitorLastUpdated(requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetCoaMonitorProductName() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> expectedResult = Arrays.asList(coaProdInfoVO);

        // Configure ILabelProductionDao.getCoaMonitorProductName(...).
        final CoaProdInfoVO coaProdInfoVO1 = new CoaProdInfoVO();
        coaProdInfoVO1.setLastUpdatedBy("123456");
        coaProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO1.setLv2Name("lv2Name");
        coaProdInfoVO1.setVersionCode("versionCode");
        coaProdInfoVO1.setLv3Name("lv3Name");
        coaProdInfoVO1.setProdName("prodName");
        coaProdInfoVO1.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> coaProdInfoVOS = Arrays.asList(coaProdInfoVO1);
        when(mockILabelProductionDao.getCoaMonitorProductName(any(LabelConfigQueryRequest.class)))
            .thenReturn(coaProdInfoVOS);

        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaMonitorProductName(requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetCoaMonitorProductName_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        when(mockILabelProductionDao.getCoaMonitorProductName(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaMonitorProductName(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetObjectAuditCondition() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getObjectAuditCondition(...).
        final SpartProfitingRelationVO spartProfitingRelationVO = new SpartProfitingRelationVO();
        spartProfitingRelationVO.setItemCode("itemCode");
        spartProfitingRelationVO.setL2Name("l2Name");
        spartProfitingRelationVO.setLv1Name("lv1Name");
        spartProfitingRelationVO.setL1CoefficientProb(new BigDecimal("0.00"));
        spartProfitingRelationVO.setLastUpdatedBy(0L);
        spartProfitingRelationVO.setL3Name("l3Name");
        spartProfitingRelationVO.setL3Coefficient(new BigDecimal("0.00"));
        spartProfitingRelationVO.setL3NameProb(new BigDecimal("0.00"));
        spartProfitingRelationVO.setL2Coefficient(new BigDecimal("0.00"));
        spartProfitingRelationVO.setLastUpdateDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        spartProfitingRelationVO.setItemDesc("itemDesc");
        spartProfitingRelationVO.setL1Coefficient(new BigDecimal("0.00"));
        spartProfitingRelationVO.setL2CoefficientProb(new BigDecimal("0.00"));
        spartProfitingRelationVO.setL1Name("l1Name");
        spartProfitingRelationVO.setCreationDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        spartProfitingRelationVO.setCreatedBy(0L);
        spartProfitingRelationVO.setL2NameProb(new BigDecimal("0.00"));
        spartProfitingRelationVO.setDataType("dataType");
        spartProfitingRelationVO.setL3CoefficientProb(new BigDecimal("0.00"));
        spartProfitingRelationVO.setStatus("status");
        spartProfitingRelationVO.setCreatedCn("createdCn");
        spartProfitingRelationVO.setLastUpdateCn("lastUpdateCn");
        final List<SpartProfitingRelationVO> spartProfitingRelationVOS = Arrays.asList(spartProfitingRelationVO);
        when(mockILabelProductionDao.getObjectAuditCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(spartProfitingRelationVOS);

        // Run the test
        final List<SpartProfitingRelationVO> result = labelProductionServiceUnderTest.getObjectAuditCondition(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetObjectAuditConditionA() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", true, false, "ALL",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getObjectAuditCondition(...).
        final SpartProfitingRelationVO spartProfitingRelationVO = new SpartProfitingRelationVO();
        spartProfitingRelationVO.setItemCode("itemCode");
        spartProfitingRelationVO.setL2Name("l2Name");
        spartProfitingRelationVO.setLv1Name("lv1Name");
        spartProfitingRelationVO.setL1CoefficientProb(new BigDecimal("0.00"));
        spartProfitingRelationVO.setLastUpdatedBy(0L);
        spartProfitingRelationVO.setL3Name("l3Name");
        spartProfitingRelationVO.setL3Coefficient(new BigDecimal("0.00"));
        spartProfitingRelationVO.setL3NameProb(new BigDecimal("0.00"));
        spartProfitingRelationVO.setL2Coefficient(new BigDecimal("0.00"));
        spartProfitingRelationVO.setLastUpdateDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        spartProfitingRelationVO.setItemDesc("itemDesc");
        spartProfitingRelationVO.setL1Coefficient(new BigDecimal("0.00"));
        spartProfitingRelationVO.setL2CoefficientProb(new BigDecimal("0.00"));
        spartProfitingRelationVO.setL1Name("l1Name");
        spartProfitingRelationVO.setCreationDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        spartProfitingRelationVO.setCreatedBy(0L);
        spartProfitingRelationVO.setL2NameProb(new BigDecimal("0.00"));
        spartProfitingRelationVO.setDataType("dataType");
        spartProfitingRelationVO.setL3CoefficientProb(new BigDecimal("0.00"));
        spartProfitingRelationVO.setStatus("status");
        spartProfitingRelationVO.setCreatedCn("createdCn");
        spartProfitingRelationVO.setLastUpdateCn("lastUpdateCn");
        final List<SpartProfitingRelationVO> spartProfitingRelationVOS = Arrays.asList(spartProfitingRelationVO);
        when(mockILabelProductionDao.getObjectAuditCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(spartProfitingRelationVOS);

        // Run the test
        final List<SpartProfitingRelationVO> result = labelProductionServiceUnderTest.getObjectAuditCondition(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetObjectAuditCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(mockILabelProductionDao.getObjectAuditCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<SpartProfitingRelationVO> result = labelProductionServiceUnderTest.getObjectAuditCondition(
            requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetObjectConfigCondition() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final ObjectConfigDataVO objectConfigDataVO = new ObjectConfigDataVO();
        objectConfigDataVO.setLastUpdatedBy("123456");
        objectConfigDataVO.setLastUpdatedByName("lastUpdatedByName");
        objectConfigDataVO.setLv1Code("lv1Code");
        objectConfigDataVO.setL1Name("l1Name");
        objectConfigDataVO.setL2Name("l2Name");
        objectConfigDataVO.setL3Name("l3Name");
        final List<ObjectConfigDataVO> expectedResult = Arrays.asList(objectConfigDataVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getObjectConfigCondition(...).
        final ObjectConfigDataVO objectConfigDataVO1 = new ObjectConfigDataVO();
        objectConfigDataVO1.setLastUpdatedBy("123456");
        objectConfigDataVO1.setLastUpdatedByName("lastUpdatedByName");
        objectConfigDataVO1.setLv1Code("lv1Code");
        objectConfigDataVO1.setL1Name("l1Name");
        objectConfigDataVO1.setL2Name("l2Name");
        objectConfigDataVO1.setL3Name("l3Name");
        final List<ObjectConfigDataVO> objectConfigDataVOS = Arrays.asList(objectConfigDataVO1);
        when(mockILabelProductionDao.getObjectConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(objectConfigDataVOS);

        // Run the test
        final List<ObjectConfigDataVO> result = labelProductionServiceUnderTest.getObjectConfigCondition(requestVO);
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetObjectConfigConditionA() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final ObjectConfigDataVO objectConfigDataVO = new ObjectConfigDataVO();
        objectConfigDataVO.setLastUpdatedBy("123456");
        objectConfigDataVO.setLastUpdatedByName("lastUpdatedByName");
        objectConfigDataVO.setLv1Code("lv1Code");
        objectConfigDataVO.setL1Name("l1Name");
        objectConfigDataVO.setL2Name("l2Name");
        objectConfigDataVO.setL3Name("l3Name");
        final List<ObjectConfigDataVO> expectedResult = Arrays.asList(objectConfigDataVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", true, false, "ALL",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getObjectConfigCondition(...).
        final ObjectConfigDataVO objectConfigDataVO1 = new ObjectConfigDataVO();
        objectConfigDataVO1.setLastUpdatedBy("123456");
        objectConfigDataVO1.setLastUpdatedByName("lastUpdatedByName");
        objectConfigDataVO1.setLv1Code("lv1Code");
        objectConfigDataVO1.setL1Name("l1Name");
        objectConfigDataVO1.setL2Name("l2Name");
        objectConfigDataVO1.setL3Name("l3Name");
        final List<ObjectConfigDataVO> objectConfigDataVOS = Arrays.asList(objectConfigDataVO1);
        when(mockILabelProductionDao.getObjectConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(objectConfigDataVOS);

        // Run the test
        final List<ObjectConfigDataVO> result = labelProductionServiceUnderTest.getObjectConfigCondition(requestVO);
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetObjectConfigCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(mockILabelProductionDao.getObjectConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<ObjectConfigDataVO> result = labelProductionServiceUnderTest.getObjectConfigCondition(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetPlanComConfigCondition() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> expectedResult = Arrays.asList(planComProdInfoVo);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getPlanComConfigCondition(...).
        final PlanComProdInfoVo planComProdInfoVo1 = new PlanComProdInfoVo();
        planComProdInfoVo1.setLastUpdatedBy("123456");
        planComProdInfoVo1.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo1.setLv0Code("lv0Code");
        planComProdInfoVo1.setLv0Name("lv0Name");
        planComProdInfoVo1.setErrorFlag("errorFlag");
        planComProdInfoVo1.setLv2Name("lv2Name");
        planComProdInfoVo1.setLv3Name("lv3Name");
        planComProdInfoVo1.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> planComProdInfoVos = Arrays.asList(planComProdInfoVo1);
        when(mockILabelProductionDao.getPlanComConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(planComProdInfoVos);

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComConfigCondition(requestVO);
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetPlanComConfigConditionA() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final PlanComProdInfoVo planComProdInfoVo = new PlanComProdInfoVo();
        planComProdInfoVo.setLastUpdatedBy("123456");
        planComProdInfoVo.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo.setLv0Code("lv0Code");
        planComProdInfoVo.setLv0Name("lv0Name");
        planComProdInfoVo.setErrorFlag("errorFlag");
        planComProdInfoVo.setLv2Name("lv2Name");
        planComProdInfoVo.setLv3Name("lv3Name");
        planComProdInfoVo.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> expectedResult = Arrays.asList(planComProdInfoVo);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", true, false, "ALL",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getPlanComConfigCondition(...).
        final PlanComProdInfoVo planComProdInfoVo1 = new PlanComProdInfoVo();
        planComProdInfoVo1.setLastUpdatedBy("123456");
        planComProdInfoVo1.setLastUpdatedByName("lastUpdatedByName");
        planComProdInfoVo1.setLv0Code("lv0Code");
        planComProdInfoVo1.setLv0Name("lv0Name");
        planComProdInfoVo1.setErrorFlag("errorFlag");
        planComProdInfoVo1.setLv2Name("lv2Name");
        planComProdInfoVo1.setLv3Name("lv3Name");
        planComProdInfoVo1.setVersionCode("versionCode");
        final List<PlanComProdInfoVo> planComProdInfoVos = Arrays.asList(planComProdInfoVo1);
        when(mockILabelProductionDao.getPlanComConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(planComProdInfoVos);

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComConfigCondition(requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetPlanComConfigCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(mockILabelProductionDao.getPlanComConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<PlanComProdInfoVo> result = labelProductionServiceUnderTest.getPlanComConfigCondition(requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetCoaConfigCondition() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> expectedResult = Arrays.asList(coaProdInfoVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getCoaConfigCondition(...).
        final CoaProdInfoVO coaProdInfoVO1 = new CoaProdInfoVO();
        coaProdInfoVO1.setLastUpdatedBy("123456");
        coaProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO1.setLv2Name("lv2Name");
        coaProdInfoVO1.setVersionCode("versionCode");
        coaProdInfoVO1.setLv3Name("lv3Name");
        coaProdInfoVO1.setProdName("prodName");
        coaProdInfoVO1.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> coaProdInfoVOS = Arrays.asList(coaProdInfoVO1);
        when(mockILabelProductionDao.getCoaConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(coaProdInfoVOS);

        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaConfigCondition(requestVO);
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetCoaConfigConditionA() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");
        final CoaProdInfoVO coaProdInfoVO = new CoaProdInfoVO();
        coaProdInfoVO.setLastUpdatedBy("123456");
        coaProdInfoVO.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO.setLv2Name("lv2Name");
        coaProdInfoVO.setVersionCode("versionCode");
        coaProdInfoVO.setLv3Name("lv3Name");
        coaProdInfoVO.setProdName("prodName");
        coaProdInfoVO.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> expectedResult = Arrays.asList(coaProdInfoVO);

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", true, false, "ALL",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getCoaConfigCondition(...).
        final CoaProdInfoVO coaProdInfoVO1 = new CoaProdInfoVO();
        coaProdInfoVO1.setLastUpdatedBy("123456");
        coaProdInfoVO1.setLastUpdatedByName("lastUpdatedByName");
        coaProdInfoVO1.setLv2Name("lv2Name");
        coaProdInfoVO1.setVersionCode("versionCode");
        coaProdInfoVO1.setLv3Name("lv3Name");
        coaProdInfoVO1.setProdName("prodName");
        coaProdInfoVO1.setErrorFlag("errorFlag");
        final List<CoaProdInfoVO> coaProdInfoVOS = Arrays.asList(coaProdInfoVO1);
        when(mockILabelProductionDao.getCoaConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(coaProdInfoVOS);

        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaConfigCondition(requestVO);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetCoaConfigCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final LabelConfigQueryRequest requestVO = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(mockILabelProductionDao.getCoaConfigCondition(any(LabelConfigQueryRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<CoaProdInfoVO> result = labelProductionServiceUnderTest.getCoaConfigCondition(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetUserProduction() {
        // Setup
        final LabelConfigQueryRequest request = new LabelConfigQueryRequest(0L, new String[] {"lv1Name"},
            new String[] {"lv1Code"}, new String[] {"lv2Name"}, new String[] {"lv2Code"}, new String[] {"lv3Name"},
            new String[] {"lv3Code"}, new String[] {"l1Name"}, new String[] {"l1Code"}, new String[] {"l2Name"},
            new String[] {"l2Code"}, new String[] {"l3Name"}, new String[] {"l3Code"}, new String[] {"planComLv1"},
            new String[] {"planComLv2"}, new String[] {"planComLv3"}, new String[] {"busiLv4"},
            new String[] {"coaCode"}, new String[] {"articulationFlag"}, "dataType", "status", 0, new Long[] {0L}, 0L,
            Arrays.asList("value"), "versionCode", new String[] {"lv0Name"}, new String[] {"prodName"},"ssss",Arrays.asList("value"),new String[] {"Y"},"test","test");

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO(0, "roleName", false, false, "lv1DataType",
            "bgDataType", new HashSet<>(Arrays.asList("value")), new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Run the test
        final List<String> result = labelProductionServiceUnderTest.getUserProduction(request);
        Assertions.assertNotNull(request);

    }

    @Test
    void testGetProfitExaminingLv1QueryCondition() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getProfitExaminingLv1QueryCondition(...).
        final ProfitGranularityL3InfoVo krCpfL2Response = new ProfitGranularityL3InfoVo();
        krCpfL2Response.setLv1Code("lv1Code");
        krCpfL2Response.setBgCode("bgCode");
        krCpfL2Response.setL1Name("l1Name");
        krCpfL2Response.setL2Name("l2Name");
        krCpfL2Response.setLv1Name("lv1Name");
        final List<ProfitGranularityL3InfoVo> krCpfL2Responses = Arrays.asList(krCpfL2Response);
        when(
            mockILabelProductionDao.getProfitExaminingLv1QueryCondition(any(ForecastsRequest.class)))
            .thenReturn(krCpfL2Responses);

        // Run the test
        final List<ProfitGranularityL3InfoVo>result = labelProductionServiceUnderTest.getProfitExaminingLv1QueryCondition(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetProfitExaminingLv1QueryConditionA() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setFcstType("halfYear");
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getProfitExaminingLv1QueryCondition(...).
        final ProfitGranularityL3InfoVo krCpfL2Response = new ProfitGranularityL3InfoVo();
        krCpfL2Response.setLv1Code("lv1Code");
        krCpfL2Response.setBgCode("bgCode");
        krCpfL2Response.setL1Name("l1Name");
        krCpfL2Response.setL2Name("l2Name");
        krCpfL2Response.setLv1Name("lv1Name");
        final List<ProfitGranularityL3InfoVo> krCpfL2Responses = Arrays.asList(krCpfL2Response);
        when(
            mockILabelProductionDao.getProfitExaminingLv1QueryCondition(any(ForecastsRequest.class)))
            .thenReturn(krCpfL2Responses);

        // Run the test
        final List<ProfitGranularityL3InfoVo>result = labelProductionServiceUnderTest.getProfitExaminingLv1QueryCondition(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetProfitExaminingLv1QueryConditionB() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setFcstType("ytd");
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getProfitExaminingLv1QueryCondition(...).
        final ProfitGranularityL3InfoVo krCpfL2Response = new ProfitGranularityL3InfoVo();
        krCpfL2Response.setLv1Code("lv1Code");
        krCpfL2Response.setBgCode("bgCode");
        krCpfL2Response.setL1Name("l1Name");
        krCpfL2Response.setL2Name("l2Name");
        krCpfL2Response.setLv1Name("lv1Name");
        final List<ProfitGranularityL3InfoVo> krCpfL2Responses = Arrays.asList(krCpfL2Response);
        when(
            mockILabelProductionDao.getProfitExaminingLv1QueryCondition(any(ForecastsRequest.class)))
            .thenReturn(krCpfL2Responses);

        // Run the test
        final List<ProfitGranularityL3InfoVo>result = labelProductionServiceUnderTest.getProfitExaminingLv1QueryCondition(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetProfitExaminingLv1QueryConditionC() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setFcstType("totalYear");
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getProfitExaminingLv1QueryCondition(...).
        final ProfitGranularityL3InfoVo krCpfL2Response = new ProfitGranularityL3InfoVo();
        krCpfL2Response.setLv1Code("lv1Code");
        krCpfL2Response.setBgCode("bgCode");
        krCpfL2Response.setL1Name("l1Name");
        krCpfL2Response.setL2Name("l2Name");
        krCpfL2Response.setLv1Name("lv1Name");
        final List<ProfitGranularityL3InfoVo> krCpfL2Responses = Arrays.asList(krCpfL2Response);
        when(
            mockILabelProductionDao.getProfitExaminingLv1QueryCondition(any(ForecastsRequest.class)))
            .thenReturn(krCpfL2Responses);

        // Run the test
        final List<ProfitGranularityL3InfoVo>result = labelProductionServiceUnderTest.getProfitExaminingLv1QueryCondition(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetProfitExaminingLv1QueryConditionD() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setFcstType("season");
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getProfitExaminingLv1QueryCondition(...).
        final ProfitGranularityL3InfoVo krCpfL2Response = new ProfitGranularityL3InfoVo();
        krCpfL2Response.setLv1Code("lv1Code");
        krCpfL2Response.setBgCode("bgCode");
        krCpfL2Response.setL1Name("l1Name");
        krCpfL2Response.setL2Name("l2Name");
        krCpfL2Response.setLv1Name("lv1Name");
        final List<ProfitGranularityL3InfoVo> krCpfL2Responses = Arrays.asList(krCpfL2Response);
        when(
            mockILabelProductionDao.getProfitExaminingLv1QueryCondition(any(ForecastsRequest.class)))
            .thenReturn(krCpfL2Responses);

        // Run the test
        final List<ProfitGranularityL3InfoVo>result = labelProductionServiceUnderTest.getProfitExaminingLv1QueryCondition(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    void testGetProfitExaminingLv1QueryCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
       final ForecastsRequest requestVO = new ForecastsRequest();

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(
            mockILabelProductionDao.getProfitExaminingLv1QueryCondition(any(ForecastsRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<ProfitGranularityL3InfoVo> result = labelProductionServiceUnderTest.getProfitExaminingLv1QueryCondition(
            requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetProfitExaminingL1QueryCondition() throws Exception {
        // Setup
       final ForecastsRequest requestVO = new ForecastsRequest();

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getProfitExaminingLv1QueryCondition(...).
        final ProfitGranularityL3InfoVo krCpfL2Response = new ProfitGranularityL3InfoVo();
        krCpfL2Response.setLv1Code("lv1Code");
        krCpfL2Response.setBgCode("bgCode");
        krCpfL2Response.setL1Name("l1Name");
        krCpfL2Response.setL2Name("l2Name");
        krCpfL2Response.setLv1Name("lv1Name");
        final List<ProfitGranularityL3InfoVo> krCpfL2Responses = Arrays.asList(krCpfL2Response);
        when(
            mockILabelProductionDao.getProfitExaminingLv1QueryCondition(any(ForecastsRequest.class)))
            .thenReturn(krCpfL2Responses);

        // Run the test
        final List<ProfitGranularityL3InfoVo> result = labelProductionServiceUnderTest.getProfitExaminingL1QueryCondition(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetProfitExaminingL1QueryCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
       final ForecastsRequest requestVO = new ForecastsRequest();

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(
            mockILabelProductionDao.getProfitExaminingLv1QueryCondition(any(ForecastsRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<ProfitGranularityL3InfoVo> result = labelProductionServiceUnderTest.getProfitExaminingL1QueryCondition(
            requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetProfitExaminingL2QueryCondition() throws Exception {
        // Setup
       final ForecastsRequest requestVO = new ForecastsRequest();

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Configure ILabelProductionDao.getProfitExaminingLv1QueryCondition(...).
        final ProfitGranularityL3InfoVo krCpfL2Response = new ProfitGranularityL3InfoVo();
        krCpfL2Response.setLv1Code("lv1Code");
        krCpfL2Response.setBgCode("bgCode");
        krCpfL2Response.setL1Name("l1Name");
        krCpfL2Response.setL2Name("l2Name");
        krCpfL2Response.setLv1Name("lv1Name");
        final List<ProfitGranularityL3InfoVo> krCpfL2Responses = Arrays.asList(krCpfL2Response);
        when(
            mockILabelProductionDao.getProfitExaminingLv1QueryCondition(any(ForecastsRequest.class)))
            .thenReturn(krCpfL2Responses);

        // Run the test
        final List<ProfitGranularityL3InfoVo> result = labelProductionServiceUnderTest.getProfitExaminingL2QueryCondition(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetProfitExaminingL2QueryCondition_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
       final ForecastsRequest requestVO = new ForecastsRequest();

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());
        when(
            mockILabelProductionDao.getProfitExaminingLv1QueryCondition(any(ForecastsRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<ProfitGranularityL3InfoVo> result = labelProductionServiceUnderTest.getProfitExaminingL2QueryCondition(
            requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetProfitExaminingLv1MonthInfo() throws Exception {
        // Setup
        final ForecastsRequest request = new ForecastsRequest();
        request.setPageIndex(0);
        request.setPageSize(0);
        request.setFcstStep("fcstStep");
        request.setBgCode(new String[] {"bgCode"});
        request.setBgCodes(Arrays.asList("value"));
        request.setTargetPeriods(Arrays.asList("value"));
        request.setPredictionType("predictionType");
        request.setFcstStepPeriod("fcstStepPeriod");
        request.setActStepPeriod("actStepPeriod");
        request.setLevel("level");
        request.setCurYear(2020);
        request.setCurMonth(1);
        request.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        request.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingLv1MonthInfo(...).
        final ProfitGranularityLv1InfoVo krCpfLv1AggrResponse = new ProfitGranularityLv1InfoVo();
        final List<ProfitGranularityLv1InfoVo> krCpfLv1AggrResponses = Arrays.asList(krCpfLv1AggrResponse);
        when(mockILabelProductionDao.getProfitExaminingLv1MonthInfo(any(ForecastsRequest.class)))
            .thenReturn(krCpfLv1AggrResponses);
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);
        Map<String, HolisticViewConfigDataVO>testMap  = new HashMap();
        HolisticViewConfigDataVO vo = new HolisticViewConfigDataVO();
        vo.setLv1Name("test");
        testMap.put("test",vo);
        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(testMap);
        // Run the test
        final Map<String,List<ProfitGranularityLv1InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingLv1MonthInfo(
            request);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetProfitExaminingLv1MonthInfo_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final ForecastsRequest request = new ForecastsRequest();
        request.setPageIndex(0);
        request.setPageSize(0);
        request.setFcstStep("fcstStep");
        request.setBgCode(new String[] {"bgCode"});
        request.setBgCodes(Arrays.asList("value"));
        request.setTargetPeriods(Arrays.asList("value"));
        request.setPredictionType("predictionType");
        request.setFcstStepPeriod("fcstStepPeriod");
        request.setActStepPeriod("actStepPeriod");
        request.setLevel("level");
        request.setCurYear(2020);
        request.setCurMonth(1);
        request.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        request.setRightVO(rightVO);

        when(mockILabelProductionDao.getProfitExaminingLv1MonthInfo(any(ForecastsRequest.class)))
            .thenReturn(Collections.emptyList());
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);
        // Run the test
        final Map<String,List<ProfitGranularityLv1InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingLv1MonthInfo(
            request);
        Map<String, HolisticViewConfigDataVO>testMap  = new HashMap();
        HolisticViewConfigDataVO vo = new HolisticViewConfigDataVO();
        vo.setLv1Name("test");
        testMap.put("test",vo);
        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(testMap);
        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyMap());
    }

    @Test
    void testGetProfitExaminingLv1YtdInfo() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);
        // Configure ILabelProductionDao.getProfitExaminingLv1YtdInfo(...).
        final ProfitGranularityLv1InfoVo krCpfLv1AggrResponse = new ProfitGranularityLv1InfoVo();
        final List<ProfitGranularityLv1InfoVo> krCpfLv1AggrResponses = Arrays.asList(krCpfLv1AggrResponse);
        when(mockILabelProductionDao.getProfitExaminingLv1YtdInfo(any(ForecastsRequest.class)))
            .thenReturn(krCpfLv1AggrResponses);
        Map<String, HolisticViewConfigDataVO>testMap  = new HashMap();
        HolisticViewConfigDataVO vo = new HolisticViewConfigDataVO();
        vo.setLv1Name("test");
        testMap.put("test",vo);
        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(testMap);
        // Run the test
        final Map<String, List<ProfitGranularityLv1InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingLv1YtdInfo(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetProfitExaminingLv1YtdInfo_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);
        when(mockILabelProductionDao.getProfitExaminingLv1YtdInfo(any(ForecastsRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, List<ProfitGranularityLv1InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingLv1YtdInfo(
            requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyMap());
    }

    @Test
    void testGetProfitExaminingL1MonthInfo() {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingL1MonthInfo(...).
        final ProfitGranularityL1InfoVo krCpfL1Response = new ProfitGranularityL1InfoVo();
        krCpfL1Response.setLv1Code("lv1Code");
        krCpfL1Response.setTargetPeriod("targetPeriod");
        krCpfL1Response.setCarryoverRatio(new BigDecimal("0.00"));
        krCpfL1Response.setBgCode("bgCode");
        krCpfL1Response.setL1Name("l1Name");
        final List<ProfitGranularityL1InfoVo> krCpfL1Responses = Arrays.asList(krCpfL1Response);
        when(mockILabelProductionDao.getProfitExaminingL1MonthInfo(any(ForecastsRequest.class)))
            .thenReturn(krCpfL1Responses);

        // Run the test
        final Map<String,List<ProfitGranularityL1InfoVo>>result = labelProductionServiceUnderTest.getProfitExaminingL1MonthInfo(requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetProfitExaminingL1MonthInfo_ILabelProductionDaoReturnsNoItems() {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        when(mockILabelProductionDao.getProfitExaminingL1MonthInfo(any(ForecastsRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final Map<String,List<ProfitGranularityL1InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingL1MonthInfo(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyMap());
    }

    @Test
    void testGetProfitExaminingL1YtdInfo() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingL1YtdInfo(...).
        final ProfitGranularityL1InfoVo krCpfL1Response = new ProfitGranularityL1InfoVo();
        krCpfL1Response.setLv1Code("lv1Code");
        krCpfL1Response.setTargetPeriod("targetPeriod");
        krCpfL1Response.setCarryoverRatio(new BigDecimal("0.00"));
        krCpfL1Response.setBgCode("bgCode");
        krCpfL1Response.setL1Name("l1Name");
        final List<ProfitGranularityL1InfoVo> krCpfL1Responses = Arrays.asList(krCpfL1Response);
        when(mockILabelProductionDao.getProfitExaminingL1YtdInfo(any(ForecastsRequest.class)))
            .thenReturn(krCpfL1Responses);

        // Run the test
        final Map<String, List<ProfitGranularityL1InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingL1YtdInfo(requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetProfitExaminingL1YtdInfo_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        when(mockILabelProductionDao.getProfitExaminingL1YtdInfo(any(ForecastsRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, List<ProfitGranularityL1InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingL1YtdInfo(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyMap());
    }

    @Test
    void testGetProfitExaminingL2MonthInfo() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingL2MonthInfo(...).
        final ProfitGranularityL2InfoVo profitGranularityL2InfoVo = new ProfitGranularityL2InfoVo();
        profitGranularityL2InfoVo.setLv1Code("lv1Code");
        profitGranularityL2InfoVo.setBgCode("bgCode");
        profitGranularityL2InfoVo.setL1Name("l1Name");
        profitGranularityL2InfoVo.setL2Name("l2Name");
        profitGranularityL2InfoVo.setLv1Name("lv1Name");
        final List<ProfitGranularityL2InfoVo> profitGranularityL2InfoVos = Arrays.asList(profitGranularityL2InfoVo);
        when(mockILabelProductionDao.getProfitExaminingL2MonthInfo(any(ForecastsRequest.class)))
            .thenReturn(profitGranularityL2InfoVos);

        // Run the test
        final Map<String, List<ProfitGranularityL2InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingL2MonthInfo(
            requestVO,Boolean.FALSE);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    void testDownloadProfitExaminingSpartInfo() throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final ForecastsRequest request = new ForecastsRequest();
        request.setPageIndex(0);
        request.setPageSize(0);
        request.setPeriodId(0L);
        request.setFcstStep("fcstStep");
        request.setFcstType("fcstType");
        request.setBgCode(new String[] {"bgCode"});
        request.setBgCodes(Arrays.asList("value"));
        request.setTargetPeriods(Arrays.asList("value"));
        request.setPredictionType("predictionType");
        request.setFcstStepPeriod("fcstStepPeriod");
        request.setActStepPeriod("actStepPeriod");
        request.setLevel("level");
        request.setSpecPeriods(Arrays.asList("value"));
        request.setCurYear(2020);
        request.setCurMonth(1);
        request.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        request.setRightVO(rightVO);
        request.setOrderField("orderField");
        final SpartInfoVo infoVo = new SpartInfoVo();
        infoVo.setRevPercent(new BigDecimal("0.00"));
        infoVo.setCostPercent("0.00");
        request.setCostItems(Arrays.asList(infoVo));
        final SpartInfoVo infoVo1 = new SpartInfoVo();
        infoVo1.setRevPercent(new BigDecimal("0.00"));
        infoVo1.setCostPercent("0.00");
        request.setRevItems(Arrays.asList(infoVo1));

        // Configure ILabelProductionDao.getProfitExaminingL1UnionAllInfo(...).
        final ProfitGranularityL1InfoVo profitGranularityL1InfoVo = new ProfitGranularityL1InfoVo();
        profitGranularityL1InfoVo.setLv1Code("lv1Code");
        profitGranularityL1InfoVo.setBgCode("bgCode");
        profitGranularityL1InfoVo.setL1Name("l1Name");
        profitGranularityL1InfoVo.setTargetPeriod("targetPeriod");
        profitGranularityL1InfoVo.setPeriodId(0L);
        final List<ProfitGranularityL1InfoVo> profitGranularityL1InfoVos = Arrays.asList(profitGranularityL1InfoVo);
        when(mockILabelProductionDao.getProfitExaminingL1UnionAllInfo(any(ForecastsRequest.class)))
            .thenReturn(profitGranularityL1InfoVos);

        // Configure ILabelProductionDao.getProfitExaminingL2UnionAllInfo(...).
        final ProfitGranularityL2InfoVo profitGranularityL2InfoVo = new ProfitGranularityL2InfoVo();
        profitGranularityL2InfoVo.setLv1Code("lv1Code");
        profitGranularityL2InfoVo.setBgCode("bgCode");
        profitGranularityL2InfoVo.setL1Name("l1Name");
        profitGranularityL2InfoVo.setTargetPeriod("targetPeriod");
        profitGranularityL2InfoVo.setPeriodId(0L);
        final List<ProfitGranularityL2InfoVo> profitGranularityL2InfoVos = Arrays.asList(profitGranularityL2InfoVo);
        when(mockILabelProductionDao.getProfitExaminingL2UnionAllInfo(any(ForecastsRequest.class)))
            .thenReturn(profitGranularityL2InfoVos);

        // Run the test
        // Verify the results
        assertThatThrownBy(() -> labelProductionServiceUnderTest.downloadProfitExaminingSpartInfo(mockResponse,
            request)).isInstanceOf(CommonApplicationException.class);
    }

    @Test
    void testDownloadProfitExaminingSpartInfo_ILabelProductionDaoGetProfitExaminingL2UnionAllInfoReturnsNoItems()
        throws Exception {
        // Setup
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        final ForecastsRequest request = new ForecastsRequest();
        request.setPageIndex(0);
        request.setPageSize(0);
        request.setPeriodId(0L);
        request.setFcstStep("fcstStep");
        request.setFcstType("fcstType");
        request.setBgCode(new String[] {"bgCode"});
        request.setBgCodes(Arrays.asList("value"));
        request.setTargetPeriods(Arrays.asList("value"));
        request.setPredictionType("predictionType");
        request.setFcstStepPeriod("fcstStepPeriod");
        request.setActStepPeriod("actStepPeriod");
        request.setLevel("level");
        request.setSpecPeriods(Arrays.asList("value"));
        request.setCurYear(2020);
        request.setCurMonth(1);
        request.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        request.setRightVO(rightVO);
        request.setOrderField("orderField");
        final SpartInfoVo infoVo = new SpartInfoVo();
        infoVo.setRevPercent(new BigDecimal("0.00"));
        infoVo.setCostPercent("0.00");
        request.setCostItems(Arrays.asList(infoVo));
        final SpartInfoVo infoVo1 = new SpartInfoVo();
        infoVo1.setRevPercent(new BigDecimal("0.00"));
        infoVo1.setCostPercent("0.00");
        request.setRevItems(Arrays.asList(infoVo1));

        // Configure ILabelProductionDao.getProfitExaminingL1UnionAllInfo(...).
        final ProfitGranularityL1InfoVo profitGranularityL1InfoVo = new ProfitGranularityL1InfoVo();
        profitGranularityL1InfoVo.setLv1Code("lv1Code");
        profitGranularityL1InfoVo.setBgCode("bgCode");
        profitGranularityL1InfoVo.setL1Name("l1Name");
        profitGranularityL1InfoVo.setTargetPeriod("targetPeriod");
        profitGranularityL1InfoVo.setPeriodId(0L);
        final List<ProfitGranularityL1InfoVo> profitGranularityL1InfoVos = Arrays.asList(profitGranularityL1InfoVo);
        when(mockILabelProductionDao.getProfitExaminingL1UnionAllInfo(any(ForecastsRequest.class)))
            .thenReturn(profitGranularityL1InfoVos);

        when(mockILabelProductionDao.getProfitExaminingL2UnionAllInfo(any(ForecastsRequest.class)))
            .thenReturn(Collections.emptyList());
        PowerMockito.doNothing().when(spartInfoExporter).exportData(any(),any(),any());
        // Verify the results
        assertThatThrownBy(() -> labelProductionServiceUnderTest.downloadProfitExaminingSpartInfo(mockResponse,
            request)).isInstanceOf(CommonApplicationException.class);
    }

    @Test
    void testGetProfitExaminingL2MonthInfo_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        when(mockILabelProductionDao.getProfitExaminingL2MonthInfo(any(ForecastsRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, List<ProfitGranularityL2InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingL2MonthInfo(
            requestVO,Boolean.FALSE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyMap());
    }

    @Test
    void testGetProfitExaminingL2YtdInfo() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingL2YtdInfo(...).
        final ProfitGranularityL2InfoVo profitGranularityL2InfoVo = new ProfitGranularityL2InfoVo();
        profitGranularityL2InfoVo.setLv1Code("lv1Code");
        profitGranularityL2InfoVo.setBgCode("bgCode");
        profitGranularityL2InfoVo.setL1Name("l1Name");
        profitGranularityL2InfoVo.setL2Name("l2Name");
        profitGranularityL2InfoVo.setLv1Name("lv1Name");
        final List<ProfitGranularityL2InfoVo> profitGranularityL2InfoVos = Arrays.asList(profitGranularityL2InfoVo);
        when(mockILabelProductionDao.getProfitExaminingL2YtdInfo(any(ForecastsRequest.class)))
            .thenReturn(profitGranularityL2InfoVos);

        // Run the test
        final Map<String, List<ProfitGranularityL2InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingL2YtdInfo(
            requestVO,Boolean.FALSE);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetProfitExaminingL2YtdInfo_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        when(mockILabelProductionDao.getProfitExaminingL2YtdInfo(any(ForecastsRequest.class)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, List<ProfitGranularityL2InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingL2YtdInfo(
            requestVO,Boolean.FALSE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyMap());
    }

    @Test
    void testGetUserProductionA() {
        // Setup
        final LabelConfigQueryRequest request = LabelConfigQueryRequest.builder()
            .periodId(0L)
            .lv1Name(new String[] {"lv1Name"})
            .l1Name(new String[] {"l1Name"})
            .l2Name(new String[] {"l2Name"})
            .busiLv4(new String[] {"busiLv4"})
            .articulationFlag(new String[] {"articulationFlag"})
            .roleId(0)
            .lv1Names(Arrays.asList("value"))
            .build();

        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Run the test
        final List<String> result = labelProductionServiceUnderTest.getUserProduction(request);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetUserProductionB() {
        // Setup
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Run the test
        final List<String> result = labelProductionServiceUnderTest.getUserProduction(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetCoaMonitorVersion() {
        // Setup
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Run the test
        final List<CoaProdInfoVO> result =mockILabelProductionDao.getCoaMonitorVersion(new LabelConfigQueryRequest());

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
    @Test
    void testGetPlanComSopInfo() {
        // Setup
        // Configure JalorUserTools.getRolePermission(...).
        final DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        dataPermissionsVO.setOptLv1Right(true);
        dataPermissionsVO.setOptBgRight(false);
        dataPermissionsVO.setLv1DataType("ALL");
        dataPermissionsVO.setBgDataType("bgDataType");
        dataPermissionsVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        dataPermissionsVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        when(mockJalorUserTools.getRolePermission(0)).thenReturn(dataPermissionsVO);

        when(mockILabelConfigDao.getHierarchySortInfoLv1()).thenReturn(new HashMap<>());

        // Run the test
        final List<PlanComProdInfoVo> result =mockILabelProductionDao.getPlanComSopInfo(new LabelConfigQueryRequest());

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetProfitExaminingL2YtdShipInfo() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingL2YtdInfo(...).
        final ProfitGranularityL2InfoVo profitGranularityL2InfoVo = new ProfitGranularityL2InfoVo();
        profitGranularityL2InfoVo.setLv1Code("lv1Code");
        profitGranularityL2InfoVo.setBgCode("bgCode");
        profitGranularityL2InfoVo.setL1Name("l1Name");
        profitGranularityL2InfoVo.setL2Name("l2Name");
        profitGranularityL2InfoVo.setLv1Name("lv1Name");
        final List<ProfitGranularityL2InfoVo> profitGranularityL2InfoVos = Arrays.asList(profitGranularityL2InfoVo);
        when(mockILabelProductionDao.getProfitExaminingL2YtdInfo(any(ForecastsRequest.class)))
            .thenReturn(profitGranularityL2InfoVos);

        // Run the test
        final Map<String, List<ProfitGranularityL2InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingL2YtdShipInfo(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetProfitExaminingL2MonthShipInfo() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingL2YtdInfo(...).
        final ProfitGranularityL2InfoVo profitGranularityL2InfoVo = new ProfitGranularityL2InfoVo();
        profitGranularityL2InfoVo.setLv1Code("lv1Code");
        profitGranularityL2InfoVo.setBgCode("bgCode");
        profitGranularityL2InfoVo.setL1Name("l1Name");
        profitGranularityL2InfoVo.setL2Name("l2Name");
        profitGranularityL2InfoVo.setLv1Name("lv1Name");
        final List<ProfitGranularityL2InfoVo> profitGranularityL2InfoVos = Arrays.asList(profitGranularityL2InfoVo);
        when(mockILabelProductionDao.getProfitExaminingL2YtdInfo(any(ForecastsRequest.class)))
            .thenReturn(profitGranularityL2InfoVos);

        // Run the test
        final Map<String, List<ProfitGranularityL2InfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingL2MonthShipInfo(
            requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

}