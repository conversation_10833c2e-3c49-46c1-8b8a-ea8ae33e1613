/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * OptTypeEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum OptTypeEnum {
    IMPORT("IMP", "导入"),
    EXPORT("EXP", "导出");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    OptTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * getByCode
     *
     * @param code code
     * @return StepTypeEnum
     */
    public static OptTypeEnum getByCode(String code) {
        for (OptTypeEnum value : OptTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

