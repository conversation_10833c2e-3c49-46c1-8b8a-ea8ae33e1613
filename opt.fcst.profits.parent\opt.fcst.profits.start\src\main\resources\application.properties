application.appName=fcst_profits_service
application.scope=fcst.headquater
application.dbType=gauss
application.serviceVersion=1.0
application.appId=com.huawei.finance.ai.opt.fop
application.subAppId=fcst_profits_service
application.tenantId=business.fcst_profits_service
server.servlet.context-path=/fcst/profits

#itp Gauss datasource Start
datasource.maxIdle.1=15
datasource.minIdle.1=10
datasource.maxTotal.1=100
datasource.maxWaitMillis.1=600000
datasource.removeAbandonedOnBorrow.1=true
datasource.removeAbandonedTimeout.1=120
datasource.timeBetweenEvictionRunsMillis.1=30000
#itp Gauss datasource End

server.port=8003
dev.server.port=60009
cxf.jaxrs.client.headers.accept=text/plain
spring.profiles.active=sit
httpClient.sgov.environment=${sgov_environment}

#注册中心
registry.eureka.registration.enabled=true
registry.eureka.serviceUrl.default=https://fin.register.his-beta.huawei.com/msa/register/v2

#usf配置
#注册模块开关，true；开启，false：关闭
usf.service.app=${application.appId}
usf.service.subApp=${application_subAppId}
usf.service.version=1.0
usf.service.environment=${fox_usf_service_environment}
usf.server.handlers=tracer-server,security-provider
usf.client.handlers=user-setter, security-consumer, loadbalancer-ribbon, tracer-client


#Start JWT Auth
security.enabled=true
security.jwt.group.alg=HS512
security.jwt.gateway.alg=RS512
jalor.jwtVerify.skipAudienceIssuer=true
jalor.jwtFilter.exclusions=*/servlet/rebuildSession,*/services/jalor/security/*,*/services/jalor/sitemap/*,*.html
sso.ignore.path=*/servlet/rebuildSession,*/servlet/ischeduler/*,*/servlet/changeRole
#End JWT Auth

#public service alg
authentication.mode=jalor
jalor.stubPrivilegeInitCache.enabled=true
jalor5.web.ssoFilter.exclusions=*\\.js,*\\.jpg,*\\.png,*\\.gif,*\\.bmp,*\\.css,*\\.class,*\\.properties,*/initUserPermission,*/rpcInitUserPermission,*/registryQueryService/*,*/personalized/setting/list,*/helperprocxy/*,*/logService/*,*/findCurrentUserThemeSettings/*,*/tryFindOrCreateUser/*,*/eureka/serverext/*,*/jalor/security/mypermissionquery/*,*/auditLog/createAuditLog,*/publicservices/*,*/jalor/eureka/serverext/list,*/servlet/cache,*/servlet/userCacheClean,*/servlet/health,*/services/userCenter/selectLv1,services/userCenter/selectBg
jalor5.web.cxfServlet.hideServiceListPage=true
jalor.stubPrivilegeSecurity.enabled=true
jalor.sql.add.backslash.enabled=true
sensitive.field.list.regexp=(ADS Container Environment Parameters|ERROR while loading root key system file)(.{1,50}(?=ADS Container Environment Parameters|ERROR while loading root key system file)|.{1,50})
App.Security.XssFilter.isContainsGWRequest=true

#Access Authentication Configuration
App.Security.RefererExclusions=*/api-docs*,*.html
# 关闭 jalor相关参数 页面xss过滤，csrf校验等
jalor.web.resource-filter.enabled=false
jalor.web.requestSecurity-filter.enabled=false
jalor.web.csrf-filter.enabled=false
jalor.web.xss-filter.enabled=false
jalor.auditlog.enabled=true
jalor.auditlog.destination=file
jalor.web.session-listener.enabled=true

#spring-actuator
endpoints.refresh.enabled=false
endpoints.enabled=false
endpoints.info.enabled = true

#Spring Cloud Config
ribbon.UseIPAddrForServer=true
endpoints.jmx.enabled=false
spring.data.redis.repositories.enabled=false
#标签审视下载模板
excel.template.fileId=403505d9-093c-4711-bbcd-fcc152eb15ce
#SwitchRoles
jalor.dispersed-filter.enabled=true

