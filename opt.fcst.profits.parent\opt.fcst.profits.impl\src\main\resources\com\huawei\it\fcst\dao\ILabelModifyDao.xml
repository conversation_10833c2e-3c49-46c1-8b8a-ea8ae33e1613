<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.profits.dao.ILabelModifyDao">
    <resultMap type="com.huawei.it.fcst.profits.vo.LabelModifyVO" id="resultMap">
        <result property="fileSourceKey" column="file_source_key"/>
        <result property="fileName" column="file_name"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="pageModule" column="page_module"/>
        <result property="status" column="status"/>
        <result property="id" column="id"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
    </resultMap>

    <sql id="allFields">
        file_source_key,
        file_name,
        last_updated_by,
        page_module,
        status,
        id,
        creation_date,
        created_by,
        last_update_date
    </sql>

    <sql id="uniqueKeyField">
        id
        =
        #{id,jdbcType=VARCHAR}
    </sql>

    <select id="findByPage" resultMap="resultMap">
        SELECT
        file_source_key,
        file_name,
        last_updated_by,
        page_module,
        status,
        id,
        creation_date,
        created_by,
        last_update_date
        FROM FIN_DM_OPT_FOP.dm_fop_label_modify_info_t
        where status='Submit'
        <if test='_parameter.get("0").userId != null  and _parameter.get("0").userId != ""'>
            AND last_updated_by = #{0.userId,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").beginTime != null'>
            AND creation_date <![CDATA[ >= ]]> #{0.beginTime,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").endTime != null'>
            AND creation_date <![CDATA[ < ]]> #{0.endTime,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").pageModule != null and _parameter.get("0").pageModule != ""'>
            AND page_module like #{0.pageModule,jdbcType=VARCHAR} || '%'
        </if>
        order by creation_date desc
        LIMIT #{1.pageSize} OFFSET #{1.mysqlStartIndex}
    </select>

    <select id="findByPageCount" resultType="int">
        SELECT COUNT(1)
        FROM FIN_DM_OPT_FOP.dm_fop_label_modify_info_t where status='Submit'
        <if test='_parameter.get("0").userId != null  and _parameter.get("0").userId != ""'>
            AND last_updated_by = #{0.userId,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").beginTime != null'>
            AND creation_date <![CDATA[ >= ]]> #{0.beginTime,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").endTime != null'>
            AND creation_date <![CDATA[ < ]]> #{0.endTime,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").pageModule != null and _parameter.get("0").pageModule != ""'>
            AND page_module like #{0.pageModule,jdbcType=VARCHAR} || '%'
        </if>
    </select>

    <select id="findDataByParam" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM FIN_DM_OPT_FOP.dm_fop_label_modify_info_t
        WHERE status='Save'
    </select>

    <select id="findById" parameterType="String" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM FIN_DM_OPT_FOP.dm_fop_label_modify_info_t
        WHERE
        <include refid="uniqueKeyField"/>
    </select>

    <insert id="createList" parameterType="java.util.List">
        INSERT INTO FIN_DM_OPT_FOP.dm_fop_label_modify_info_t
        (<include refid="allFields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.fileSourceKey,jdbcType=VARCHAR},#{item.fileName,jdbcType=VARCHAR},#{item.lastUpdatedBy,jdbcType=VARCHAR},#{item.pageModule,jdbcType=VARCHAR},'Submit',#{item.id,jdbcType=VARCHAR},#{item.creationDate,jdbcType=TIMESTAMP},#{item.createdBy,jdbcType=VARCHAR},#{item.lastUpdateDate,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <update id="updateList">
        UPDATE FIN_DM_OPT_FOP.dm_fop_label_modify_info_t set status = 'Submit',
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP}
        WHERE status='Save'
        <if test="modifyList != null and modifyList.size() > 0">
            AND id IN
            <foreach collection='modifyList' open="(" close=")" item="item" index="index" separator=",">
                #{item.id}
            </foreach>
        </if>
    </update>

    <select id="groupDataByParam" parameterType="java.util.List" resultType="com.huawei.it.fcst.profits.vo.LabelCountVO">
        SELECT lv1_name lv1Name,period_id periodId,sum(modify_size) modifyNumber FROM FIN_DM_OPT_FOP.dm_fop_label_modify_info_t_statics
        WHERE status='Submit'
        <if test='periodIds != null and periodIds != ""'>
            AND period_id in
            <foreach collection='periodIds' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by lv1_name,period_id
    </select>

    <select id="findStaticDataByParam" resultType="java.lang.String">
        SELECT id FROM FIN_DM_OPT_FOP.dm_fop_label_modify_info_t WHERE status='Save'
    </select>

    <insert id="createStaticList" parameterType="java.util.List">
        INSERT INTO FIN_DM_OPT_FOP.dm_fop_label_modify_info_t(last_updated_by,page_module,status,lv1_name,id,creation_date,period_id,created_by,last_update_date,modify_size)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.lastUpdatedBy,jdbcType=VARCHAR},#{item.pageModule,jdbcType=VARCHAR},'Save',#{item.lv1Name,jdbcType=VARCHAR},#{item.id,jdbcType=VARCHAR},CURRENT_TIMESTAMP,#{item.periodId,jdbcType=VARCHAR},#{item.createdBy,jdbcType=VARCHAR},CURRENT_TIMESTAMP,#{item.modifySize,jdbcType=NUMERIC})
        </foreach>
    </insert>

    <update id="updateStaticList" parameterType="java.util.List">
        UPDATE FIN_DM_OPT_FOP.dm_fop_label_modify_info_t set status = 'Submit',
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        last_update_date = CURRENT_TIMESTAMP
        WHERE status='Save'
    </update>
</mapper>
