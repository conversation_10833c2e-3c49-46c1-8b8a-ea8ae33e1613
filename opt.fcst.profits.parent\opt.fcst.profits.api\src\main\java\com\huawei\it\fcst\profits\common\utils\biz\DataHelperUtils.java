/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.utils.biz;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * DataHelperUtils
 *
 * <AUTHOR>
 * @since 2022/10/02.
 */
public class DataHelperUtils {

    /**
     * 设置半年度时点信息
     * 去年实际
     *
     * @param request
     * @return
     */
    public static void setHalfPeriod(ForecastsRequest request) {
        int year = TimeUtils.getCurYear(request.getPeriodId());
        int month = TimeUtils.getCurMonth(request.getPeriodId());
        request.setCurYear(year);
        request.setCurMonth(month);
        request.setIncludeFlag("1");
        request.setMaxPeriods(Arrays.asList(String.valueOf(request.getCurYear())));
        if (!StringUtils.equals(CommonConstant.FCST_HALF_YEAR_STEP, request.getFcstStep())) {
            return;
        }
        if (null == request.getTabGraphType() || StringUtils.equals(request.getTabGraphType(), CommonConstant.TAB_HALF_YEAR)) {
            String monthType = "H1"; // 上半年
            if (month > 6) {
                monthType = "H2"; // 下半年
            }
            request.setActStepPeriod((year - 1) + monthType);
            request.setFcstStepPeriod(year + monthType);
        }
        if (null == request.getTabGraphType()) {
            request.setMaxPeriods(Arrays.asList(request.getFcstStepPeriod()));
        }
    }

    /**
     * 根据预测步长，获取预测时点
     *
     * @param request
     */
    public static List<String> getFullQn(ForecastsRequest request) {
        List<String> fullQnList = new ArrayList<>();
        int year = request.getCurYear();
        int curMonth = TimeUtils.getCurMonth(request.getPeriodId());
        for (int i = 2019; i <= year; i++) {
            int tempQ = 4;
            if (i == year) {
                tempQ = (curMonth - 1) / 3;
                if (tempQ == 0) {
                    break;
                }
                fullQnList.add(i + "Q1");
                if (tempQ >= 2) {
                    fullQnList.add(i + "Q2");
                }
                if (tempQ == 3) {
                    fullQnList.add(i + "Q" + tempQ);
                }
            } else {
                for (int j = 1; j <= tempQ; j++) {
                    fullQnList.add(i + "Q" + j);
                }
            }
        }
        fullQnList.add("当年累计");
        return fullQnList;
    }

    /**
     * 获取所有年份
     *
     * @param curYear
     * @return
     */
    public static List<String> getFullHisYear(int curYear, int curMonth, String type) {
        List<String> fullYearList = new ArrayList<>();
        String suffix = "H1";
        if (curMonth > 6) {
            suffix = "H2";
        }
        for (int i = 2019; i <= curYear - 1; i++) {
            if (StringUtils.equals("H", type)) {
                fullYearList.add(i + suffix);
            } else {
                fullYearList.add(String.valueOf(i));
            }
        }
        fullYearList.add("当年累计");
        return fullYearList;
    }

    public static List<String> getHalfYearHisYear(int curYear,int curMonth) {
        List<String> fullYearList = new ArrayList<>();
        String[] suffixs = new String[] {"H1", "H2"};
        // 历史数展示到当年的前一个节点
        for (int i = 2019; i <= curYear; i++) {
            if (i != curYear) {
                fullYearList.add(i + suffixs[0]);
                fullYearList.add(i + suffixs[1]);
            } else {
                if (curMonth > 6) {
                    fullYearList.add(i + suffixs[0]);
                }
            }
        }
        fullYearList.add("当年累计");
        return fullYearList;
    }

    // 预测只到当前季度|年度
    public static List<String> getHalfYearFutureYear(int curYear, int curMonth) {
        List<String> fullYearList = new ArrayList<>();
        String[] suffixs = new String[] {"H1", "H2"};
        if(curMonth>6){
            fullYearList.add(curYear + suffixs[1]);
        } else {
            fullYearList.add(curYear + suffixs[0]);
        }
        return fullYearList;
    }

    public static List<String> getFutureQuarter(ForecastsRequest request) {
        int curYear = request.getCurYear();
        int curMonth = request.getCurMonth();
        List<String> fullYearList = new ArrayList<>();
        String[] suffixs = new String[] {"Q1", "Q2", "Q3", "Q4"};
        int quarterNum = (curMonth - 1) / 3;
        fullYearList.add(curYear + suffixs[quarterNum]);
        request.setFcstStepPeriod(curYear + suffixs[quarterNum]);
        return fullYearList;
    }

    /**
     * 获取所有历史月份
     *
     * @param curYear
     * @return
     */
    public static List<String> getFullHisMonth(int curYear, int curMonth) {
        List<String> fullYearMonthList = new ArrayList<>();
        for (int i = 2019; i <= curYear; i++) {
            int tempMonth = 12;
            if (i == curYear) {
                tempMonth = curMonth - 1;
            }
            for (int j = 1; j <= tempMonth; j++) {
                fullYearMonthList.add(i + StringUtils.leftPad(String.valueOf(j), 2, "0"));
            }
        }
        return fullYearMonthList;
    }

    /**
     * 获取所有预测月份
     *
     * @param curYear
     * @return
     */
    public static List<String> getFullPredictMonth(int curYear, int curMonth) {
        List<String> fullYearMonthList = new ArrayList<>();
        for (int k = curMonth; k <= 12; k++) {
            fullYearMonthList.add(curYear + StringUtils.leftPad(String.valueOf(k), 2, "0") + "AI auto");
        }
        return fullYearMonthList;
    }

    public static void getTotalYtdMonthItem(ForecastsRequest forecastsRequest, String curYear) {
        List<String> ytdList = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            ytdList.add(curYear + StringUtils.leftPad(String.valueOf(i), 2, "0") + "YTD");
        }
        forecastsRequest.setTargetPeriods(ytdList);
    }

    public static void getTotalHalfYearMonthItem(ForecastsRequest forecastsRequest, String curYear) {
        List<String> halfYearList = new ArrayList<>();
        for (int i = 1; i <= 2; i++) {
            halfYearList.add(curYear + "H" + i);
        }
        forecastsRequest.setTargetPeriods(halfYearList);
    }

    public static void getTotalQuarterItem(ForecastsRequest forecastsRequest, String curYear) {
        List<String> quarterList = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            quarterList.add(curYear + "Q" + i);
        }
        forecastsRequest.setTargetPeriods(quarterList);
    }

    public static void getTotalYearItem(ForecastsRequest forecastsRequest, String curYear) {
        List<String> yearList = new ArrayList<>();
        yearList.add(curYear);
        forecastsRequest.setTargetPeriods(yearList);
    }
}
