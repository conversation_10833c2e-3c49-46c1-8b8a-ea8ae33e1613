/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * The Entity of HolisticViewProdInfoVO
 * @since 2022-10-17 19:38:39
 */

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class HolisticViewProdInfoVO extends HolisticViewConfigDataVO {
    private static final long serialVersionUID = 1L;

    /**
     * 重量级团队lv0code
     **/
    private String lv0Code;

    /**
     * 重量级团队lv0名称
     **/
    private String lv0Name;

    /**
     * 问题数据标签
     */
    private String errorFlag;

    private String versionCode;
}
