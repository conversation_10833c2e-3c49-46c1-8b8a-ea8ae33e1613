package com.huawei.it.fcst.profits.impl;

import com.huawei.it.fcst.profits.service.config.impl.HolisticViewConfigDataExporter;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;

import cn.hutool.core.bean.BeanUtil;

import org.junit.Test;
import org.junit.Before;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HolisticViewConfigDataExporter Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>3月 6, 2023</pre>
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class HolisticViewConfigDataExporterTest {
    @InjectMocks
    HolisticViewConfigDataExporter holisticViewConfigDataExporter;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void resetDataList() {
        List<Map<String, Object>> infoList = new ArrayList<>();
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        holisticViewConfigDataVO.setArticulationFlag("SCENO1");
        holisticViewConfigDataVO.setStatus("Import");
        HolisticViewConfigDataVO holisticViewConfigDataVO1 = new HolisticViewConfigDataVO();
        holisticViewConfigDataVO1.setArticulationFlag("SCENO2");
        holisticViewConfigDataVO1.setStatus("Import");
        HolisticViewConfigDataVO holisticViewConfigDataVO2 = new HolisticViewConfigDataVO();
        holisticViewConfigDataVO2.setArticulationFlag("SCENO3");
        holisticViewConfigDataVO2.setStatus("Import");
        Map<String,Object> infoMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
        Map<String,Object> infoMap1 = BeanUtil.beanToMap(holisticViewConfigDataVO1);
        Map<String,Object> infoMap2 = BeanUtil.beanToMap(holisticViewConfigDataVO2);
        infoList.add(infoMap);
        infoList.add(infoMap1);
        infoList.add(infoMap2);

        holisticViewConfigDataExporter.resetDataList(new ArrayList() ,infoList);
        Assertions.assertEquals("待提交", infoList.get(0).get("status"));
    }
    @Test
    public void resetDataListA() {
        List<Map<String, Object>> infoList = new ArrayList<>();
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        holisticViewConfigDataVO.setArticulationFlag("SCENO1");
        holisticViewConfigDataVO.setStatus("Submit");
        HolisticViewConfigDataVO holisticViewConfigDataVO1 = new HolisticViewConfigDataVO();
        holisticViewConfigDataVO1.setArticulationFlag("SCENO2");
        holisticViewConfigDataVO1.setStatus("Submit");
        HolisticViewConfigDataVO holisticViewConfigDataVO2 = new HolisticViewConfigDataVO();
        holisticViewConfigDataVO2.setArticulationFlag("SCENO3");
        holisticViewConfigDataVO2.setStatus("Submit");
        Map<String,Object> infoMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
        Map<String,Object> infoMap1 = BeanUtil.beanToMap(holisticViewConfigDataVO1);
        Map<String,Object> infoMap2 = BeanUtil.beanToMap(holisticViewConfigDataVO2);
        infoList.add(infoMap);
        infoList.add(infoMap1);
        infoList.add(infoMap2);
        holisticViewConfigDataExporter.resetDataList(new ArrayList() ,infoList);
        Assertions.assertEquals("提交", infoList.get(0).get("status"));
    }
    @Test
    public void resetDataListB() {
        List<Map<String, Object>> infoList = new ArrayList<>();
        infoList.add(new HashMap<>());
        holisticViewConfigDataExporter.resetDataList(new ArrayList() ,infoList);
        Assertions.assertEquals(null, infoList.get(0).get("status"));
    }
} 
