/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * The Entity of PlanComConfigDataVO
 *
 * @since 2022-10-17 19:38:39
 */
@Getter
@Setter
public class PlanComConfigDataVO extends LabelConfigVo {
    private static final long serialVersionUID = 1L;

    private String lv1Code;

    /**
     * 一级计委包
     **/
    private String planComLv1;

    /**
     * 二级计委包
     **/
    private String planComLv2;

    /**
     * 三级计委包
     **/
    private String planComLv3;

    /**
     * 四级业务包
     **/
    private String busiLv4;

    /**
     * l1名称
     **/
    private String l1Name;

    /**
     * l2名称
     */
    private String l2Name;

    /**
     * L2系数
     */
    private String l2Coefficient;

    @Override
    public boolean equals(Object other) {
        //	先判断自反性
        if (other == this) {
            return true;
        }

        //  判断同类型，同时也满足了第五条（null）
        if (!(other instanceof PlanComConfigDataVO)) {
            return false;
        }

        //  最后再进行类型转换，比较值
        PlanComConfigDataVO that = (PlanComConfigDataVO) other;
        return this.toString().equals(that.toString());
    }

    @Override
    public int hashCode() {
        return Objects.hash(lv1Code, l1Name, l2Name, planComLv1, planComLv2, planComLv3, busiLv4);
    }

    @Override
    public String toString() {
        return l1Name + planComLv1 + planComLv2 + planComLv3 + busiLv4;
    }
}
