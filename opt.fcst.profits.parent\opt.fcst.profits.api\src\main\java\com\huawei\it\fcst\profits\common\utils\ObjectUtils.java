/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.UUID;

/**
 * ObjectUtils
 *
 * <AUTHOR>
 * @since 2022/10/02.
 */
public class ObjectUtils {

    private static final Logger logger = LoggerFactory.getLogger(ObjectUtils.class);

    /**
     * 设置对象属性值
     * 默认值
     *
     * @param obj
     * @param fieldName
     * @param value
     */
    public static void setFieldVal(Object obj, String fieldName, Object value) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            ReflectionUtils.makeAccessible(field);
            field.set(obj, value);
        } catch (Exception e1) {
            logger.error("setFieldVal failed");
        }
    }

    public static Field getFieldByName(Object obj, String filedName) {
        Field field = ReflectionUtils.findField(obj.getClass(), filedName);
        ReflectionUtils.makeAccessible(field);
        return field;
    }

    /**
     * 获取UUID
     *
     * @return String
     */
    public static String genRanCode() {
        return UUID.randomUUID().toString().replace("-", "") + System.currentTimeMillis();
    }
}
