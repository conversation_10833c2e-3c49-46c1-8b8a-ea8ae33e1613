/*
* Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
*
*/

package com.huawei.it.fcst.profits.impl;

import java.util.ArrayList;
import java.util.List;

import com.huawei.it.fcst.profits.service.predict.impl.KrCpfL1Service;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.huawei.it.fcst.profits.dao.IKrCpfL1Dao;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.response.KrCpfL1Response;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

import com.alibaba.fastjson.JSONObject;

/**
* 功能描述
*
* <AUTHOR>
* @since 2023年02月10日
*/
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class KrCpfL1ServiceTest {

   @InjectMocks
   private KrCpfL1Service krCpfL1Service;

   @Mock
   private IKrCpfL1Dao iKrCpfL1Dao;

   @Before
   public void before() {
       MockitoAnnotations.initMocks(this);
       UserVO user = new UserVO();
       user.setUserId(9999L);
       user.setUserAccount("xtestAccount");
       user.setUserCN("testCN");
       user.setEmployeeNumber("testCN");
       user.setScope("testScope");
       user.setAppName("testAppName");
       RequestContext testRequestContext = new RequestContext();
       testRequestContext.setUser(user);
       RequestContextManager.setCurrent(testRequestContext);
   }

   @Test
   public void getMergeData() throws CommonApplicationException {
       List<String> test = new ArrayList<>();
       doReturn(test).when(iKrCpfL1Dao).getL1TotalInfo(any());
       List pagedResult = krCpfL1Service.getMergeData(any());
       Assertions.assertEquals(test, pagedResult);
   }

   @Test
   public void getL1PolylineInfoTest() {
       ForecastsRequest forecastsRequest = JSONObject.parseObject(
           "{\"periodId\":202212,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"年度法\",\"phaseDate\":\"********\",\"predictionType\":\"MONTH\",\"roleId\":6440,\"overseaDesc\":\"全球\",\"lv2Code\":\"153324\",\"l1Name\":\"5G&LTE FDD\",\"tabGraphType\":\"Y\"}\n",
           ForecastsRequest.class);
       List<KrCpfL1Response> list = new ArrayList<>();
       list.add(new KrCpfL1Response());
       when(krCpfL1Service.getMergeData(any())).thenReturn(list);
       krCpfL1Service.getL1PolylineInfo(forecastsRequest);
       Assertions.assertNotNull(list);
   }

   @Test
   public void getL1PolylineInfoTestA(){
       ForecastsRequest forecastsRequest = new ForecastsRequest();
       Assertions.assertNotNull(krCpfL1Service.getL1PolylineInfo(forecastsRequest));
   }

    @Test
    public void getL1PolylineInfoTestB() {
        ForecastsRequest forecastsRequest = JSONObject.parseObject(
            "{\"periodId\":202212,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"年度法\",\"phaseDate\":\"********\",\"predictionType\":\"MONTH\",\"roleId\":6440,\"overseaDesc\":\"全球\",\"lv2Code\":\"153324\",\"l1Name\":\"5G&LTE FDD\",\"tabGraphType\":\"Y\"}\n",
            ForecastsRequest.class);
        List<KrCpfL1Response> list = new ArrayList<>();
        KrCpfL1Response krCpfL1Response = new KrCpfL1Response();
        krCpfL1Response.setTargetPeriod("2022");
        list.add(krCpfL1Response);
        when(krCpfL1Service.getMergeData(any())).thenReturn(list);
        krCpfL1Service.getL1PolylineInfo(forecastsRequest);
        Assertions.assertNotNull(forecastsRequest);
    }
}