/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * BgEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum BgEnum {
    BG("PDCG901159|PDCG901160", "CNBG-EBG"),
    GROUP("PROD0002", "ICT"),
    CNBG("PDCG901159", "CNBG"),
    EBG("PDCG901160", "EBG");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    BgEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

