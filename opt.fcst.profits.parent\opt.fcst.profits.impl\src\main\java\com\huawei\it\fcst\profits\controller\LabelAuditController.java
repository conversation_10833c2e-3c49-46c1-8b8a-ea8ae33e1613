/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

import com.huawei.it.jalor5.core.util.CollectionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.common.vo.ResultCode;
import com.huawei.it.fcst.profits.service.IFcstProfitsCommonService;
import com.huawei.it.fcst.profits.service.ISpartProfitingRelationService;
import com.huawei.it.fcst.profits.vo.LabelCountVO;
import com.huawei.it.fcst.profits.vo.LabelOperateLogVO;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.LabelConfigRequest;
import com.huawei.it.fcst.profits.vo.request.LabelInfoRequest;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.annotation.SecurityPolicy;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;

/**
 * 标签审视、标签配置
 */
@JalorResource(code = "labelAuditController", desc = "labelAuditController")
@Named("labelAuditController")
public class LabelAuditController implements ILabelAuditController {

    private static final Logger logger = LoggerFactory.getLogger(LabelAuditController.class);

    @Autowired
    private ISpartProfitingRelationService spartProfitingRelationService;

    @Autowired
    private IFcstProfitsCommonService iFcstProfitsCommonService;

    @Audit(module="labelAuditController-getConfigDataByPage",operation="getConfigDataByPage",message="查询标签配置列表")
    @JalorOperation(code = "getConfigDataByPage", desc = "查询标签配置列表")
    @Override
    public CommonResult getConfigDataByPage(LabelConfigRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            return CommonResult.success(spartProfitingRelationService.findConfigByPage(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("getConfigDataByPage异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @JalorOperation(code = "getVersionInfo", desc = "获取版本信息")
    @Override
    public CommonResult getVersionInfo() {
        try {
            return CommonResult.success(spartProfitingRelationService.getVersions());
        } catch (CommonApplicationException ex) {
            logger.error("获取版本信息异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Override
    @JalorOperation(code = "getProductLine", desc = "获取产品线列表信息")
    public CommonResult getProductLine(LabelConfigRequest labelConfigRequest) {
        try {
            return CommonResult.success(spartProfitingRelationService.getLv1List(labelConfigRequest));
        } catch (CommonApplicationException ex) {
            logger.error("获取产业异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Override
    @JalorOperation(code = "getL1Names", desc = "获取L1名称")
    public CommonResult getL1Names(LabelConfigRequest request) {
        try {
            vailArrayParamLimitSize(request);
            return CommonResult.success(spartProfitingRelationService.getL1NameList(request));
        } catch (CommonApplicationException ex) {
            logger.error("获取l1名称异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Override
    @JalorOperation(code = "getL2Names", desc = "获取L2名称")
    public CommonResult getL2Names(LabelConfigRequest request) {
        try {
            vailArrayParamLimitSize(request);
            return CommonResult.success(spartProfitingRelationService.getL2NameList(request));
        } catch (CommonApplicationException ex) {
            logger.error("获取l2名称异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Override
    @JalorOperation(code = "getL3Names", desc = "获取L3名称")
    public CommonResult getL3Names(LabelConfigRequest request) {
        try {
            vailArrayParamLimitSize(request);
            return CommonResult.success(spartProfitingRelationService.getL3NameList(request));
        } catch (CommonApplicationException ex) {
            logger.error("获取l3名称异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Audit(module="labelAuditController-getAuditDataByPage",operation="getAuditDataByPage",message="查询标签审视信息列表")
    @JalorOperation(code = "getAuditDataByPage", desc = "查询标签审视信息列表")
    @Override
    public CommonResult getAuditDataByPage(LabelConfigRequest requestVO) {
        try {
            vailArrayParamLimitSize(requestVO);
            PagedResult<SpartProfitingRelationVO> pagedResult = spartProfitingRelationService.findByPage(requestVO);
            Map result = new LinkedHashMap();
            result.put("result", pagedResult.getResult());
            result.put("pageVO", pagedResult.getPageVO());
            result.putAll(iFcstProfitsCommonService.validateSumbit(requestVO));
            return CommonResult.success(result);
        } catch (CommonApplicationException ex) {
            logger.error("获取getAuditDataByPage异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Audit(module="labelAuditController-save",operation="save",message="保存标签审视信息")
    @Override
    @JalorOperation(code = "save", desc = "保存标签审视信息")
    public CommonResult save(List<LabelInfoRequest> voList) {
        spartProfitingRelationService.saveData("Y", voList);
        return CommonResult.success("保存成功");
    }

    @Audit(module="labelAuditController-submit",operation="submit",message="提交标签审视信息")
    @Override
    @JalorOperation(code = "submit", desc = "提交标签审视信息")
    public CommonResult submit() {
        int day = TimeUtils.getDayOfMonth();
        Map<String, Object> status = iFcstProfitsCommonService.validateSumbit();
        Integer startDay = (Integer) status.get("startDay");
        Integer endDay = (Integer) status.get("endDay");
        if (!(Boolean) status.get("isSubmit")) {
            if (ObjectUtils.anyNull(startDay, endDay)) {
                return CommonResult.failed(ResultCode.CHECK_TIP, "不可提交!");
            } else {
                return CommonResult.failed(ResultCode.CHECK_TIP,
                        String.format(Locale.ROOT, "请于每月的%s号00:00:00后至%s号12:00:00前进行提交！当前为%s号,不可提交!", startDay, endDay, day));
            }
        }
        return spartProfitingRelationService.submitData();
    }

    /**
     * 模板下载
     *
     * @param response 入参
     * @return
     * @throws CommonApplicationException
     */
    @Audit(module="labelAuditController-templateDownload",operation="templateDownload",message="下载导入模板")
    @JalorOperation(code = "templateDownload", desc = "下载导入模板")
    @Override
    public CommonResult templateDownload(HttpServletResponse response) {
        try {
            spartProfitingRelationService.templateDownload(response);
            return CommonResult.success("下载成功");
        } catch (CommonApplicationException ex) {
            logger.error("下载模板异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "下载失败");
        }
    }

    @Audit(module="labelAuditController-importData",operation="importData",message="导入标签审视信息")
    @Override
    @JalorOperation(code = "importData", desc = "导入标签审视信息")
    public CommonResult importData(Attachment attachment, String fileName) throws CommonApplicationException {
        return spartProfitingRelationService.importData(attachment, fileName);
    }

    @Audit(module="labelAuditController-exportData",operation="exportData",message="导出标签审视信息")
    @JalorOperation(code = "exportData", desc = "导出标签审视信息")
    @Override
    public CommonResult exportData(HttpServletResponse response, LabelConfigRequest labelConfigRequest) throws CommonApplicationException {
        if (!CollectionUtil.isNullOrEmpty(labelConfigRequest.getLv1Name()) && labelConfigRequest.getLv1Name().length > 200) {
            throw new CommonApplicationException("操作的数据过多");
        }
        return spartProfitingRelationService.exportData(response, labelConfigRequest);
    }

    @Audit(module="labelAuditController-getManualModifyInfo",operation="getManualModifyInfo",message="标签复盘-获取标签人工修改率")
    @JalorOperation(code = "getManualModifyInfo", desc = "标签复盘-获取标签人工修改率")
    @Override
    public CommonResult getManualModifyInfo(String year, String roleId) {
        try {
            List<LabelCountVO> manualModifyList = spartProfitingRelationService.getManualModifyInfo(year, roleId);
            if (CollectionUtils.isEmpty(manualModifyList)) {
                return CommonResult.success(new ArrayList<>());
            }
            return CommonResult.success(manualModifyList);
        } catch (CommonApplicationException ex) {
            logger.error("获取修改率异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    @Audit(module="labelAuditController-getLastUpdatedBys",operation="getLastUpdatedBys",message="获取用户名称")
    @Override
    @JalorOperation(code = "getLastUpdatedBys", desc = "获取用户名称")
    public CommonResult getLastUpdatedBys(LabelConfigRequest request) {
        try {
            return CommonResult.success(spartProfitingRelationService.getLastUpdatedBys(request));
        } catch (CommonApplicationException ex) {
            logger.error("查询用户异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询用户信息失败");
        }
    }

    @JalorOperation(code = "getStatusList", desc = "获取数据状态")
    @Override
    public CommonResult getStatusList(LabelConfigRequest request) {
        try {
            return CommonResult.success(spartProfitingRelationService.getStatusList(request));
        } catch (CommonApplicationException ex) {
            logger.error("查询用户异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }

    }
    @JalorOperation(policy = SecurityPolicy.AllSystemUser)
    @Override
    public CommonResult getOperateRecordLogStatus(LabelOperateLogVO request) {
        try {
            return CommonResult.success(spartProfitingRelationService.getOperateRecordLogStatus(request));
        } catch (CommonApplicationException ex) {
            logger.error("查询用户异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "查询失败");
        }
    }

    private void vailArrayParamLimitSize(LabelConfigRequest request) throws CommonApplicationException {
        if (!CollectionUtil.isNullOrEmpty(request.getLv1Name()) && request.getLv1Name().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getL1Name()) && request.getL1Name().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getL2Name()) && request.getL2Name().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getL3Name()) && request.getL3Name().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getDataType()) && request.getDataType().length > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getLv1List()) && request.getLv1List().size() > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(request.getLastUpdatedBys()) && request.getLastUpdatedBys().size() > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
    }

}
