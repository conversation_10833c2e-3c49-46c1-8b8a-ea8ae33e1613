/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.service;

import java.util.Map;

import com.huawei.it.jalor5.core.exception.ApplicationException;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年06月08日
 */
public interface IAccessService {
    /**
     * 判断是否办公IP
     *
     * @return true|false
     * @throws ApplicationException
     */
    Boolean isAccess() throws ApplicationException;

    /**
     * 根据IP查询地址服务
     * @return MAP
     * @throws ApplicationException
     */
    Map<String, String> findAccess() throws ApplicationException;
}
