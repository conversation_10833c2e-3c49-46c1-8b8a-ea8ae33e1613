/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.audit.impl;

import com.huawei.it.fcst.profits.comm.AbstractService;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.constants.RelationConstants;
import com.huawei.it.fcst.profits.common.enums.ModuleEnum;
import com.huawei.it.fcst.profits.common.enums.OptTypeEnum;
import com.huawei.it.fcst.profits.common.enums.RecStsEnum;
import com.huawei.it.fcst.profits.common.enums.SaveTypeEnum;
import com.huawei.it.fcst.profits.common.utils.FileProcessUtis;
import com.huawei.it.fcst.profits.common.utils.ObjectUtils;
import com.huawei.it.fcst.profits.common.utils.UserHandle;
import com.huawei.it.fcst.profits.common.utils.UserInfo;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.common.vo.ExcelVO;
import com.huawei.it.fcst.profits.common.vo.ResultCode;
import com.huawei.it.fcst.profits.common.vo.UploadInfoVO;
import com.huawei.it.fcst.profits.dao.ILabelOperateLogDao;
import com.huawei.it.fcst.profits.service.IExportService;
import com.huawei.it.fcst.profits.service.ISpartProfitingRelationService;
import com.huawei.it.fcst.profits.thread.SpartRelationExportThread;
import com.huawei.it.fcst.profits.thread.SpartRelationMainSubmitThread;
import com.huawei.it.fcst.profits.thread.SpartRelationMainThread;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.utils.HeaderUtils;
import com.huawei.it.fcst.profits.vo.LabelCountVO;
import com.huawei.it.fcst.profits.vo.LabelOperateLogVO;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.LabelConfigRequest;
import com.huawei.it.fcst.profits.vo.request.LabelInfoRequest;
import com.huawei.it.jalor5.core.base.PageConfig;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.servlet.http.HttpServletResponse;

@Service
public class SpartProfitingRelationService extends AbstractService implements ISpartProfitingRelationService {
    /**
     * 标签审视提交
     */
    private static final String LABEL_SUBMIT = "label_submit";

    /**
     * 标签审视导入
     */
    private static final String LABEL_IMPORT = "label_import";

    /**
     * 否
     */
    private static final String IS_NOT = "否";

    private static final int BATCH_NUM = 5000;

    /**
     * 初始化默认时间配置
     */
    private static final Map<String, Integer> INIT_DATE = new HashMap<>();

    static {
        INIT_DATE.put("startDay", 7);
        INIT_DATE.put("endDay", 20);
    }

    private static final Logger logger = LoggerFactory.getLogger(SpartProfitingRelationService.class);

    @Inject
    private IExportService iExportService;

    @Inject
    ILabelOperateLogDao iOperateLogDao;

    /**
     * 分页查询信息
     *
     * @param requestVO 请求vo
     * @return PagedResult分页结果
     */
    @Override
    public PagedResult<SpartProfitingRelationVO> findByPage(LabelConfigRequest requestVO) throws CommonApplicationException {
        getRequestSystemUserId(requestVO);
        PageVO pageVO = new PageVO();
        // 默认设置分页最大参数
        PageConfig.DEFAULT.setMaxPageSize(RelationConstants.PAGE_MAX_SIZE);
        pageVO.setPageSize(requestVO.getPageSize() > RelationConstants.PAGE_MAX_SIZE ? RelationConstants.PAGE_MAX_SIZE : requestVO.getPageSize());
        pageVO.setCurPage(requestVO.getPageIndex());
        CommUtils.buildReqParams(requestVO);
        if (checkLv1Result(requestVO)) {
            return new PagedResult<>();
        }
        PagedResult<SpartProfitingRelationVO> spartProfitingResult = iSpartProfitingRelationDao.findByPage(requestVO, pageVO);
        List<SpartProfitingRelationVO> result = spartProfitingResult.getResult();
        if (CollectionUtils.isEmpty(result)) {
            return spartProfitingResult;
        }
        // 收集用户id信息，方便批量查询中文名称
        Set<Long> userIds = new HashSet<>();
        for (SpartProfitingRelationVO spartProfitingRelationVO : result) {
            if (spartProfitingRelationVO.getCreatedBy() != null && spartProfitingRelationVO.getCreatedBy() != -1 && spartProfitingRelationVO.getCreatedBy() != 0) {
                userIds.add(spartProfitingRelationVO.getCreatedBy());
            }
            if (spartProfitingRelationVO.getLastUpdatedBy() != null && spartProfitingRelationVO.getLastUpdatedBy() != -1 && spartProfitingRelationVO.getLastUpdatedBy() != 0) {
                userIds.add(spartProfitingRelationVO.getLastUpdatedBy());
            }
        }
        Map<Long, String> resultMap = new HashMap<>();
        resultMap.put(-1L, "system");
        resultMap.put(0L, "system");
        resultMap.put(null, "system");
        if (CollectionUtils.isNotEmpty(userIds)) {
            resultMap.putAll(getUserCnName(userIds));
        }
        // 根据用户id匹配对应的用户中文名称
        result.stream().forEach(spartProfit -> {
            spartProfit.setCreatedCn(resultMap.get(spartProfit.getCreatedBy()));
            spartProfit.setLastUpdateCn(resultMap.get(spartProfit.getLastUpdatedBy()));
        });
        spartProfitingResult.setResult(result);
        return spartProfitingResult;
    }

    // 权限检验
    private boolean checkLv1Result(LabelConfigRequest requestVO) {
        UserVO curUser = (UserVO) RequestContext.getCurrent(true).getUser();
        RoleVO currentRole = curUser.getCurrentRole();
        DataPermissionsVO rightVO = getUserRoleOpt(currentRole.getRoleId());
        // 校验权限信息
        if (!rightVO.isOptLv1Right()) {
            return true;
        }
        if (!StringUtils.equals("ALL", rightVO.getLv1DataType())) {
            List<String> lv1s = new ArrayList<>();
            Arrays.asList(rightVO.getLv1DimensionSet().toArray(new String[0])).stream().forEach(lv1 -> {
                if (RelationConstants.LV1_FULL_MAPS.containsKey(lv1)) {
                    lv1s.add(RelationConstants.LV1_FULL_MAPS.get(lv1));
                }
            });
            // 无产业权限
            if (CollectionUtils.isEmpty(lv1s)) {
                return true;
            }
            // 查询为全量产业，则设置查询为当前权限范围
            if (Objects.isNull(requestVO.getLv1List())) {
                requestVO.setLv1List(lv1s);
            } else {
                // 否则查询产业为交集
                List<String> list = lv1s.stream().filter(requestVO.getLv1List()::contains).collect(Collectors.toList());
                if (CollectionUtil.isNullOrEmpty(list)) {
                    return true;
                }
                requestVO.setLv1List(list);
            }
        }
        return false;
    }

    /**
     * 获取lv1列表信息
     *
     * @return List
     */
    @Override
    public List<SpartProfitingRelationVO> getLv1List(LabelConfigRequest labelConfigRequest) throws CommonApplicationException {
        DataPermissionsVO rightVO = getUserRoleOpt(labelConfigRequest.getRoleId());
        // 校验权限信息
        if (!rightVO.isOptLv1Right()) {
            return new ArrayList<>();
        }
        labelConfigRequest.setLv1List(null);
        if (!StringUtils.equals("ALL", rightVO.getLv1DataType())) {
            List<String> lv1s = new ArrayList<>();
            Arrays.asList(rightVO.getLv1DimensionSet().toArray(new String[0])).stream().forEach(lv1 -> {
                if (RelationConstants.LV1_FULL_MAPS.containsKey(lv1)) {
                    lv1s.add(RelationConstants.LV1_FULL_MAPS.get(lv1));
                }
            });
            labelConfigRequest.setLv1List(lv1s);
        }
        // 根据不同的QueryType查询不同的SQL。type: config(标签配置)、review(标签审视)
        if ("config".equals(labelConfigRequest.getQueryType())) {
            return iSpartProfitingRelationDao.getLv1List(labelConfigRequest);
        } else {
            return iSpartProfitingRelationDao.getAuditLv1Names(labelConfigRequest);
        }
    }

    /**
     * 获取lv1列表信息
     *
     * @return List
     */
    public List<String> getSpartConfInfo() {
        List<String> spartList = iSpartProfitingRelationDao.getSpartConfInfo();
        if (CollectionUtils.isEmpty(spartList)) {
            return new ArrayList<>();
        }
        return spartList;
    }

    /**
     * 获取l1名称列表信息
     *
     * @return List
     */
    @Override
    public List<SpartProfitingRelationVO> getL1NameList(LabelConfigRequest request) throws CommonApplicationException {
        CommUtils.buildReqParams(request);
        if (checkLv1Result(request)) {
            return new ArrayList<>();
        }
        // 根据不同的QueryType查询不同的SQL。type: config(标签配置)、review(标签审视)
        if ("config".equals(request.getQueryType())) {
            return iSpartProfitingRelationDao.getL1NameList(request);
        } else {
            return iSpartProfitingRelationDao.getAuditL1Names(request);
        }
    }

    /**
     * 获取l2名称列表信息
     *
     * @param request 请求vo
     * @return List
     */
    @Override
    public List<SpartProfitingRelationVO> getL2NameList(LabelConfigRequest request) throws CommonApplicationException {
        CommUtils.buildReqParams(request);
        if (checkLv1Result(request)) {
            return new ArrayList<>();
        }
        // 根据不同的QueryType查询不同的SQL。type: config(标签配置)、review(标签审视)
        List<SpartProfitingRelationVO> list = null;
        if ("config".equals(request.getQueryType())) {
            list = iSpartProfitingRelationDao.getL2NameList(request);
        } else {
            list = iSpartProfitingRelationDao.getAuditL2Names(request);
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        return list.stream().sorted(Comparator.comparing(SpartProfitingRelationVO::getL2Name, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
    }

    /**
     * 获取l3名称列表信息
     *
     * @param request 请求vo
     * @return List
     */
    @Override
    public List<SpartProfitingRelationVO> getL3NameList(LabelConfigRequest request) throws CommonApplicationException {
        CommUtils.buildReqParams(request);
        if (checkLv1Result(request)) {
            return new ArrayList<>();
        }
        List<SpartProfitingRelationVO> list = null;
        // 根据不同的QueryType查询不同的SQL。type: config(标签配置)、review(标签审视)
        if ("config".equals(request.getQueryType())) {
            list = iSpartProfitingRelationDao.getL3NameList(request);
        } else {
            list = iSpartProfitingRelationDao.getAuditL3Names(request);
        }
        // 筛选出数据库为NULL的数据，转换后返回前端
        AtomicBoolean flag = new AtomicBoolean(false);
        list = list.stream().filter(item -> {
            if (item == null) {
                flag.set(true);
                return false;
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        // 存在NULL值，转化为名字为NULL的数据项
        if (flag.get()) {
            SpartProfitingRelationVO nullVo = new SpartProfitingRelationVO();
            nullVo.setL3Name("NULL");
            list.add(nullVo);
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        return list.stream().sorted(Comparator.comparing(SpartProfitingRelationVO::getL3Name, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
    }

    /**
     * 获取版本信息
     *
     * @return List
     */
    @Override
    public List<String> getVersions() throws CommonApplicationException {
        // 删掉缓存* modify by pengjian 00391034
        return iSpartProfitingRelationDao.getVersions();
    }

    /**
     * 根据条件分页查询
     *
     * @param request
     * @return PagedResult分页结果
     */
    @Override
    public PagedResult<SpartProfitingRelationVO> findConfigByPage(LabelConfigRequest request) throws CommonApplicationException {
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(request.getPageSize());
        pageVO.setCurPage(request.getPageIndex());
        CommUtils.buildReqParams(request);
        if (checkLv1Result(request)) {
            return new PagedResult<>();
        }
        return iSpartProfitingRelationDao.findConfigByPage(request, pageVO);
    }

    /**
     * 获取复盘率信息
     *
     * @param year 年份
     * @return List
     */
    @Override
    public List<LabelCountVO> getManualModifyInfo(String year, String roleId) throws CommonApplicationException {
        DataPermissionsVO rightVO = getUserRoleOpt(Integer.parseInt(roleId));
        // 校验权限信息
        if (!rightVO.isOptLv1Right()) {
            return new ArrayList<>();
        }
        List<String> lv1s = new ArrayList<>();
        if (!StringUtils.equals("ALL", rightVO.getLv1DataType())) {
            Arrays.asList(rightVO.getLv1DimensionSet().toArray(new String[0])).stream().forEach(lv1 -> {
                if (RelationConstants.LV1_FULL_MAPS.containsKey(lv1)) {
                    lv1s.add(RelationConstants.LV1_FULL_MAPS.get(lv1));
                }
            });
        }
        // 获取全年会计期
        List<String> yearPeriods = CommUtils.buildYearMonthList(year);
        return getLabelMonthList(lv1s, yearPeriods);
    }

    /**
     * 获取标签统计值
     *
     * @param lv1s        lv1Name 集合
     * @param yearPeriods 会计期
     * @return List
     */
    private List<LabelCountVO> getLabelMonthList(List<String> lv1s, List<String> yearPeriods) {
        List<LabelCountVO> monthData = iSpartProfitingRelationDao.getYearStatics(yearPeriods, lv1s);
        if (CollectionUtils.isEmpty(monthData)) {
            return Collections.EMPTY_LIST;
        }
        List<LabelCountVO> resultList = new ArrayList<>();
        // 按照lvName分组数据
        Map<String, List<LabelCountVO>> map = monthData.stream().collect(Collectors.groupingBy(LabelCountVO::getLv1Name));
        map.forEach((lv1Name, value) -> {
            if (StringUtils.isEmpty(lv1Name)) {
                return;
            }
            List<LabelCountVO> tempList = new ArrayList<>();
            LabelCountVO labelRecountVO = new LabelCountVO();
            labelRecountVO.setLv1Name(lv1Name);
            // lvName分组后，按照periodId 分组，组织数据结构
            Map<String, List<LabelCountVO>> valuesMap = value.stream().collect(Collectors.groupingBy(LabelCountVO::getPeriodId));
            yearPeriods.forEach(month -> {
                if (valuesMap != null && valuesMap.get(month) != null) {
                    tempList.addAll(valuesMap.get(month));
                } else {
                    LabelCountVO labelCountVO = initLabelCount(month, lv1Name);
                    tempList.add(labelCountVO);
                }
            });
            labelRecountVO.setLabelRecountVOList(tempList);
            resultList.add(labelRecountVO);
        });
        return resultList;
    }

    /**
     * 多线程保存数据
     *
     * @param updateFlag  更新标识
     * @param requestList 请求信息列表
     */
    @Override
    public void saveData(String updateFlag, List<LabelInfoRequest> requestList) {
        EXECUTOR_SERVICE.execute(new SpartRelationMainThread(LabelOperateLogVO.builder().isSkip(true).build(), UserHandle.getUserId(), requestList, EXECUTOR_SERVICE, TimeUtils.getCurTime(), updateFlag));
    }

    /**
     * 多线程提交数据
     */
    @Override
    public CommonResult submitData() {
        Long userId = UserHandle.getUserId();
        Integer waitCount = iSpartProfitingRelationDao.getWaitCount(userId);
        if (waitCount == 0) {
            return CommonResult.failed(ResultCode.CHECK_TIP, "当前没有数据可以提交");
        }
        int copyDataCount = iSpartProfitingRelationDao.findCopyDataCount(null, userId);
        if (copyDataCount > 0) {
            return CommonResult.failed(ResultCode.CHECK_TIP, "异步提交数据中，请等待异步操作完成后再提交");
        } else {
            // 开始记录日志
            String objectId = ObjectUtils.genRanCode();
            LabelOperateLogVO labelOperateLogVO = LabelOperateLogVO.builder().isSkip(false).status("START").countNum(waitCount).objectId(objectId).methodName(LABEL_SUBMIT).userId(userId).build();
            iOperateLogDao.insertLongInfo(labelOperateLogVO);
            EXECUTOR_SERVICE.execute(new SpartRelationMainSubmitThread(labelOperateLogVO, userId, EXECUTOR_SERVICE));
            return CommonResult.success(labelOperateLogVO, "提交成功");
        }
    }


    /**
     * 导入数据
     *
     * @param attachment 导入信息内容
     * @throws CommonApplicationException 异常
     */
    @Override
    public CommonResult importData(Attachment attachment, String fileName) throws CommonApplicationException {
        Map<String, Object> params = new HashMap<>();
        Timestamp curTime = TimeUtils.getCurTime();
        Long userId = UserHandle.getUserId();
        params.put("module", ModuleEnum.MODULE_AUDIT.getDesc());
        Map<String, String> resultMap = new HashMap<>();
        FileProcessUtis.getFileName(attachment, resultMap);
        // 设置上传参数
        UploadInfoVO uploadInfoVO = setUploadInfoVO(attachment, params, curTime, userId, resultMap);
        List<ExcelVO> excelList = new ArrayList();
        // 构建Excel表头信息
        HeaderUtils.buildHeaders(excelList, HeaderUtils.AUDIT_CHILD_IMP_HEADER);
        excelUtil.readExcel(attachment, excelList, uploadInfoVO);
        if (StringUtils.isNotBlank(uploadInfoVO.getErrorTips())) {
            String errMsg = "输入值不合法";
            if (uploadInfoVO.getErrorTips().indexOf("请导入Excel") >= 0) {
                errMsg = uploadInfoVO.getErrorTips();
            }
            excelUtil.insertErrorRecord(uploadInfoVO, errMsg);
            return CommonResult.failed(ResultCode.CHECK_TIP, errMsg);
        }
        if (CollectionUtils.isEmpty(uploadInfoVO.getDataList())) {
            uploadInfoVO.setRowNumber(0);
            excelUtil.insertErrorRecord(uploadInfoVO, "导入模板为空,请检查！");
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入模板为空,请检查！");
        }
        List<String> checkItemCodeList = checkItemCode(uploadInfoVO);
        if(uploadInfoVO.getCount() == 0 && !CollectionUtil.isNullOrEmpty(checkItemCodeList)){
            excelUtil.insertErrorRecord(uploadInfoVO, "请检查spart编码是否存在");
            return CommonResult.failed(checkItemCodeList);
        }
        List<LabelInfoRequest> invalidDataList = deleteInvalidDataList(uploadInfoVO);
        // 保留有效数据
        List<LabelInfoRequest> validDataList  = uploadInfoVO.getDataList().stream().filter(item -> !IS_NOT.equals(item.getIsVaild())).collect(Collectors.toList());
        uploadInfoVO.setDataList(validDataList);
        // excel只有无效的数据，没有有效数据，就是只删除数据操作，直接记录日志，返回成功
        if (invalidDataList.size() !=0 && validDataList.size() ==0 ) {
            // 开始记录日志
            String objectId = ObjectUtils.genRanCode();
            LabelOperateLogVO labelOperateLogVO = LabelOperateLogVO.builder().isSkip(false).countNum(uploadInfoVO.getDataList().size()).objectId(objectId).methodName(LABEL_IMPORT).status("SUCCESS").userId(userId).build();
            iOperateLogDao.insertLongInfo(labelOperateLogVO);
            creatRecord(uploadInfoVO, RecStsEnum.SUCCESS.getCode(), "", TimeUtils.getCurPeriod(), uploadInfoVO.getFileKey(), "", OptTypeEnum.IMPORT.getCode());
            return CommonResult.success(labelOperateLogVO, "导入成功");
        }
        if (uploadInfoVO.getCount() == 0) {
            // 开始记录日志
            String objectId = ObjectUtils.genRanCode();
            LabelOperateLogVO labelOperateLogVO = LabelOperateLogVO.builder().isSkip(false).countNum(uploadInfoVO.getDataList().size()).objectId(objectId).methodName(LABEL_IMPORT).status("START").userId(userId).build();
            iOperateLogDao.insertLongInfo(labelOperateLogVO);
            EXECUTOR_SERVICE.execute(new SpartRelationMainThread(labelOperateLogVO, userId, uploadInfoVO.getDataList(), EXECUTOR_SERVICE, curTime, "N"));
            creatRecord(uploadInfoVO, RecStsEnum.SUCCESS.getCode(), "", TimeUtils.getCurPeriod(), uploadInfoVO.getFileKey(), "", OptTypeEnum.IMPORT.getCode());
            return CommonResult.success(labelOperateLogVO, "导入成功");
        } else {
            logErrorRecord(uploadInfoVO, uploadInfoVO.getDataList());
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    @NotNull
    private List<LabelInfoRequest> deleteInvalidDataList(UploadInfoVO uploadInfoVO) throws CommonApplicationException {
        List<LabelInfoRequest> invalidDataList  = uploadInfoVO.getDataList().stream().filter(item -> IS_NOT.equals(item.getIsVaild())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(invalidDataList)) {
            invalidDataList.stream().forEach(vo -> {
                if (null == vo.getPeriodId()) {
                    vo.setPeriodId(Long.valueOf(TimeUtils.getCurPeriod()));
                }
                if (StringUtils.isEmpty(vo.getL2Name())) {
                    vo.setL2Name("snull");
                }
                if (StringUtils.isEmpty(vo.getL3Name())) {
                    vo.setL3Name("snull");
                }
            });
            // 删除无效数据
            iSpartProfitingRelationDao.deleteInvalidList(invalidDataList);
        }
        return invalidDataList;
    }

    private UploadInfoVO setUploadInfoVO(Attachment attachment, Map<String, Object> params, Timestamp curTime, Long userId, Map<String, String> resultMap) {
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setStartTime(curTime);
        uploadInfoVO.setSuffix(".xlsx");
        uploadInfoVO.setFileName(resultMap.get("prefix"));
        uploadInfoVO.setFileSize(FileProcessUtis.getFileSize(attachment));
        uploadInfoVO.setParams(params);
        uploadInfoVO.setUserId(userId);
        uploadInfoVO.setOptFlag(false);
        uploadInfoVO.setCheckList(getSpartConfInfo());
        uploadInfoVO.setSuffix(resultMap.get("suffix"));
        return uploadInfoVO;
    }

    /**
     * 校验itemCode是否存在
     *
     * @param uploadInfoVO
     * @return
     */
    private List<String> checkItemCode(UploadInfoVO uploadInfoVO){
        final List<String> itemCodeList = uploadInfoVO.getDataList().stream().map(LabelInfoRequest::getItemCode).distinct().collect(Collectors.toList());
        final int itemCoumNum = itemCodeList.size();
        final int num = itemCoumNum % BATCH_NUM == 0 ? itemCoumNum / BATCH_NUM : itemCoumNum / BATCH_NUM + 1;
        List<String> errorList = new ArrayList<>();
        for (int i = 0; i < num; i++) {
            final int size = (i + 1) * BATCH_NUM < itemCoumNum ?  (i + 1) * BATCH_NUM : itemCoumNum ;
            List<String> findSubList = itemCodeList.subList(i * BATCH_NUM, size);
            // 查询数据
            List<String> resultList = iSpartProfitingRelationDao.findItemCode(findSubList);
            if(findSubList.size() != resultList.size()){
                // 求差集findSubList - resultList
                errorList.addAll(findSubList.stream().filter(str -> !resultList.contains(str)).collect(Collectors.toList()));
            }
        }
        return errorList;
    }

    /**
     * 记录错误日志信息
     *
     * @param uploadInfoVO
     * @param verifyDataList
     */
    public void logErrorRecord(UploadInfoVO uploadInfoVO, List<LabelInfoRequest> verifyDataList) {
        Map params = new ConcurrentHashMap<>();
        params.put("fileName", "对象维表-导入异常反馈-");
        String errorFileKey = uploadInfoVO.getFileKey();
        if (CollectionUtils.isNotEmpty(verifyDataList)) {
            iExportService.exportAuditData(params, String.valueOf(uploadInfoVO.getUserId()), CommUtils.buildImpData(verifyDataList));
            uploadInfoVO.setFileName(String.valueOf(params.get("fileName")));
            if (Objects.nonNull(params.get("fileKey"))) {
                errorFileKey = String.valueOf(params.get("fileKey"));
            }
        }
        creatRecord(uploadInfoVO, RecStsEnum.FAIL.getCode(), CommonConstant.ERROR_INPUT_DATA, TimeUtils.getCurPeriod(), "", errorFileKey, OptTypeEnum.IMPORT.getCode());
    }

    /**
     * 导出数据
     *
     * @param response 响应信息
     * @throws CommonApplicationException 异常
     */
    @Override
    public CommonResult exportData(HttpServletResponse response, LabelConfigRequest labelConfigRequest) {
        getRequestSystemUserId(labelConfigRequest);
        String[] lv1Name = labelConfigRequest.getLv1Name();
        List<String> lv1NameList = Arrays.asList(lv1Name);
        // 如果包含历史标签，需要校验，只能单个产业导出
        if (labelConfigRequest.getPageSize() > 300000) {
            return CommonResult.failed(ResultCode.CHECK_TIP, "当前导出的数据量过大，请限制导出数据在30万以下");
        }
        if (checkLv1Result(labelConfigRequest)) {
            return CommonResult.failed(ResultCode.CHECK_TIP, "导出的数据为空");
        }
        CopyOnWriteArrayList<SpartProfitingRelationVO> copyList = new CopyOnWriteArrayList<>();
        try {
            final CountDownLatch countDownLatch = new CountDownLatch(lv1NameList.size());
            for (int i = 0; i < lv1NameList.size(); i++) {
                LabelConfigRequest requestVO = new LabelConfigRequest();
                BeanUtils.copyProperties(labelConfigRequest, requestVO);
                requestVO.setLv1Name(new String[]{lv1NameList.get(i)});
                EXECUTOR_SERVICE.execute(new SpartRelationExportThread(requestVO, countDownLatch, copyList));
            }
            countDownLatch.await();
            if (CollectionUtils.isEmpty(copyList)) {
                return CommonResult.failed(ResultCode.CHECK_TIP, "导出的数据为空");
            }
            List<Map<String, Object>> resultList = new ArrayList<>();
            setUserExtendInfo(copyList, labelConfigRequest, resultList);
            // 转换L1,L2,L3系数
            Map<String, Object> params = new ConcurrentHashMap<>();
            params.put("userId", UserHandle.getUserId());
            String fileName = CommonConstant.TEMPLATE_AUDIT_EXP_SHEET_NAME + labelConfigRequest.getPeriodId() + CommonConstant.TEMPLATE_AUDIT_SHEET_SUFFIX_NAME;
            params.put("fileName", fileName);
            iExportService.exportAuditData(params, resultList, response);
            creatRecord(fileName, Long.valueOf(String.valueOf(params.get("fileSize"))), SaveTypeEnum.SAVE.getCode(), "", resultList.size(), String.valueOf(labelConfigRequest.getPeriodId()), String.valueOf(params.get("fileKey")), null, OptTypeEnum.EXPORT.getCode(), RecStsEnum.SUCCESS.getCode(), ModuleEnum.MODULE_AUDIT.getDesc());
            return CommonResult.success(ResultCode.CHECK_TIP, "导出成功");
        } catch (Exception ex) {
            logger.error("标签数据导出exportData occurs error: {}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导出失败");
        }
    }

    @Override
    public List<Map<String, Object>> getLastUpdatedBys(LabelConfigRequest request) throws CommonApplicationException {
        List<Map<String, Object>> mapList = new ArrayList<>();
        try {
            Map<Long, String> resultMap = new HashMap<>();
            if (checkLv1Result(request)) {
                return Collections.EMPTY_LIST;
            }
            Set<Long> userIds = iSpartProfitingRelationDao.getLastUpdatedBys(request);
            if (CollectionUtils.isEmpty(userIds)) {
                return Collections.EMPTY_LIST;
            }
            Set<Long> ids = userIds.stream().filter(userId -> (userId != null && String.valueOf(userId).length() > 8)).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(ids)) {
                final Map<Long, String> userCnName = this.getUserCnName(ids);
                for (Long userId : ids) {
                    resultMap.put(userId, userCnName.get(userId));
                }
            }
            Set<Long> systemId = userIds.stream().filter(userId -> (userId == null || String.valueOf(userId).length() < 8)).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(systemId)) {
                resultMap.put(0L, "system");
            }
            if (resultMap.isEmpty()) {
                return Collections.EMPTY_LIST;
            }
            Map<String, Object> value = null;
            for (Map.Entry<Long, String> map : resultMap.entrySet()) {
                value = new HashMap<>(4);
                value.put("lastUpdatedBy", map.getKey());
                value.put("lastUpdatedByName", map.getValue());
                mapList.add(value);
            }
        } catch (HttpServerErrorException exception) {
            throw new CommonApplicationException("获取用户信息失败");
        }
        return mapList;
    }

    @Override
    public List<SpartProfitingRelationVO> getStatusList(LabelConfigRequest request) throws CommonApplicationException {
        return iSpartProfitingRelationDao.getStatusList(request);
    }

    @Override
    public LabelOperateLogVO getOperateRecordLogStatus(LabelOperateLogVO labelOperateLogVO) throws CommonApplicationException {
        if (StringUtils.isEmpty(labelOperateLogVO.getObjectId())) {
            labelOperateLogVO.setUserId(UserInfo.getUserId());
            return iOperateLogDao.getByUserOperateRecordLog(labelOperateLogVO);
        }
        return iOperateLogDao.getOperateRecordLogStatus(labelOperateLogVO.getObjectId());
    }

    private void getRequestSystemUserId(LabelConfigRequest labelConfigRequest) {
        if (Objects.nonNull(labelConfigRequest) && !CollectionUtils.isEmpty(labelConfigRequest.getLastUpdatedBys())) {
            Set<Long> systemId = labelConfigRequest.getLastUpdatedBys().stream().filter(userId -> (userId == null || String.valueOf(userId).length() < 8)).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(systemId)) {
                Set<Long> userIds = iSpartProfitingRelationDao.getLastUpdatedBys(labelConfigRequest);
                Set<Long> tempSet = userIds.stream().filter(userId -> (userId == null || String.valueOf(userId).length() < 8)).collect(Collectors.toSet());
                labelConfigRequest.getLastUpdatedBys().addAll(tempSet);
            }
        }
    }
}
