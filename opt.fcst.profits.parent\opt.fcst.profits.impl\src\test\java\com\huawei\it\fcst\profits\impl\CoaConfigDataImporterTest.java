package com.huawei.it.fcst.profits.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

import com.huawei.it.fcst.profits.common.poi.ExcelUtil;
import com.huawei.it.fcst.profits.common.utils.FileProcessUtis;
import com.huawei.it.fcst.profits.common.vo.ExcelVO;
import com.huawei.it.fcst.profits.common.vo.UploadInfoVO;
import com.huawei.it.fcst.profits.dao.IDmFopRecordDao;
import com.huawei.it.fcst.profits.service.config.impl.CoaConfigDataImporter;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.vo.CoaConfigDataVO;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import cn.hutool.core.bean.BeanUtil;

import org.junit.Test;
import org.junit.Before;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * CoaConfigDataImporter Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>3月 6, 2023</pre>
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({FileProcessUtis.class, CommUtils.class})
public class CoaConfigDataImporterTest {
    @InjectMocks
    CoaConfigDataImporter coaConfigDataImporter;
    @Mock
    protected ExcelUtil excelUtil;
    @Mock
    IDmFopRecordDao iDmFopRecordDao;
    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    /**
     * Method: getHeadList()
     */
    @Test
    public void testGetHeadList() throws Exception {
        List<ExcelVO> headList = coaConfigDataImporter.getHeadList();
        Assertions.assertEquals(false, headList.isEmpty());
    }

    @Test
    public void testLogErrorRecord() throws Exception {
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName("test.xlsx");
        uploadInfoVO.setFileKey("XXXXXXXXXXXXXXXXXXXXXX");
        uploadInfoVO.setUserId(1250L);
        uploadInfoVO.setFileSize(1250L);
        uploadInfoVO.setRowNumber(1);
        List dataList = new ArrayList<>();
        dataList.add(new CoaConfigDataVO());
        mockStatic(FileProcessUtis.class);
        //(targetFile, fileName, String.valueOf(params.get("userId"))));
        when(FileProcessUtis.class, "uploadToS3", any(), anyString(), anyString()).thenReturn("XXXXXXXXXXXXXXXX");
        coaConfigDataImporter.logErrorRecord(uploadInfoVO, dataList);

        Assertions.assertNotNull(uploadInfoVO);
    }
    @Test
    public void testLogErrorRecordA() throws Exception {
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName("test.xlsx");
        uploadInfoVO.setFileKey("XXXXXXXXXXXXXXXXXXXXXX");
        uploadInfoVO.setUserId(1250L);
        uploadInfoVO.setFileSize(1250L);
        uploadInfoVO.setRowNumber(1);
        List dataList = new ArrayList<>();
        mockStatic(FileProcessUtis.class);
        //(targetFile, fileName, String.valueOf(params.get("userId"))));
        when(FileProcessUtis.class, "uploadToS3", any(), anyString(), anyString()).thenReturn("XXXXXXXXXXXXXXXX");
        coaConfigDataImporter.logErrorRecord(uploadInfoVO, dataList);
        Assertions.assertNotNull(uploadInfoVO);
    }
    @Test
    public void testLogErrorRecordB() throws Exception {
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName("test.xlsx");
        uploadInfoVO.setUserId(1250L);
        uploadInfoVO.setFileSize(1250L);
        uploadInfoVO.setRowNumber(1);
        List dataList = new ArrayList<>();
        dataList.add(new CoaConfigDataVO());
        mockStatic(FileProcessUtis.class);
        //(targetFile, fileName, String.valueOf(params.get("userId"))));
        when(FileProcessUtis.class, "uploadToS3", any(), anyString(), anyString()).thenReturn("");
        coaConfigDataImporter.logErrorRecord(uploadInfoVO, dataList);
        Assertions.assertNotNull(uploadInfoVO);
    }
    /**
     * Method: setNormalInfoAndCheckRepeated(List<Object> dataList, Map<String, Object> dataMap, AtomicInteger counterRef, Map<String, Object> preparedInfo)
     */
    @Test
    public void testSetNormalInfoAndCheckRepeated() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        Map<String, Object> dataMap = BeanUtil.beanToMap(new CoaConfigDataVO());
        coaConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(),dataMap,new AtomicInteger(),new HashMap<>());
        Assertions.assertNotNull(dataMap);
    }

    /**
     * Method: setVoDataInfoAndCheck(List<Object> dataList, String title, String value, AtomicInteger atomicInteger, Map<String, Object> keys, Map<String, Object> infoMap)
     */
    @Test(expected = NullPointerException.class)
    public void testSetVoDataInfoAndCheck() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String []titles = {"产业","L1名称","L2名称","产品编码","NULL"};
        String value = "Test";
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName","TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);

        for (int i = 0; i < titles.length; i++) {
            coaConfigDataImporter.setVoDataInfoAndCheck(dataList, titles[i], value, new AtomicInteger(), key,
                new HashMap<>());
        }
        Assertions.assertNotNull(key);
    }
    @Test
    public void testSetVoDataInfoAndCheckA() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String []titles = {"产业","L1名称","L2名称","产品编码"};
        String value = null;
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        // strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName","TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);
        for (int i = 0; i < titles.length; i++) {
            coaConfigDataImporter.setVoDataInfoAndCheck(dataList, titles[i], value, new AtomicInteger(), key,
                new HashMap<>());
        }
        Assertions.assertNotNull(key);
    }
    @Test
    public void testSetVoDataInfoAndCheckB() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String titles = "产业";
        String value = "TEst";
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName","TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);
        Map<String, Object> infpMap = new HashMap<>();
        infpMap.put("errorMsg","Test");
        coaConfigDataImporter.setVoDataInfoAndCheck(dataList, titles, value, new AtomicInteger(), key,
            infpMap);
        Assertions.assertNotNull(key);
    }
    @Test
    public void testGetLegalTitleMap() throws Exception {
        coaConfigDataImporter.getLegalTitleMap();
        Assertions.assertNotNull(new Object());
    }
    @Test
    public void testSetVoDataInfoAndCheckC() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String titles = "L1名称";
        String value = "Test";
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName","TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);
        Map<String, Object> infpMap = new HashMap<>();
        infpMap.put("errorMsg","Test");
        List<String> l1NameList = new LinkedList<>();
        l1NameList.add("Test");
        key.put("l1NameList",l1NameList);
        coaConfigDataImporter.setVoDataInfoAndCheck(dataList, titles, value, new AtomicInteger(), key,
            infpMap);
        Assertions.assertNotNull(key);
    }
    @Test
    public void testSetVoDataInfoAndCheckD() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String titles = "L1名称";
        String value = "Test123";
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName","TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);
        Map<String, Object> infpMap = new HashMap<>();
        infpMap.put("errorMsg","Test");
        List<String> l1NameList = new LinkedList<>();
        l1NameList.add("Test");
        key.put("l1NameList",l1NameList);
        coaConfigDataImporter.setVoDataInfoAndCheck(dataList, titles, value, new AtomicInteger(), key,
            infpMap);
        Assertions.assertNotNull(key);
    }
    @Test
    public void testSetVoDataInfoAndCheckF() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String titles = "L1名称123";
        String value = "Test123";
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName","TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);
        Map<String, Object> infpMap = new HashMap<>();
        infpMap.put("errorMsg","Test");
        List<String> l1NameList = new LinkedList<>();
        l1NameList.add("Test");
        key.put("l1NameList",l1NameList);
        coaConfigDataImporter.setVoDataInfoAndCheck(dataList, titles, value, new AtomicInteger(), key,
            infpMap);
        Assertions.assertNotNull(key);
    }
    @Test
    public void testSetVoDataInfoAndCheckE() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String titles = "L2名称";
        String value = "Test123";
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName","TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);
        Map<String, Object> infpMap = new HashMap<>();
        infpMap.put("errorMsg","Test");
        List<String> l1NameList = new LinkedList<>();
        l1NameList.add("Test");
        key.put("l1NameList",l1NameList);
        coaConfigDataImporter.setVoDataInfoAndCheck(dataList, titles, value, new AtomicInteger(), key,
            infpMap);
        Assertions.assertNotNull(key);
    }
    @Test
    public void testSetVoDataInfoAndCheckZ() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String titles = "产品编码";
        String value = "Test123";
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName","TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);
        Map<String, Object> infpMap = new HashMap<>();
        infpMap.put("errorMsg","Test");
        List<String> l1NameList = new LinkedList<>();
        l1NameList.add("Test");
        key.put("l1NameList",l1NameList);
        coaConfigDataImporter.setVoDataInfoAndCheck(dataList, titles, value, new AtomicInteger(), key,
            infpMap);
        Assertions.assertNotNull(key);
    }
}
