/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * BoolEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum BoolEnum {
    TRUE("True", "True"),
    FALSE("False", "False");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    BoolEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

