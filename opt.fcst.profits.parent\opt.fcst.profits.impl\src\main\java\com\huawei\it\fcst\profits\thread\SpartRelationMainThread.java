/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.thread;

import java.sql.Timestamp;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.huawei.it.fcst.profits.common.constants.Constants;
import com.huawei.it.fcst.profits.common.constants.RelationConstants;
import com.huawei.it.fcst.profits.common.utils.CalcUtils;
import com.huawei.it.fcst.profits.dao.ILabelOperateLogDao;
import com.huawei.it.fcst.profits.vo.LabelOperateLogVO;
import com.huawei.it.fcst.profits.vo.request.LabelInfoRequest;
import com.huawei.it.jalor5.core.ioc.Jalor;

/**
 * 标签功能
 *
 * <AUTHOR>
 * @since 2022/10/02.
 */
public class SpartRelationMainThread implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(SpartRelationMainThread.class);

    private List<LabelInfoRequest> dataList;

    private Long userId;

    private Timestamp curTime;

    private String updateFlag;

    private ExecutorService executorService;

    private LabelOperateLogVO labelOperateLogVO;

    /**
     * 构造方法
     *
     * @param labelOperateLogVO 日志对象
     * @param userId 用户ID
     * @param dataList 操作数据
     * @param executorService 执行线程
     * @param curTime 时间
     * @param updateFlag 状态
     */
    public SpartRelationMainThread(LabelOperateLogVO labelOperateLogVO, Long userId, List<LabelInfoRequest> dataList, ExecutorService executorService, Timestamp curTime, String updateFlag) {
        this.dataList = dataList;
        this.userId = userId;
        this.updateFlag = updateFlag;
        this.executorService = executorService;
        this.curTime = curTime;
        this.labelOperateLogVO = labelOperateLogVO;
    }

    @Override
    public void run() {
        synchronized (SpartRelationMainThread.class) {
            int batchNum = dataList.size() / RelationConstants.BATCH_COUNT;
            final CountDownLatch countDownLatch = new CountDownLatch(CalcUtils.getLatchNum(dataList.size(), batchNum));
            String status = Constants.SUCCESS.getValue();
            try {
                for (int i = 0; i < batchNum; i++) {
                    executorService.execute(new SpartRelationThread(countDownLatch, labelOperateLogVO,userId, updateFlag, dataList.subList(i * RelationConstants.BATCH_COUNT, (i + 1) * RelationConstants.BATCH_COUNT), curTime));
                }
                if (dataList.size() % RelationConstants.BATCH_COUNT > 0) {
                    executorService.execute(new SpartRelationThread(countDownLatch, labelOperateLogVO,userId, updateFlag, dataList.subList(batchNum * RelationConstants.BATCH_COUNT, dataList.size()), curTime));
                }
                countDownLatch.await();
            } catch (Exception ex) {
                status = Constants.FAIL.getValue();
                logger.error("SpartRelationMainThread occurs error: {}", ex);
            }finally {
                upsertOperateStatus(status , labelOperateLogVO);
            }
        }
    }

    private void upsertOperateStatus(String status ,LabelOperateLogVO labelOperateLogVO) {
        if(labelOperateLogVO.getIsSkip()){
            return;
        }
        ILabelOperateLogDao operateLogDao = Jalor.getContext().getBean(ILabelOperateLogDao.class);
        operateLogDao.updateUpsertNum(LabelOperateLogVO.builder().objectId(labelOperateLogVO.getObjectId()).status(status).build());
    }
}