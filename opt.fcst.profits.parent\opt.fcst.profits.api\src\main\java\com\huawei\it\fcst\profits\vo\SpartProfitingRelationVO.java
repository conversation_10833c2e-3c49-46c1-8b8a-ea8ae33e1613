/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.huawei.it.fcst.profits.common.utils.FcstJsonFormatVisitorWrapper;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * The Entity of SpartProfitingRelationVO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-15 09:18:41
 */
@Getter
@Setter
public class SpartProfitingRelationVO {

    /**
     * ITEM编
     **/
    @JsonProperty("item_code")
    private String itemCode;

    /**
     * L2名称
     **/
    @JsonProperty("l2_name")
    private String l2Name;

    /**
     * LV1名称
     **/
    @JsonProperty("lv1_name")
    private String lv1Name;

    /**
     * LV1编码
     **/
    @JsonProperty("lv1_code")
    private String lv1Code;

    /**
     * 备注
     **/
    @JsonProperty("remark")
    private String remark;

    /**
     * L1系数预测概率
     **/
    @JsonProperty("l1_coefficient_prob")
    private BigDecimal l1CoefficientProb;

    /**
     * 修改人
     **/
    @JsonProperty("last_updated_by")
    private Long lastUpdatedBy;

    /**
     * L3名称
     **/
    @JsonProperty("l3_name")
    private String l3Name;

    /**
     * L3系数
     **/
    @JsonProperty("l3_coefficient")
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal l3Coefficient;

    /**
     * 会计期
     **/
    @JsonProperty("period_id")
    private Long periodId;

    /**
     * L3名称预测概率
     **/
    @JsonProperty("l3_name_prob")
    private BigDecimal l3NameProb;

    /**
     * L2系数
     **/
    @JsonProperty("l2_coefficient")
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal l2Coefficient;

    /**
     * 修改时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("last_update_date")
    private Timestamp lastUpdateDate;

    /**
     * 是否删除
     **/
    @JsonProperty("del_flag")
    private String delFlag;

    /**
     * ITEM描
     **/
    @JsonProperty("item_desc")
    private String itemDesc;

    /**
     * L1系数
     **/
    @JsonProperty("l1_coefficient")
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal l1Coefficient;

    /**
     * L2系数预测概率
     **/
    @JsonProperty("l2_coefficient_prob")
    private BigDecimal l2CoefficientProb;

    /**
     * L1名称
     **/
    @JsonProperty("l1_name")
    private String l1Name;

    /**
     * 修改标识（Y 是、N 否）
     **/
    @JsonProperty("update_flag")
    private String updateFlag;

    /**
     * 创建时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("creation_date")
    private Timestamp creationDate;

    /**
     * 创建人
     **/
    @JsonProperty("created_by")
    private Long createdBy;

    /**
     * L2名称预测概率
     **/
    @JsonProperty("l2_name_prob")
    private BigDecimal l2NameProb;

    /**
     * 数据类型（Manual 历史、AI 新增）
     **/
    @JsonProperty("data_type")
    private String dataType;

    /**
     * L3系数预测概率
     **/
    @JsonProperty("l3_coefficient_prob")
    private BigDecimal l3CoefficientProb;

    /**
     * 状态（SAVE 保存、SUBMIT 提交）
     **/
    private String status;

    /**
     * 错误信息
     */
    private String errorMessage;

    private String optType;

    private String unionKey;

    private String unionStatusKey;

    // 创建人中文名：eg:xiaoming WX12333
    private String createdCn;

    // 修改人中文名：eg:xiaoming WX12333
    private String lastUpdateCn;

    private List<SpartProfitingRelationVO> submitList;

}
