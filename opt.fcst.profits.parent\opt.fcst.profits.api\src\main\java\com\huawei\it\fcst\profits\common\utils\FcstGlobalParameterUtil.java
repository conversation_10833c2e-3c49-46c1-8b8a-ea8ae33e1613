/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.common.utils;

import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.registry.RegistryVO;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年06月06日
 */
public class FcstGlobalParameterUtil {
    private volatile static Map<String, RegistryVO> REGISTRY_CONFIG = new ConcurrentHashMap<>();
    private volatile static Map<String, List<LookupItemVO>> LOOKUP_CONFIG = new ConcurrentHashMap<>();
    private volatile static Map<String, List<String>> ICT_CONFIG = new ConcurrentHashMap<>();

    /**
     * 初始化设置数据字典值
     *
     * @param key   key
     * @param value value
     */
    public static void putRegistryValue(String key, RegistryVO value) {
        REGISTRY_CONFIG.put(key, value);
    }

    /**
     * 清理值
     */
    public static void cleanRegistryValue() {
        REGISTRY_CONFIG.clear();
    }

    /**
     * 存放lookup对象
     *
     * @param value lookup对象
     */
    public static void putAllLookupValue(Map<String, List<LookupItemVO>> value) {
        LOOKUP_CONFIG.putAll(value);
    }

    /**
     * 清理lookup值
     */
    public static void cleanLookupValue() {
        LOOKUP_CONFIG.clear();
    }

    /**
     * 存放ICT维表对象
     *
     * @param value lookup对象
     */
    public static void putConfigValue(Map<String, List<String>> value) {
        ICT_CONFIG.putAll(value);
    }

    /**
     * 清理ICT维表对象
     */
    public static void cleanConfigValue() {
        ICT_CONFIG.clear();
    }

    /**
     * 获取ICT维表对象
     */
    public static Map<String,List<String>> getConfigValue() {
        return ICT_CONFIG;
    }

    /**
     * 根据路径和name获取数据字典值
     *
     * @param path 路径
     * @param key  name
     * @return value
     */
    public static String getRegistryValue(String path, String key) {
        RegistryVO registryVO = REGISTRY_CONFIG.get(path + "." + key);
        if (Objects.nonNull(registryVO)) {
            return registryVO.getValue();
        }
        return "";
    }

    /**
     * 根据路径和name获取数据字典值
     *
     * @param path 路径
     * @param key  name
     * @param def  默认值
     * @return value
     */
    public static String getRegistryEspaceValue(String path, String key, String def) {
        RegistryVO registryVO = REGISTRY_CONFIG.get(path + "." + key);
        if (Objects.isNull(registryVO)) {
            return def;
        }
        return registryVO.getValue();
    }

    /**
     * lookup根据itemCode 获取lookup值
     *
     * @param itemCode code
     * @return lookup对象
     */
    public static Optional<LookupItemVO> findMonthTypeItemByItemCode(String itemCode) {
        if (LOOKUP_CONFIG.get("FCST_METHOD_TYPE") != null) {
            return LOOKUP_CONFIG.get("FCST_METHOD_TYPE").stream().filter(lookupItemVO -> itemCode.equalsIgnoreCase(lookupItemVO.getItemCode())).findFirst();
        }
        return Optional.empty();
    }

    /**
     * 获取数据字典值
     *
     * @param classifyCode 编码
     * @param language     语言
     * @return list
     */
    public static List<LookupItemVO> findItemListByZhOrEn(String classifyCode, String language) {
        if (StringUtils.isEmpty(classifyCode)) {
            return Collections.EMPTY_LIST;
        }
        if (StringUtils.isEmpty(language)) {
            language = "en_US";
        }
        String finalLanguage = language;
        return LOOKUP_CONFIG.get(classifyCode).stream().filter(lookupItemVO -> finalLanguage.equalsIgnoreCase(lookupItemVO.getLanguage())).collect(Collectors.toList());
    }

    public static List<String> getLTSUrlList() {
        if (LOOKUP_CONFIG.get("LTS_URL_LIST") != null) {
            return LOOKUP_CONFIG.get("LTS_URL_LIST").stream().map(invo->invo.getItemName()).collect(Collectors.toList());
        }
        return Collections.EMPTY_LIST;
    }
}
