/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.dao;

import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import java.util.List;

/**
 * IKrCpfLv1Aggr Dao接口类
 *
 * <AUTHOR>
 * @since 2022-10-18 09:26:09
 */
public interface IKrCpfLv1AggrDao {
    /**
     * getLv1List 获取产线信息
     *
     * @return List<KrCpfLv1AggrResponse>
     */
    List<KrCpfLv1AggrResponse> getLv1List(ForecastsRequest forecastsRequest);

    /**
     * getLv1List 获取SOP信息
     *
     * @return List<KrCpfLv1AggrResponse>
     */
    List<String> getSopInfo(ForecastsRequest forecastsRequest);

    /**
     * findLevelByPage 分页查询
     *
     * @param forecastsRequest forecastsRequest
     * @param pageVO           pageVO
     * @return PagedResult<KrCpfLv1AggrResponse>
     */
    PagedResult<KrCpfLv1AggrResponse> findLevelByPage(ForecastsRequest forecastsRequest, PageVO pageVO);

    /**
     * getVersions 获取版本信息
     *
     * @return List<String>
     */
    List<Long> getVersions() throws CommonApplicationException;

    List<Long> getFutureYearVersions() throws CommonApplicationException;

    /**
     * findSubtotalGroupFcst
     *
     * @param forecastsRequest forecastsRequest
     * @return List<KrCpfLv1AggrResponse>
     */
    List<KrCpfLv1AggrResponse> findSubtotalGroup(ForecastsRequest forecastsRequest);

    /**
     * findDataByTop
     *
     * @param forecastsRequest forecastsRequest
     * @return List<KrCpfLv1AggrResponse>
     */
    List<KrCpfLv1AggrResponse> findDataByTop(ForecastsRequest forecastsRequest);

    /**
     * findRecordOrder
     *
     * @param forecastsRequest forecastsRequest
     * @return List<KrCpfLv1AggrResponse>
     */
    List<String> findRecordOrder(ForecastsRequest forecastsRequest);

    /**
     * findRecordOrder
     *
     * @param forecastsRequest forecastsRequest
     * @return List<KrCpfLv1AggrResponse>
     */
    List<String> findAiCombinedRecordOrder(ForecastsRequest forecastsRequest);

    /**
     * findRecordOrder
     *
     * @param forecastsRequest forecastsRequest
     * @return List<KrCpfLv1AggrResponse>
     */
    List<String> findGroupAnalystsRecordOrder(ForecastsRequest forecastsRequest);

    /**
     * ai 数据明细
     * @param forecastsRequest
     * @return list
     */
    List<KrCpfLv1AggrResponse> findAiCombinedData(ForecastsRequest forecastsRequest);

    /**
     * 汇总ai表
     * @param forecastsRequest 查询条件
     * @return list
     */
    List<KrCpfLv1AggrResponse> findSubtotalAiCombinedData(ForecastsRequest forecastsRequest);

    /**
     * 查询集团配置表信息
     * @param forecastsRequest 查询条件
     * @return
     */
    List<KrCpfLv1AggrResponse> findGroupAnalystsData(ForecastsRequest forecastsRequest);

    /**
     * 查询集团配置表信息
     * @param forecastsRequest 查询条件
     * @return sub 结果
     */
    List<KrCpfLv1AggrResponse> findSubtotalGroupAnalystsData(ForecastsRequest forecastsRequest);
}
