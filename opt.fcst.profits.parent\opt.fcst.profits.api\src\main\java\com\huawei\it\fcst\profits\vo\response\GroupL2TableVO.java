/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import lombok.Getter;
import lombok.Setter;

/**
 * The Entity of GroupL2TableVO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-15 16:00:24
 */
@Getter
@Setter
public class GroupL2TableVO {
    private String l1Name;

    private String l2Name;

    private String key;

    private KrCpfL2Response lastYearActure;

    private KrCpfL2Response curYearSum;

    private KrCpfL2Response lastYearPeriod;

    private KrCpfL2Response aiAuto;

    private KrCpfL2Response ai2Auto;
}
