/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The Entity of CoaProdInfoVO
 * @since 2022-10-17 19:38:39
 */
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class CoaProdInfoVO extends CoaConfigDataVO {
    private static final long serialVersionUID = 1L;

    /**
     * 重量级团队lv2名称
     **/
    private String lv2Name;

    private String versionCode;

    /**
     * 重量级团队lv3名称
     **/
    private String lv3Name;

    private String prodName;

    private String errorFlag;
}

