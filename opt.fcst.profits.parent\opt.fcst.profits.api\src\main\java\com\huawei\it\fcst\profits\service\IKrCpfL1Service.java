/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service;

import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.response.L1PolylineVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

/**
 * The ServiceInterface of KrCpfL1ActVO entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:25:12
 */
public interface IKrCpfL1Service {
    /**
     * 获取L1业务转结率信息
     *
     * @param forecastsRequest forecastsRequest
     * @return GroupL1GraphVO
     * @throws CommonApplicationException
     */
    L1PolylineVO getL1PolylineInfo(ForecastsRequest forecastsRequest) throws CommonApplicationException;

}
