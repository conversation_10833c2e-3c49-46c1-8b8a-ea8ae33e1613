/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.thread;

import com.huawei.it.fcst.profits.common.enums.SaveTypeEnum;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.dao.ILabelOperateLogDao;
import com.huawei.it.fcst.profits.dao.ISpartProfitingRelationDao;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.vo.LabelOperateLogVO;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.LabelInfoRequest;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

public class SpartRelationThread implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(SpartRelationThread.class);

    private List<LabelInfoRequest> requestList;

    private String updateFlag;

    private Long userId;

    private Timestamp curTime;

    private LabelOperateLogVO labelOperateLogVO;

    private final Object lock = new Object();
    private CountDownLatch countDownLatch;
    public SpartRelationThread(CountDownLatch countDownLatch , LabelOperateLogVO labelOperateLogVO, Long userId, String updateFlag, List<LabelInfoRequest> requestList, Timestamp curTime) {
        this.updateFlag = updateFlag;
        this.userId = userId;
        this.requestList = requestList;
        this.curTime = curTime;
        this.labelOperateLogVO = labelOperateLogVO;
        this.countDownLatch = countDownLatch;
    }

    @Override
    public void run() {
        synchronized (lock) {
            try {
                if (CollectionUtils.isEmpty(requestList)) {
                    return;
                }
                // 数据分组
                int num = groupByConditions(updateFlag, requestList);
                upsertOperateNum(labelOperateLogVO, num);
                countDownLatch.countDown();
            } catch (Exception ex) {
                countDownLatch.countDown();
                logger.error("saveData occurs error: {}", ex);
            }
        }
    }

    public void upsertOperateNum(LabelOperateLogVO labelOperateLogVO, int num){
        if(num < 1 || Objects.isNull(labelOperateLogVO) || labelOperateLogVO.getIsSkip()){
            return;
        }
        LabelOperateLogVO logVO = LabelOperateLogVO.builder().objectId(labelOperateLogVO.getObjectId()).upsertNum(num).status("PROCESSING").build();
        ILabelOperateLogDao operateLogDao = Jalor.getContext().getBean(ILabelOperateLogDao.class);
        operateLogDao.updateUpsertNum(logVO);
    }

    /**
     * 保存新增数据信息
     *
     * @param userId     用户id
     * @param insertList 新增数据
     */
    public int saveInsertRecord(Long userId, String updateFlag, List<LabelInfoRequest> insertList,
                                 Timestamp curTime) throws CommonApplicationException {
        int num = 0;
        if (CollectionUtils.isEmpty(insertList)) {
            return num;
        }
        List<SpartProfitingRelationVO> spartList = new ArrayList<>();
        insertList.stream().forEach(req -> {
            SpartProfitingRelationVO spartProfitingRelationVO = new SpartProfitingRelationVO();
            BeanUtils.copyProperties(req, spartProfitingRelationVO);
            spartProfitingRelationVO.setItemCode(req.getItemCode());
            spartProfitingRelationVO.setItemDesc(req.getItemDesc());
            spartProfitingRelationVO.setLv1Name(req.getLv1Name());
            spartProfitingRelationVO.setLv1Code(req.getLv1Code());
            spartProfitingRelationVO.setL1Name(req.getL1Name());
            spartProfitingRelationVO.setL2Name(req.getL2Name());
            spartProfitingRelationVO.setL3Name(req.getL3Name());
            spartProfitingRelationVO.setPeriodId(Long.valueOf(TimeUtils.getCurPeriod()));
            spartProfitingRelationVO.setDataType("Add");
            if (StringUtils.equals("Y", updateFlag)) {
                spartProfitingRelationVO.setDataType(req.getDataType());
            }
            spartProfitingRelationVO.setLastUpdatedBy(userId);
            spartProfitingRelationVO.setCreatedBy(userId);
            spartProfitingRelationVO.setCreationDate(curTime);
            spartProfitingRelationVO.setLastUpdateDate(curTime);
            spartProfitingRelationVO.setUpdateFlag(updateFlag);
            spartProfitingRelationVO.setStatus(SaveTypeEnum.SAVE.getCode());
            spartProfitingRelationVO.setDelFlag("N");
            spartList.add(spartProfitingRelationVO);
        });
        if (CollectionUtils.isNotEmpty(spartList)) {
            num = batchInsert(spartList);
        }
        return num;
    }

    /**
     * 保存编辑的信息
     *
     * @param userId        用户id
     * @param updateList    更新内容信息
     * @param saveStatusMap 保持状态信息集合
     */
    public int saveEditRecord(Long userId, List<LabelInfoRequest> updateList, Map<String,
            List<SpartProfitingRelationVO>> saveStatusMap, Timestamp curTime) throws CommonApplicationException {
        int num = 0;
        if (CollectionUtils.isEmpty(updateList)) {
            return num;
        }
        List<SpartProfitingRelationVO> spartList = new ArrayList<>();
        updateList.stream().forEach(request -> {
            String unionKey = CommUtils.getUnionKey(String.valueOf(request.getPeriodId()), request.getItemCode(), request.getL1Name());
            List<SpartProfitingRelationVO> relationVOList = saveStatusMap.get(unionKey);
            if (CollectionUtils.isEmpty(relationVOList) || relationVOList.size() != 1) {
                return;
            }
            SpartProfitingRelationVO dbVO = relationVOList.get(0);
            dbVO.setL2Name(request.getL2Name());
            dbVO.setL3Name(request.getL3Name());
            dbVO.setL2Coefficient(request.getL2Coefficient());
            dbVO.setL1Coefficient(request.getL1Coefficient());
            dbVO.setL3Coefficient(request.getL3Coefficient());
            dbVO.setLastUpdatedBy(userId);
            dbVO.setLastUpdateDate(curTime);
            dbVO.setStatus(SaveTypeEnum.SAVE.getCode());
            spartList.add(dbVO);
        });
        Object iSpartProfitingRelationDao = Jalor.getContext().getBean("ISpartProfitingRelationDao");
        if (null == iSpartProfitingRelationDao) {
            return num;
        }
        ((ISpartProfitingRelationDao) iSpartProfitingRelationDao).updateOldList(spartList);
        num = ((ISpartProfitingRelationDao) iSpartProfitingRelationDao).createList(spartList);
        return num;
    }

    /**
     * 保持提交的信息
     *
     * @param userId          用户id
     * @param copySubmitList  复制保存信息
     * @param submitStatusMap 状态信息集合
     */
    public int saveSubmitRecord(Long userId, List<LabelInfoRequest> copySubmitList, Map<String, List<SpartProfitingRelationVO>> submitStatusMap) throws CommonApplicationException {
        int num = 0;
        if (CollectionUtils.isEmpty(copySubmitList)) {
            return num;
        }
        List<SpartProfitingRelationVO> resultList = new ArrayList<>();
        Map<String, List<LabelInfoRequest>> copySubmitMap = copySubmitList.stream().collect(Collectors.groupingBy(spart -> CommUtils.getUnionKey(String.valueOf(spart.getPeriodId()), spart.getItemCode(), spart.getL1Name())));
        copySubmitMap.entrySet().stream().forEach(entry -> {
            if (Objects.nonNull(submitStatusMap) && Objects.nonNull(submitStatusMap.get(entry.getKey()))) {
                copyAndBuildSpart(userId, resultList, entry.getValue().get(0), submitStatusMap.get(entry.getKey()), curTime);
            }
        });
        if (CollectionUtils.isNotEmpty(resultList)) {
            num = batchInsert(resultList);
        }
        return num;
    }

    public int batchInsert(List<SpartProfitingRelationVO> resultList) throws CommonApplicationException {
        int num = 0;
        if (CollectionUtils.isEmpty(resultList)) {
            return num;
        }
        try {
            Object iSpartProfitingRelationDao = Jalor.getContext().getBean("ISpartProfitingRelationDao");
            if (null == iSpartProfitingRelationDao) {
                return num;
            }
            num = ((ISpartProfitingRelationDao) iSpartProfitingRelationDao).createList(resultList);
        } catch (Exception ex) {
            logger.error("batchInsertOrUpdate occurs error: {}", ex);
            throw new CommonApplicationException("批量插入更新异常");
        }
        return num;
    }

    /**
     * 复制构建数据
     *
     * @param userId
     * @param resultList
     * @param request
     * @param spartList
     */
    public void copyAndBuildSpart(Long userId, List<SpartProfitingRelationVO> resultList, LabelInfoRequest request,
                                  List<SpartProfitingRelationVO> spartList, Timestamp curTime) {
        if (CollectionUtils.isEmpty(spartList) || spartList.size() > 2) {
            return;
        }
        SpartProfitingRelationVO spart = new SpartProfitingRelationVO();
        SpartProfitingRelationVO relationVO = new SpartProfitingRelationVO();
        if (spartList.size() == 1) {
            spart = spartList.get(0);
        }
        if (spartList.size() == 2) {
            spart = spartList.stream().max(Comparator.comparing(SpartProfitingRelationVO::getLastUpdateDate)).get();
        }
        // 排除存在Save,submit相同的数据
        if (checkIfSameData(request, spart)) {
            return;
        }
        BeanUtils.copyProperties(spart, relationVO);
        relationVO.setL2Name(request.getL2Name());
        relationVO.setL3Name(request.getL3Name());
        // 设置l1-l3值
        setCoefficientValue(relationVO, request, spart);
        relationVO.setCreatedBy(spart.getCreatedBy());
        relationVO.setCreationDate(spart.getCreationDate());
        relationVO.setLastUpdatedBy(userId);
        relationVO.setLastUpdateDate(curTime);
        relationVO.setUpdateFlag("Y");
        relationVO.setStatus(SaveTypeEnum.SAVE.getCode());
        resultList.add(relationVO);
    }


    /**
     * 根据条件查询列表信息
     *
     * @param voList vo信息列表
     * @return int
     */
    public int groupByConditions(String updateFlag, List<LabelInfoRequest> voList) throws CommonApplicationException {
        List<LabelInfoRequest> updateList = new ArrayList<>();
        List<LabelInfoRequest> insertList = new ArrayList<>();
        List<LabelInfoRequest> copySubmitList = new ArrayList<>();
        voList.stream().forEach(vo -> {
            if (null == vo.getPeriodId()) {
                vo.setPeriodId(Long.valueOf(TimeUtils.getCurPeriod()));
            }
            if (StringUtils.equals("无", vo.getL3Name()) || StringUtils.isBlank(vo.getL3Name())) {
                vo.setL3Name(null);
            }
        });
        Object iSpartProfitingRelationDao = Jalor.getContext().getBean("ISpartProfitingRelationDao");
        List<SpartProfitingRelationVO> originalDBList = ((ISpartProfitingRelationDao) iSpartProfitingRelationDao).findDataByList(voList);
        if (CollectionUtils.isEmpty(originalDBList)) {
            insertList.addAll(requestList);
            return saveInsertRecord(userId, updateFlag, insertList, curTime);
        }
        // 暂存数据
        Map<String, List<SpartProfitingRelationVO>> saveStatusMap = originalDBList.stream().filter(spart -> SaveTypeEnum.SAVE.getCode().equals(spart.getStatus()))
                .collect(Collectors.groupingBy(spart -> CommUtils.getUnionKey(String.valueOf(spart.getPeriodId()), spart.getItemCode(), spart.getL1Name())));
        // 提交数据
        Map<String, List<SpartProfitingRelationVO>> submitStatusMap = originalDBList.stream().filter(spart ->
                        SaveTypeEnum.SUBMIT.getCode().equals(spart.getStatus()))
                .collect(Collectors.groupingBy(spart -> CommUtils.getUnionKey(String.valueOf(spart.getPeriodId()), spart.getItemCode(), spart.getL1Name())));
        int num = 0;
        if (submitStatusMap.size() == 0 && saveStatusMap.size() == voList.size()) {
            updateList.addAll(requestList);
            int saveNum = saveEditRecord(userId, updateList, saveStatusMap, curTime);
            num = num + saveNum;
        } else if (saveStatusMap.size() == 0 && submitStatusMap.size() == voList.size()) {
            copySubmitList.addAll(requestList);
            int submitSaveNum = saveSubmitRecord(userId, copySubmitList, submitStatusMap);
            num = num + submitSaveNum;
        } else {
            num = num + specToData(userId, updateFlag, updateList, insertList, copySubmitList, saveStatusMap, submitStatusMap);
        }
        return num;
    }

    public int specToData(Long userId, String updateFlag, List<LabelInfoRequest> updateList, List<LabelInfoRequest> insertList,
                           List<LabelInfoRequest> copySubmitList, Map<String, List<SpartProfitingRelationVO>> saveStatusMap,
                           Map<String, List<SpartProfitingRelationVO>> submitStatusMap)
            throws CommonApplicationException {
        // 请求数据
        Map<String, List<LabelInfoRequest>> reqDataMap = requestList.stream().collect(Collectors.groupingBy(spart ->
                CommUtils.getUnionKey(String.valueOf(spart.getPeriodId()), spart.getItemCode(), spart.getL1Name())));
        // 存在2条提交、保存中间态数据情况、非特殊情况
        reqDataMap.entrySet().stream().forEach(entry -> {
            // db不存在暂存数据，无(提交)数据情况==新增
            if (Objects.isNull(saveStatusMap.get(entry.getKey())) && Objects.isNull(submitStatusMap.get(entry.getKey()))) {
                insertList.add(entry.getValue().get(0));
            }
            // db不存在暂存数据，有(提交)数据情况==复制更新
            if (Objects.isNull(saveStatusMap.get(entry.getKey())) && Objects.nonNull(submitStatusMap.get(entry.getKey()))) {
                copySubmitList.add(entry.getValue().get(0));
            }
            // db存在暂存数据 ==更新
            if (Objects.nonNull(saveStatusMap.get(entry.getKey()))) {
                updateList.add(entry.getValue().get(0));
            }
        });
        int inserNum = saveInsertRecord(userId, updateFlag, insertList, curTime);
        int saveEditNum = saveEditRecord(userId, updateList, saveStatusMap, curTime);
        int saveSubmitNum =saveSubmitRecord(userId, copySubmitList, submitStatusMap);
        return inserNum+saveEditNum+saveSubmitNum;
    }

    /**
     * 校验数据是否存在修改
     *
     * @param labelInfoRequest
     * @param spart
     * @return
     */
    public boolean checkIfSameData(LabelInfoRequest labelInfoRequest, SpartProfitingRelationVO spart) {
        if (Objects.isNull(labelInfoRequest) || Objects.isNull(spart)) {
            return true;
        }
        if (!StringUtils.equals(labelInfoRequest.getL2Name(), spart.getL2Name())) {
            return false;
        }
        if (!StringUtils.equals(labelInfoRequest.getL3Name(), spart.getL3Name())) {
            return false;
        }
        // 需要特殊处理: 校验的时候应该把 0.3333330000 和 0.3333333333 视作一样的
        if (!coefficientCompare(labelInfoRequest.getL1Coefficient(),spart.getL1Coefficient())) {
            return false;
        }
        if (!coefficientCompare(labelInfoRequest.getL2Coefficient(),spart.getL2Coefficient())) {
            return false;
        }
        if (!coefficientCompare(labelInfoRequest.getL3Coefficient(),spart.getL3Coefficient())) {
            return false;
        }
        return true;
    }

    /**
     * 设置l1-l3 赋值逻辑
     *
     * @param relationVO 修改对象
     * @param labelInfoRequest 请求参数
     * @param target 数据库对象
     */
    private void setCoefficientValue(SpartProfitingRelationVO relationVO, LabelInfoRequest labelInfoRequest, SpartProfitingRelationVO target) {
        if (coefficientCompare(labelInfoRequest.getL1Coefficient(), target.getL1Coefficient())) {
            relationVO.setL1Coefficient(target.getL1Coefficient());
        } else {
            relationVO.setL1Coefficient(labelInfoRequest.getL1Coefficient());
        }
        if (coefficientCompare(labelInfoRequest.getL2Coefficient(), target.getL2Coefficient())) {
            relationVO.setL2Coefficient(target.getL2Coefficient());
        } else {
            relationVO.setL2Coefficient(labelInfoRequest.getL2Coefficient());
        }
        if (coefficientCompare(labelInfoRequest.getL3Coefficient(), target.getL3Coefficient())) {
            relationVO.setL3Coefficient(target.getL3Coefficient());
        } else {
            relationVO.setL3Coefficient(labelInfoRequest.getL3Coefficient());
        }
    }

    /**
     * 比较系数，保留六位小数进行比较
     *
     * @param val 请求参数
     * @param target 目标参数
     * @return true|false
     */
    private boolean coefficientCompare(BigDecimal val, BigDecimal target) {
        if (ObjectUtils.allNull(val, target)) {
            return Boolean.TRUE;
        }
        if(Objects.nonNull(val) && val.compareTo(BigDecimal.ZERO)== BigDecimal.ZERO.intValue() && Objects.isNull(target)){
            return Boolean.TRUE;
        }
        if(Objects.nonNull(target) && target.compareTo(BigDecimal.ZERO)== BigDecimal.ZERO.intValue() && Objects.isNull(val)){
            return Boolean.TRUE;
        }
        if (ObjectUtils.allNotNull(val,target)) {
            return val.setScale(6, BigDecimal.ROUND_DOWN).compareTo(target.setScale(6, BigDecimal.ROUND_DOWN)) == BigDecimal.ZERO.intValue();
        } else {
            return Boolean.FALSE;
        }
    }
}
