/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * PredictionEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum PredictionEnum {
    TAB("TAB", "表格"),
    GRAPH("GRAPH", "图"),
    MONTH("MONTH", "月度预测"),
    YEAR("YEAR", "年度预算");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    PredictionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * getByCode
     *
     * @param code code
     * @return StepTypeEnum
     */
    public static PredictionEnum getByCode(String code) {
        for (PredictionEnum value : PredictionEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

