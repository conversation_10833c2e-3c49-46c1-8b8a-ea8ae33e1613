/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.comm;

import com.huawei.it.commons.security.util.Constants;
import com.huawei.it.fcst.profits.common.poi.ExcelUtil;
import com.huawei.it.fcst.profits.common.poi.PoiEnum;
import com.huawei.it.fcst.profits.common.utils.FileProcessUtis;
import com.huawei.it.fcst.profits.common.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.profits.utils.HeaderTitleUtils;
import com.huawei.it.fcst.profits.utils.HeaderUtils;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.StreamUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public abstract class AbstractConfigDataExporter {
    private static final Logger logger = LoggerFactory.getLogger(AbstractConfigDataExporter.class);

    @Autowired
    protected ExcelUtil excelUtil;

    public void exportData(Map<String, Object> paramsMap, List<?> resultList, HttpServletResponse response)
            throws CommonApplicationException {

        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        // 构建导出Excel的表头信息
        HeaderUtils.buildHeader(titleVoList, titles,
                HeaderTitleUtils.creatExportHeaderList(String.valueOf(paramsMap.get("dataType"))));
        excelUtil.expSelectColumnExcel(paramsMap, titles, titleVoList, resultList, response);
        if (Objects.nonNull(paramsMap.get("fileName"))) {
            exportDataStreamAndUpload(paramsMap);
        }
    }

    protected void exportDataStreamAndUpload(Map<String, Object> paramsMap) throws CommonApplicationException {
        File targetFile = null;
        FileOutputStream outputStream = null;
        try {
            String fileName = String.valueOf(paramsMap.get("fileName"));
            logger.info("export data start" + fileName);
            targetFile = File.createTempFile(fileName, ".xlsx");
            outputStream = new FileOutputStream(targetFile);
            PoiEnum.exportExcel(outputStream, paramsMap);
            logger.info("export data end" + fileName);
            paramsMap.put("fileKey",
                    FileProcessUtis.uploadToS3(targetFile, fileName + ".xlsx", String.valueOf(paramsMap.get("userId"))));
            paramsMap.put("fileSize", targetFile.length() / 1024);
        } catch (Exception ex) {
            logger.error("exportAuditData occurs error");
        } finally {
            StreamUtil.closeStreams(outputStream);
            try {
                if (null != targetFile) {
                    if (!targetFile.delete()) {
                        logger.error("删除临时文件失败");
                    }
                }
            } catch (Exception ex) {
                logger.error("删除临时文件失败: {}", ex);
            }
        }
    }

    public void downloadFile(HttpServletResponse response, String targetPath, String titleName)
            throws CommonApplicationException {
        InputStream inputStream = null;
        Workbook wb = null;
        try {
            Resource classPathResource = new ClassPathResource(targetPath);
            inputStream = classPathResource.getInputStream();
            if (Objects.nonNull(inputStream)) {
                wb = WorkbookFactory.create(inputStream);
                response.setContentType("application/vnd.ms-excel;charset=UTF-8");
                response.setHeader("Content-Disposition",
                        "attachment;filename=\"" + URLEncoder.encode(titleName, Constants.UTF_8) + "\"");
                wb.write(response.getOutputStream());
            }
        } catch (Exception ex) {
            logger.error("模板文件不存在");
            throw new CommonApplicationException("模板文件不存在");
        } finally {
            ExcelUtil.closeStreamAndWorkbook(inputStream, wb);
        }
    }

    public abstract void resetDataList(List resultResult, List<Map<String, Object>> infoList);
}
