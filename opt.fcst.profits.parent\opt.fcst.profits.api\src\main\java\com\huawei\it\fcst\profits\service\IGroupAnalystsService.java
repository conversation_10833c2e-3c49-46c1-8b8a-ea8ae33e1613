/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.GroupAnalystsVO;
import com.huawei.it.fcst.profits.vo.LabelOperateLogVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import org.apache.cxf.jaxrs.ext.multipart.Attachment;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

/**
 * IGroupAnalystsService
 *
 */
public interface IGroupAnalystsService {

    List<GroupAnalystsVO> getDropDownBox(GroupAnalystsVO requestVO) throws CommonApplicationException;

    List<GroupAnalystsVO> getGroupAnalystsPageInfo(GroupAnalystsVO request);

    void groupAnalystsTemplateDownload(HttpServletResponse response) throws CommonApplicationException;

    LabelOperateLogVO getOperateRecordLogStatus(String objectId);

    CommonResult fullImportGroupAnalystsInfo(Attachment file) throws CommonApplicationException;

    CommonResult importGroupAnalystsInfo(Attachment file) throws CommonApplicationException;

    CommonResult exportGroupAnalystsInfo(HttpServletResponse response, GroupAnalystsVO request)
        throws CommonApplicationException;

    CommonResult submit() throws ApplicationException;
}
