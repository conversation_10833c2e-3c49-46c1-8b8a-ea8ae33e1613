/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * TableHeaderVo Class
 *
 * <AUTHOR>
 * @since 2022-09-28
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TableHeaderVo {
    /**
     * 列标题
     */
    private String title;

    /**
     * 值对象的字段名
     */
    private String field;

    /**
     * 子标题
     */
    private List<TableHeaderVo> children;
}
