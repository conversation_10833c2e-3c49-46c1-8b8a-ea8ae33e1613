<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.profits.dao.IKrCpfL1Dao">
    <select id="getL1TotalInfo" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfL1Response">
        select
        period_id periodId,
        target_period targetPeriod,
        lv1_code lv1Code,
        lv1_name lv1Name,
        lv2_code lv2Code,
        lv2_name lv2Name,
        l1_name l1Name,
        bg_code bgCode,
        bg_name bgName,
        unit_price_act unitPrice,
        unit_cost_act unitCost,
        mca_adjust_ratio_act mcaAdjustRatio,
        mgp_adjust_ratio_act mgpAdjustRatio,
        carryover_ratio_act carryoverRatio,
        '' unitPriceUpper,
        '' unitPriceLower,
        '' unitCostUpper,
        '' unitCostLower,
        0 unitPriceAcc,
        0 unitCostAcc,
        'ACT' as dataType
        FROM
        FIN_DM_OPT_FOP.DM_FOP_L1_ACT_T
        where currency='CNY' and del_flag = 'N'
        <include refid="commonCondition"/>
        <if test='actPeriods != null and actPeriods.size() > 0'>
            AND target_period IN
            <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test='includeFlag != null and includeFlag == "1"'>
            union
            select
            period_id periodId,
            target_period targetPeriod,
            lv1_code lv1Code,
            lv1_name lv1Name,
            lv2_code lv2Code,
            lv2_name lv2Name,
            l1_name l1Name,
            bg_code bgCode,
            bg_name bgName,
            max(unit_price_timeseries_fcst) unitPrice,
            max(unit_cost_timeseries_fcst) unitCost,
            max(mca_adjust_ratio_fcst) mcaAdjustRatio,
            max(mgp_adjust_ratio_fcst) mgpAdjustRatio,
            max(carryover_ratio_fcst) carryoverRatio,
            max(unit_price_timeseries_fcst_upper) unitPriceUpper,
            max(unit_price_timeseries_fcst_lower) unitPriceLower,
            max(unit_cost_timeseries_fcst_upper) unitCostUpper,
            max(unit_cost_timeseries_fcst_lower) unitCostLower,
            max(unit_price_fcst_acc) unitPriceAcc,
            max(unit_cost_fcst_acc) unitCostAcc,
            data_type as dataType
            FROM
            FIN_DM_OPT_FOP.DM_FOP_KR_CPF_L1_FCST_V
            where currency='CNY' and del_flag = 'N'
            <if test='specPeriods != null and specPeriods.size() > 0'>
                and fcst_type in
                <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='specPeriods == null'>
                and fcst_type is null
            </if>
            <include refid="commonCondition"/>
            <if test='phaseDate != null and phaseDate != ""'>
                AND (phase_date = #{phaseDate,jdbcType=VARCHAR}
                <if test='specAutoTarget != null and specAutoTarget != ""'>
                    or phase_date = (select max(phase_date) from FIN_DM_OPT_FOP.kr_cpf_l1_fcst_t where del_flag='N' and
                    period_id = #{periodId,jdbcType=VARCHAR}
                    and target_period = #{specAutoTarget,jdbcType=VARCHAR}
                    and phase_date not like '%-%')
                </if>
                or phase_date is null)
            </if>
            <if test='dataType != null and dataType !=""'>
                and data_type = #{dataType ,jdbcType=VARCHAR}
            </if>
            <if test='l1Names != null'>
                AND l1_name IN
                <foreach collection='l1Names' item="item" separator="," open="(" close=")" index="">
                    <![CDATA[
                        #{item,jdbcType=VARCHAR}
                    ]]>
                </foreach>
            </if>
            <if test='fcstPeriods != null'>
                AND target_period IN
                <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="overseaDesc != null and overseaDesc != ''">
                AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
            </if>
            group by period_id ,target_period ,lv1_code ,lv1_name , lv2_code , lv2_name ,l1_name ,bg_code ,bg_name ,data_type
        </if>
    </select>


    <sql id="commonCondition">
        <if test='lv2Code != null'>
            AND lv2_code = #{lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='l1Name != null'>
            AND l1_name =
            <![CDATA[
                 #{l1Name,jdbcType=VARCHAR}
            ]]>
        </if>
        <if test='periodId != null'>
            AND period_id = #{periodId,jdbcType=VARCHAR}
        </if>
        <if test='lv1Codes != null'>
            AND lv1_code IN
            <foreach collection='lv1Codes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='lv2Codes != null'>
            AND lv2_code IN
            <foreach collection='lv2Codes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCode != null'>
            AND bg_code IN
            <foreach collection='bgCode' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>
    <select id="exportL1Factor" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfL1Response">
        select
        periodId,
        targetPeriod,
        lv1Code,
        lv1Name,
        lv2Code,
        lv2Name,
        l1Name,
        bgCode,
        bgName,
        unitPrice,
        unitCost,
        mcaAdjustRatio,
        mgpAdjustRatio,
        carryoverRatio,
        equipRevAfter,
        mgpRateAfter ,
        phaseDate,
        fcstType ,
        overseaDesc,
        groupKey
        from (
        SELECT
        period_id periodId,
        target_period targetPeriod,
        lv1_code lv1Code,
        lv1_name lv1Name,
        lv2_code lv2Code,
        lv2_name lv2Name,
        l1_name l1Name,
        bg_code bgCode,
        bg_name bgName,
        <![CDATA[(case when l1Name in ('5G&LTE TDD','5G&LTE FDD','GUC','SRAN') then unit_price_act else '' end) ]]>
        unitPrice,
        <![CDATA[(case when l1Name in ('5G&LTE TDD','5G&LTE FDD','GUC','SRAN') then unit_cost_act else '' end) ]]>
        unitCost,
        mca_adjust_ratio_act mcaAdjustRatio,
        mgp_adjust_ratio_act mgpAdjustRatio,
        carryover_ratio_act carryoverRatio,
        equip_rev_cons_after_amt as equipRevAfter,
        mgp_ratio as mgpRateAfter,
        '' phaseDate,
        '' fcstType,
        (case when oversea_desc  = '国内' then '中国区' else oversea_desc end) overseaDesc  ,
        'ACT' groupKey
        FROM fin_dm_opt_fop.dm_fop_l1_act_t
        WHERE currency = 'CNY'
        AND del_flag = 'N'
        <include refid="expCondition"/>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test="predictionType =='YEAR' ">
            <if test='actPeriods != null and actPeriods != ""'>
                AND target_period IN
                <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        <if test="predictionType !='YEAR' ">
            <if test='actPeriods != null and actPeriods != ""'>
                AND target_period NOT IN
                <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        UNION ALL
        select
        period_id periodId,
        target_period targetPeriod,
        lv1_code lv1Code,
        lv1_name lv1Name,
        lv2_code lv2Code,
        lv2_name lv2Name,
        l1_name l1Name,
        bg_code bgCode,
        bg_name bgName,
        <![CDATA[ (case when l1Name in ('5G&LTE TDD','5G&LTE FDD','GUC','SRAN') then unit_price_timeseries_fcst else '' end) ]]>
        unitPrice,
        <![CDATA[(case when l1Name in ('5G&LTE TDD','5G&LTE FDD','GUC','SRAN') then unit_cost_timeseries_fcst else '' end) ]]>
        unitCost,
        mca_adjust_ratio_fcst mcaAdjustRatio,
        mgp_adjust_ratio_fcst mgpAdjustRatio,
        carryover_ratio_fcst carryoverRatio,
        equip_rev_after_fcst equipRevAfter,
        mgp_rate_after_fcst mgpRateAfter,
        phase_date phaseDate,
        fcst_type,
        (case when oversea_desc  = '国内' then '中国区' else oversea_desc end) overseaDesc  ,
        'fcst' groupKey
        from fin_dm_opt_fop.kr_cpf_l1_fcst_t where currency = 'CNY'
        and del_flag = 'N'
        <include refid="expCondition"/>
        AND (phase_date = #{phaseDate,jdbcType=VARCHAR}
        or phase_date is null)
        <![CDATA[
            and IFNULL(fcst_type,'null') <> '年度平均法'
        ]]>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test="predictionType =='YEAR' ">
            <if test='specPeriods != null and specPeriods.size() > 0'>
                and fcst_type in
                <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='fcstPeriods != null and fcstPeriods != "" and fcstPeriods.size()>0'>
                AND target_period IN
                <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        ) order by l1name desc ,targetPeriod desc
    </select>

    <select id="exportL1FactorCount" resultType="int">
        select count (*) from (
        SELECT period_id periodId
        FROM fin_dm_opt_fop.dm_fop_l1_act_t
        WHERE currency = 'CNY'
        AND del_flag = 'N'
        <include refid="expCondition"/>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test="predictionType =='YEAR' ">
            <if test='actPeriods != null and actPeriods != ""'>
                AND target_period IN
                <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        <if test="predictionType !='YEAR' ">
            <if test='actPeriods != null and actPeriods != ""'>
                AND target_period NOT IN
                <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        UNION ALL
        select
        period_id periodId
        from fin_dm_opt_fop.kr_cpf_l1_fcst_t where currency = 'CNY'
        and del_flag = 'N'
        <include refid="expCondition"/>
        AND (phase_date = #{phaseDate,jdbcType=VARCHAR}
        or phase_date is null)
        <![CDATA[
               and IFNULL(fcst_type,'null') <> '年度平均法'
          ]]>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test="predictionType =='YEAR' ">
            <if test='specPeriods != null and specPeriods.size() > 0'>
                and fcst_type in
                <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='fcstPeriods != null and fcstPeriods != "" and fcstPeriods.size()>0'>
                AND target_period IN
                <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        )
    </select>


    <sql id="expCondition">
        AND period_id = #{periodId,jdbcType=VARCHAR}
        <if test='lv1s != null and lv1s.size() > 0 '>
            AND lv1_code IN
            <foreach collection='lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCodes != null  and bgCodes.size() > 0'>
            AND bg_code IN
            <foreach collection='bgCodes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>
</mapper>
