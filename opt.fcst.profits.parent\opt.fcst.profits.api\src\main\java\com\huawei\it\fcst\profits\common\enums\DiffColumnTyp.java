/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * BgEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum DiffColumnTyp {
    GROUP_C1("lastYearActure", "PROD0002"),
    GROUP_C2("curYearSum", "PROD0002"),
    GROUP_C3("lastYearPeriod", "PROD0002"),
    GROUP_C4("aiAuto", "PROD0002"),
    GROUP_C5("ai2Auto", "PROD0002"),
    <PERSON><PERSON>UP_C6("groupAnalyst", "PROD0002"),
    GROUP_C7("combinedExpertGroup", "PROD0002"),
    GROUP_C8("combinedExpert", "PROD0002"),
    CNBG_C1("cnbgLastYearActure", "PDCG901159"),
    CNBG_C2("cnbgCurYearSum", "PDCG901159"),
    CNBG_C3("cnbgLastYearPeriod", "PDCG901159"),
    CNBG_C4("cnbgAiAuto", "PDCG901159"),
    CNBG_C5("cnbgAi2Auto", "PDCG901159"),
    CNBG_C6("cnbgCombinedExpert", "PDCG901159"),
    EBG_C1("ebgLastYearActure", "PDCG901160"),
    EBG_C2("ebgCurYearSum", "PDCG901160"),
    EBG_C3("ebgLastYearPeriod", "PDCG901160"),
    EBG_C4("ebgAiAuto", "PDCG901160"),
    EBG_C5("ebgAi2Auto", "PDCG901160"),
    EBG_C6("ebgCombinedExpert", "PDCG901160");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    DiffColumnTyp(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * getByCode
     *
     * @param code code
     * @return StepTypeEnum
     */
    public static DiffColumnTyp getByCode(String code) {
        for (DiffColumnTyp value : DiffColumnTyp.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

