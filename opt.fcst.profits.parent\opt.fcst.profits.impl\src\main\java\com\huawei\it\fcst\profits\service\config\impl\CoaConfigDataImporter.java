/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.config.impl;

import cn.hutool.core.bean.BeanUtil;
import com.huawei.it.fcst.profits.comm.AbstractConfigDataImporter;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.LabelConfigEnum;
import com.huawei.it.fcst.profits.common.enums.OptTypeEnum;
import com.huawei.it.fcst.profits.common.enums.RecStsEnum;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.ExcelVO;
import com.huawei.it.fcst.profits.common.vo.UploadInfoVO;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.utils.HeaderTitleUtils;
import com.huawei.it.fcst.profits.utils.HeaderUtils;
import com.huawei.it.fcst.profits.vo.CoaConfigDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * coa导入功能
 *
 * @since 20230208
 * <AUTHOR>
 */
@Component
public class CoaConfigDataImporter extends AbstractConfigDataImporter {

    private static final Logger logger = LoggerFactory.getLogger(CoaConfigDataImporter.class);
    @Override
    public List<ExcelVO> getHeadList() {
        return HeaderUtils.buildExcelVO(HeaderTitleUtils.creatImportHeaderList(LabelConfigEnum.COA.getCode()));
    }

    @Override
    public void logErrorRecord(UploadInfoVO uploadInfoVO, List verifyDataList) throws CommonApplicationException {
        String errorFileKey = uploadInfoVO.getFileKey();
        Map params = new ConcurrentHashMap<>();
        params.put("fileName", CommonConstant.LABEL_CONFIG_EXCEPTION1 + uploadInfoVO.getFileName());
        params.put("dataType", "COA");
        params.put("userId", String.valueOf(uploadInfoVO.getUserId()));
        if (CollectionUtils.isNotEmpty(verifyDataList)) {
            buildExportStreamAndUpload(params, String.valueOf(uploadInfoVO.getUserId()), verifyDataList,
                HeaderTitleUtils.creatErrorHeaderList("COA"));
            if (Objects.nonNull(params.get("fileKey"))) {
                errorFileKey = String.valueOf(params.get("fileKey"));
            }
        }
        creatRecord(String.valueOf(params.get("fileName")), uploadInfoVO.getFileSize(), "Save", CommonConstant.ERROR_INPUT_DATA,
            uploadInfoVO.getRowNumber(), TimeUtils.getCurPeriod(), "", errorFileKey, OptTypeEnum.IMPORT.getCode(),
            RecStsEnum.FAIL.getCode(), CommonConstant.LABEL_CONFIG_MODULE_NAME1);
    }

    @Override
    public void setNormalInfoAndCheckRepeated(List<Object> dataList, Map<String, Object> dataMap,
        AtomicInteger counterRef, Map<String, Object> preparedInfo) {
        CoaConfigDataVO dataVO = BeanUtil.fillBeanWithMap(dataMap, new CoaConfigDataVO(), false);
        CommUtils.setTimeCreatorInfoAndCheckRepeated(dataList, counterRef, dataVO,
            CommonConstant.LABEL_CONFIG_COA_REPEATED_MESSAGE);
    }

    @Override
    public void setVoDataInfoAndCheck(List<Object> dataList, String title, String value, AtomicInteger atomicInteger,
        Map<String, Object> keys, Map<String, Object> infoMap) {
        StringBuilder builder = new StringBuilder();
        List<String> lv1Name = (List<String>)keys.get("lv1Name");
        List<String> l1Name = (List<String>)keys.get("l1NameList");
        Map<String,String> hierarchySortInfoLv1 = (Map<String,String>)keys.get("hierarchySortInfoLv1");
        keys.put("dataType","COA");
        switch (title) {
            case "产业":
                infoMap.put("lv1Name", value);
                if (ObjectUtils.isEmpty(infoMap.get("lv1Name"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING2);
                } else if (!lv1Name.contains(infoMap.get("lv1Name"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING1);
                } else {
                    // 配置通套的Lv1Code
                    infoMap.put("lv1Code", hierarchySortInfoLv1.get(value));
                }break;
            case "L1名称":
                infoMap.put("l1Name",value);
                if (ObjectUtils.isEmpty(infoMap.get("l1Name"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING3);
                } else if (!l1Name.contains(value)) { // 增加对l1Name的校验，必须在全景图维护的l1Name中
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING13);
                }
                break;
            case "L2名称":
                infoMap.put("l2Name",value);
                if (ObjectUtils.isEmpty(infoMap.get("l2Name"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING4);
                }break;
            case "产品编码":
                infoMap.put("coaCode",value);
                if (ObjectUtils.isEmpty(infoMap.get("coaCode"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING5);
                }break;
            default:
                return;
        }
        if (builder.length() > 0) {
            atomicInteger.addAndGet(1);
            if (infoMap.containsKey("errorMsg")) {
                infoMap.put("errorMsg", infoMap.get("errorMsg") + builder.toString());
            } else {
                infoMap.put("errorMsg", builder.toString());
            }
        }
    }

    @Override
    public Map<String, ExcelVO> getLegalTitleMap() {
        List<ExcelVO> coaImportHeaderList = this.getHeadList();
        return coaImportHeaderList.stream().collect(Collectors.toMap(k -> k.getHeadName(), v -> v));
    }
}
