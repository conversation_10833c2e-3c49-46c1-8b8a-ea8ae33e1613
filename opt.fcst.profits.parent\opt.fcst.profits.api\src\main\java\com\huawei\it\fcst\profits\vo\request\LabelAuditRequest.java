/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.request;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * The Entity of LabelAuditRequest
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-12 11:07:53
 */
@Getter
@Setter
public class LabelAuditRequest extends PageRequest {
    /**
     * 会计期
     **/
    private Long periodId;

    /**
     * ITEM编
     **/
    private String itemCode;

    /**
     * LV1编码
     **/
    private String lv1Name;

    /**
     * L3系数
     **/
    private BigDecimal l3Coefficient;

    /**
     * L2系数
     **/
    private BigDecimal l2Coefficient;

    /**
     * L1系数
     **/
    private BigDecimal l1Coefficient;

    /**
     * 数据类型（Manual 历史、AI 新增）
     **/
    private String dataType;

    private String[] itemCodes;

    private String[] lv1Names;

    private String[] l1Name;

    private String[] l2Name;

    private String[] l3Name;
}
