<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.profits.dao.IGroupAnalystsDao">

    <insert id="batchInsertDataVOs" parameterType="java.util.List">
        INSERT INTO fin_dm_opt_fop.dm_fop_group_analysts_result_t
        (version_code
        ,bg_code
        ,bg_name
        ,oversea_desc
        ,lv1_code
        ,lv1_name
        ,lv2_code
        ,lv2_name
        ,currency
        ,equip_rev_amt
        ,mgp_ratio
        ,remark
        ,created_by
        ,creation_date
        ,last_updated_by
        ,last_update_date
        ,del_flag
        ,target_code
        ,target_desc
        ,status
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.versionCode,jdbcType=VARCHAR},
            'PROD0002',
            'ICT',
            '全球',
            #{item.lv1Code,jdbcType=VARCHAR},
            #{item.lv1Name,jdbcType=VARCHAR},
            #{item.lv2Code,jdbcType=VARCHAR},
            #{item.lv2Name,jdbcType=VARCHAR},
            'CNY',
            #{item.equipRevAmt,jdbcType=VARCHAR},
            #{item.mgpRatio,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=BIGINT},
            NOW(),
            #{item.lastUpdatedBy,jdbcType=BIGINT},
            NOW(),
            'N',
            #{item.targetCode,jdbcType=VARCHAR},
            #{item.targetDesc,jdbcType=VARCHAR},
            'IMPORT')
        </foreach>
    </insert>

    <update id="updateDataStatus">
        UPDATE fin_dm_opt_fop.dm_fop_group_analysts_result_t SET
            STATUS = 'SUBMIT',
            LAST_UPDATE_DATE = now(),
            LAST_UPDATED_BY = #{userId,jdbcType=BIGINT}
        WHERE DEL_FLAG = 'N'
        AND STATUS = 'IMPORT'
    </update>

    <delete id="deleteDataVOs" parameterType="java.util.Map">
        <foreach collection="paramMap" item="value" index="key" open="" close="" separator=";">
            DELETE FROM fin_dm_opt_fop.dm_fop_group_analysts_result_t
            WHERE del_flag = 'N'
            AND status = 'IMPORT'
            AND version_code = #{key,jdbcType=VARCHAR}
            AND lv1_name = #{value,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteSubmitData">
        DELETE FROM fin_dm_opt_fop.dm_fop_group_analysts_result_t t1
        WHERE t1.del_flag = 'N'
        AND t1.status = 'SUBMIT'
        AND EXISTS (SELECT 1 FROM (SELECT DISTINCT version_code, lv1_name
            FROM fin_dm_opt_fop.dm_fop_group_analysts_result_t
            WHERE del_flag = 'N'
            AND status = 'IMPORT') t2
          WHERE t2.version_code = t1.version_code
          AND t2.lv1_name = t1.lv1_name
        )
    </delete>

    <delete id="deleteFullSubmitData">
        DELETE FROM fin_dm_opt_fop.dm_fop_group_analysts_result_t
        WHERE del_flag = 'N'
        AND status = 'SUBMIT'
        AND version_code in (SELECT DISTINCT version_code from fin_dm_opt_fop.dm_fop_group_analysts_result_t
            where del_flag = 'N' AND status = 'IMPORT' AND remark = 'Full_Import' )
    </delete>

    <select id="getDropDownBox" resultType="com.huawei.it.fcst.profits.vo.GroupAnalystsVO">
        SELECT distinct
        <if test='versionCode == null or versionCode ==""'>
            version_code versionCode
        </if>
        <if test='versionCode != null and versionCode !=""'>
            lv1_name lv1Name, lv1_code lv1Code
        </if>
        from fin_dm_opt_fop.dm_fop_group_analysts_result_t
        where del_flag = 'N'
        <if test='versionCode != null and versionCode !=""'>
            AND version_code = #{versionCode,jdbcType=VARCHAR}
        </if>
        <if test='lv1Codes != null and lv1Codes.size() > 0 '>
            AND lv1_code IN
            <foreach collection='lv1Codes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='lv1Names != null and lv1Names.size() > 0 '>
            AND lv1_name IN
            <foreach collection='lv1Names' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='versionCode == null or versionCode ==""'>
            ORDER BY version_code desc
        </if>
        <if test='versionCode != null and versionCode !=""'>
            ORDER BY lv1_code
        </if>
    </select>

    <select id="getGroupAnalystsPageInfo" resultType="com.huawei.it.fcst.profits.vo.GroupAnalystsVO">
        SELECT
        version_code versionCode,
        currency,lv1_code lv1Code, lv1_name lv1Name,lv2_code lv2Code, lv2_name  lv2Name
        ,max(CASE WHEN target_code ='Y' THEN equip_rev_amt ELSE NULL END ) as yearEquipRevAmt
        ,max(CASE WHEN target_code ='Y' THEN mgp_ratio ELSE NULL END )as yearMgpRatio
        ,max(CASE WHEN target_code ='Q' THEN equip_rev_amt ELSE NULL END )as quartEquipRevAmt
        ,max(CASE WHEN target_code ='Q' THEN mgp_ratio ELSE NULL END) as  quartMgpRatio
        ,max(CASE WHEN target_code ='H' THEN equip_rev_amt ELSE NULL END)as halfYearEquipRevAmt
        ,max(CASE WHEN target_code ='H' THEN mgp_ratio ELSE NULL END) as halfYearmgpRatio
        ,status
        ,max(last_updated_by) lastUpdatedBy
        ,max(last_update_date) lastUpdateDate
        FROM  fin_dm_opt_fop.dm_fop_group_analysts_result_t
        WHERE del_flag = 'N'
        <if test='versionCode != null'>
            AND version_code = #{versionCode,jdbcType=VARCHAR}
        </if>
        <if test='lv1Codes != null and lv1Codes.size() > 0 '>
            AND lv1_code IN
            <foreach collection='lv1Codes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='lv1Names != null and lv1Names.size() > 0 '>
            AND lv1_name IN
            <foreach collection='lv1Names' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by version_code,currency,lv1_code, lv1_name,lv2_code, lv2_name,status
        order by version_code,status,lv1_name,yearEquipRevAmt desc
    </select>

    <select id="getLv1GroupAnalystsInfo" resultType="com.huawei.it.fcst.profits.vo.GroupAnalystsVO">
    SELECT
    version_code versionCode,
    currency,lv1_code lv1Code, lv1_name lv1Name
    ,max(CASE WHEN target_code ='Y' THEN equip_rev_amt ELSE NULL END ) as yearEquipRevAmt
    ,max(CASE WHEN target_code ='Y' THEN mgp_ratio ELSE NULL END )as yearMgpRatio
    ,max(CASE WHEN target_code ='Q' THEN equip_rev_amt ELSE NULL END )as quartEquipRevAmt
    ,max(CASE WHEN target_code ='Q' THEN mgp_ratio ELSE NULL END) as  quartMgpRatio
    ,max(CASE WHEN target_code ='H' THEN equip_rev_amt ELSE NULL END)as halfYearEquipRevAmt
    ,max(CASE WHEN target_code ='H' THEN mgp_ratio ELSE NULL END) as halfYearmgpRatio
    ,status
    ,max(last_updated_by) lastUpdatedBy
    ,max(last_update_date) lastUpdateDate
    FROM fin_dm_opt_fop.dm_fop_group_analysts_result_t
    WHERE del_flag = 'N'
    AND lv2_code IS NULL
    <if test='versionCode != null'>
        AND version_code = #{versionCode,jdbcType=VARCHAR}
    </if>
    <if test='lv1Codes != null and lv1Codes.size() > 0 '>
        AND lv1_code IN
        <foreach collection='lv1Codes' item="item" separator="," open="(" close=")" index="">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </if>
    group by version_code,currency,lv1_code, lv1_name,status
    order by yearEquipRevAmt desc
</select>

    <select id="getProdInfoList" resultType="com.huawei.it.fcst.profits.vo.GroupAnalystsVO">
        SELECT
            DISTINCT
            LV1_NAME lv1Name,LV1_CODE lv1Code,LV2_NAME lv2Name,LV2_CODE lv2Code
        from
            fin_dm_opt_fop.apd_fop_ict_fcst_holistic_view_t
        WHERE DEL_FLAG ='N' AND  LV1_CODE is not null AND  LV2_CODE is not null
    </select>

    <select id="getGroupAnalystsPageInfoList" resultType="com.huawei.it.fcst.profits.vo.GroupAnalystsVO">
        SELECT
        version_code versionCode,
        currency,
        lv1_name lv1Name,
        lv2_name lv2Name,
        equip_rev_amt equipRevAmt,
        mgp_ratio mgpRatio,
        TARGET_DESC targetDesc,
        status,
        last_updated_by  lastUpdatedBy,
        last_update_date lastUpdateDate
        FROM  fin_dm_opt_fop.dm_fop_group_analysts_result_t
        WHERE del_flag = 'N'
        <if test='status != null and status != ""'>
            AND status = #{status,jdbcType=VARCHAR}
        </if>
        <if test='versionCode != null and versionCode != ""'>
            AND version_code = #{versionCode,jdbcType=VARCHAR}
        </if>
        <if test='createdBy != null'>
            AND created_by = #{createdBy,jdbcType=BIGINT}
        </if>
        <if test='lv1Codes != null and lv1Codes.size() > 0 '>
            AND lv1_code IN
            <foreach collection='lv1Codes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='lv1Names != null and lv1Names.size() > 0 '>
            AND lv1_name IN
            <foreach collection='lv1Names' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by versionCode,lv1_name,lv2_name,status
    </select>

    <select id="checkImportDataNum" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM fin_dm_opt_fop.dm_fop_group_analysts_result_t
        WHERE del_flag = 'N'
        AND status = 'IMPORT'
        AND last_updated_by = #{userId,jdbcType=BIGINT}
    </select>

    <select id="checkImportFlag" resultType="java.lang.String">
        SELECT DISTINCT version_code
        FROM fin_dm_opt_fop.dm_fop_group_analysts_result_t
        WHERE del_flag = 'N'
        AND status = 'IMPORT'
        AND remark = 'Full_Import'
        LIMIT 1
    </select>

</mapper>
