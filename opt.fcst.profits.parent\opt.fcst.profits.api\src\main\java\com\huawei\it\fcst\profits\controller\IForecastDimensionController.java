/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2026. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.response.QueryVersionResponse;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Api("盈利预测量纲相关")
@Path("/forecast/dimension")
@Consumes({"application/json"})
@Produces({"application/json"})
@Validated
public interface IForecastDimensionController {

    @POST
    @Path("/getBudgetVersionInfo")
    CommonResult<QueryVersionResponse> getBudgetVersionInfo(ForecastsRequest forecastsRequest);


}
