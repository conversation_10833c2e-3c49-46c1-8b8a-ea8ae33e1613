/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service;

import com.huawei.it.fcst.profits.vo.LabelModifyVO;
import com.huawei.it.fcst.profits.vo.request.LabelModifyRequest;
import com.huawei.it.jalor5.core.base.PagedResult;

/**
 * The ServiceInterface of FcstLabelModifyVO entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-17 19:38:39
 */
public interface ILabelModifyService {
    PagedResult<LabelModifyVO> findByPage(LabelModifyRequest vo);

}
