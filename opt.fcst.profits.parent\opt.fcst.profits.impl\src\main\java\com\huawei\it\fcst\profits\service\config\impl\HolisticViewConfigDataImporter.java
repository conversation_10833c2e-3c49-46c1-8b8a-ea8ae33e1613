/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.config.impl;

import cn.hutool.core.bean.BeanUtil;

import com.huawei.it.fcst.profits.comm.AbstractConfigDataImporter;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.LabelConfigEnum;
import com.huawei.it.fcst.profits.common.enums.OptTypeEnum;
import com.huawei.it.fcst.profits.common.enums.RecStsEnum;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.ExcelVO;
import com.huawei.it.fcst.profits.common.vo.UploadInfoVO;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.utils.HeaderTitleUtils;
import com.huawei.it.fcst.profits.utils.HeaderUtils;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
public class HolisticViewConfigDataImporter extends AbstractConfigDataImporter {

    @Override
    public List<ExcelVO> getHeadList() {
        return HeaderUtils.buildExcelVO(
            HeaderTitleUtils.creatImportHeaderList(LabelConfigEnum.HOLISTIC_VIEW.getCode()));
    }

    @Override
    public void logErrorRecord(UploadInfoVO uploadInfoVO, List verifyDataList) throws CommonApplicationException {
        // ict全景图异常导出，增加场景字段还原
        String errorFileKey = uploadInfoVO.getFileKey();
        Map params = new ConcurrentHashMap<>();
        params.put("fileName", CommonConstant.LABEL_CONFIG_EXCEPTION1 + uploadInfoVO.getFileName());
        params.put("userId", String.valueOf(uploadInfoVO.getUserId()));
        params.put("dataType", "HOLISTIC_VIEW");
        if (CollectionUtils.isNotEmpty(verifyDataList)) {
            verifyDataList.forEach(dataVo -> {
                if (!ObjectUtils.isEmpty(dataVo)) {
                    if ("SCENO1".equals(((HolisticViewConfigDataVO) dataVo).getArticulationFlag())) {
                        ((HolisticViewConfigDataVO) dataVo).setArticulationFlag(
                            CommonConstant.LABEL_CONFIG_ARTICULATION_FLAG1);
                    } else if ("SCENO2".equals(((HolisticViewConfigDataVO) dataVo).getArticulationFlag())) {
                        ((HolisticViewConfigDataVO) dataVo).setArticulationFlag(
                            CommonConstant.LABEL_CONFIG_ARTICULATION_FLAG2);
                    } else if ("SCENO3".equals(((HolisticViewConfigDataVO) dataVo).getArticulationFlag())) {
                        ((HolisticViewConfigDataVO) dataVo).setArticulationFlag(
                            CommonConstant.LABEL_CONFIG_ARTICULATION_FLAG3);
                    }
                    if ("Y".equals(((HolisticViewConfigDataVO) dataVo).getAnalysisFlag())) {
                        ((HolisticViewConfigDataVO) dataVo).setAnalysisFlag("是");
                    } else if ("N".equals(((HolisticViewConfigDataVO) dataVo).getAnalysisFlag())) {
                        ((HolisticViewConfigDataVO) dataVo).setAnalysisFlag("否");
                    }
                }
            });
            buildExportStreamAndUpload(params, String.valueOf(uploadInfoVO.getUserId()), verifyDataList,
                HeaderTitleUtils.creatErrorHeaderList("HOLISTIC_VIEW"));
            if (Objects.nonNull(params.get("fileKey"))) {
                errorFileKey = String.valueOf(params.get("fileKey"));
            }
        }
        creatRecord(String.valueOf(params.get("fileName")), uploadInfoVO.getFileSize(), "Save",
            CommonConstant.ERROR_INPUT_DATA, uploadInfoVO.getRowNumber(), TimeUtils.getCurPeriod(), "", errorFileKey,
            OptTypeEnum.IMPORT.getCode(), RecStsEnum.FAIL.getCode(), CommonConstant.LABEL_CONFIG_MODULE_NAME2);
    }

    @Override
    public void getReadyInfoBeforeTraversal(Map<String, Object> keys, String roleId) {
        super.getReadyInfoBeforeTraversal(keys, roleId);
        keys.put("hierarchySortLv2", iLabelConfigDao.getHierarchySortInfoLv2());
        keys.put("hierarchySortLv3", iLabelConfigDao.getHierarchySortInfoLv3());
    }

    @Override
    public void setNormalInfoAndCheckRepeated(List<Object> dataList, Map<String, Object> dataMap,
        AtomicInteger counterRef, Map<String, Object> preparedInfo) {
        HolisticViewConfigDataVO dataVO = BeanUtil.fillBeanWithMap(dataMap, new HolisticViewConfigDataVO(), false);
        List<HolisticViewConfigDataVO> lv2List = (List<HolisticViewConfigDataVO>) preparedInfo.get("hierarchySortLv2");
        List<HolisticViewConfigDataVO> lv3List = (List<HolisticViewConfigDataVO>) preparedInfo.get("hierarchySortLv3");
        // 检查导入层级关系是否正确,检查
        if (StringUtils.isNotBlank(dataVO.getLv3Name()) && StringUtils.isNotBlank(dataVO.getLv2Name())) {
            checkHierarchySortLv3(lv3List, dataVO, counterRef);
        } else if (StringUtils.isNotBlank(dataVO.getLv2Name())) {
            checkHierarchySortLv2(lv2List, dataVO, counterRef);
        }
        // 场景名称转译
        String sceneName = "";
        switch (dataVO.getArticulationFlag()) {
            case "场景一":
                sceneName = "SCENO1";
                break;
            case "场景二":
                sceneName = "SCENO2";
                break;
            case "场景三":
                sceneName = "SCENO3";
                break;
            default:
                sceneName = dataVO.getArticulationFlag();
                break;
        }
        dataVO.setArticulationFlag(sceneName);
        // 设置工业类型
        if (ObjectUtils.isEmpty(dataVO.getL1Name())) {
            dataVO.setIndustryType("OTHR");
        } else {
            dataVO.setIndustryType("TGT");
        }
        if (ObjectUtils.isEmpty(dataVO.getL1Name()) && CommonConstant.ARTICULATION_CHECK_FLAG_LIST.contains(
            sceneName)) {
            counterRef.addAndGet(1);
            if (StringUtils.isNotBlank(dataVO.getErrorMsg())) {
                dataVO.setErrorMsg(dataVO.getErrorMsg() + CommonConstant.LABEL_CONFIG_ICT_L1NAME_NULL_MESSAGE);
            } else {
                dataVO.setErrorMsg(CommonConstant.LABEL_CONFIG_ICT_L1NAME_NULL_MESSAGE);
            }
        }
        // 分析场景导入检查
        if (StringUtils.equals("是", dataVO.getAnalysisFlag())) {
            dataVO.setAnalysisFlag("Y");
        } else if (StringUtils.equals("否", dataVO.getAnalysisFlag())) {
            dataVO.setAnalysisFlag("N");
        }
        // 设置日期时间相关信息以及验重
        CommUtils.setTimeCreatorInfoAndCheckRepeated(dataList, counterRef, dataVO,
            CommonConstant.LABEL_CONFIG_ICT_REPEATED_MESSAGE);
    }

    private void checkHierarchySortLv3(List<HolisticViewConfigDataVO> list, HolisticViewConfigDataVO dataVO,
        AtomicInteger counterRef) {
        List<HolisticViewConfigDataVO> formatVos = list.stream()
            .filter(
                item -> item.getLv1Name().equals(dataVO.getLv1Name()) && (item.getLv2Name().equals(dataVO.getLv2Name()))
                    && item.getLv3Name().equals(dataVO.getLv3Name()))
            .collect(Collectors.toList());
        // 匹配到层级关系
        if (formatVos.size() != 0) {
            HolisticViewConfigDataVO formatVo = formatVos.get(0);
            dataVO.setLv2Code(formatVo.getLv2Code());
            dataVO.setLv3Code(formatVo.getLv3Code());
        } else {
            if (StringUtils.isNotBlank(dataVO.getErrorMsg())) {
                dataVO.setErrorMsg(dataVO.getErrorMsg() + CommonConstant.LABEL_CONFIG_WARNING10);
            } else {
                dataVO.setErrorMsg(CommonConstant.LABEL_CONFIG_WARNING10);
            }
            counterRef.addAndGet(1);
        }
    }

    private void checkHierarchySortLv2(List<HolisticViewConfigDataVO> list, HolisticViewConfigDataVO dataVO,
        AtomicInteger counterRef) {
        List<HolisticViewConfigDataVO> formatVos = list.stream()
            .filter(item -> item.getLv1Name().equals(dataVO.getLv1Name()) && (item.getLv2Name()
                .equals(dataVO.getLv2Name())))
            .collect(Collectors.toList());
        // 匹配到层级关系
        if (formatVos.size() != 0) {
            HolisticViewConfigDataVO formatVo = formatVos.get(0);
            dataVO.setLv2Code(formatVo.getLv2Code());
        } else {
            if (StringUtils.isNotBlank(dataVO.getErrorMsg())) {
                dataVO.setErrorMsg(dataVO.getErrorMsg() + CommonConstant.LABEL_CONFIG_WARNING11);
            } else {
                dataVO.setErrorMsg(CommonConstant.LABEL_CONFIG_WARNING11);
            }
            counterRef.addAndGet(1);
        }
    }

    @Override
    public void setVoDataInfoAndCheck(List<Object> dataList, String title, String value, AtomicInteger atomicInteger,
        Map<String, Object> keys, Map<String, Object> objectMap) {
        keys.put("dataType", "HOLISTIC_VIEW");
        StringBuilder builder = new StringBuilder();
        setFieldValue(title, value, atomicInteger, objectMap, builder, keys);

    }

    private void setFieldValue(String title, String value, AtomicInteger atomicInteger, Map<String, Object> objectMap,
        StringBuilder builder, Map<String, Object> keys) {
        List lv1Name = (List) keys.get("lv1Name");
        Map<String, String> hierarchySortInfoLv1 = (Map<String, String>) keys.get("hierarchySortInfoLv1");
        switch (title) {
            case "重量级团队LV1中文名":
                objectMap.put("lv1Name", value);
                if (ObjectUtils.isEmpty(objectMap.get("lv1Name"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING8);
                } else if (!lv1Name.contains(objectMap.get("lv1Name"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING1);
                } else {
                    objectMap.put("lv1Code", hierarchySortInfoLv1.get(value));
                }
                break;
            case "重量级团队LV2中文名":
                objectMap.put("lv2Name", value);
                break;
            case "重量级团队LV3中文名":
                objectMap.put("lv3Name", value);
                break;
            case "L1名称":
                objectMap.put("l1Name", value);
                break;
            case "预测场景":
                objectMap.put("articulationFlag", value);
                if (ObjectUtils.isEmpty(objectMap.get("articulationFlag"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING9);
                } else {
                    if (!CommonConstant.ARTICULATION_FLAG_LIST.contains(value)) {
                        builder.append(CommonConstant.LABEL_CONFIG_WARNING12);
                    }
                }
                break;
            case "分析场景":
                objectMap.put("analysisFlag", value);
                if (ObjectUtils.isEmpty(objectMap.get("analysisFlag"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING14);
                } else if(!CommonConstant.ANALYSE_FLAG_LIST.contains(objectMap.get("analysisFlag"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_ICT_ANALYSIS_FLAG_ERROR_MESSAGE);
                }
                break;
            default:
                return;
        }
        checkErrorValue(atomicInteger, objectMap, builder);
    }

    @Override
    public Map<String, ExcelVO> getLegalTitleMap() {
        List<ExcelVO> holisticImportHeaderList = this.getHeadList();
        return holisticImportHeaderList.stream().collect(Collectors.toMap(k -> k.getHeadName(), v -> v));
    }
}
