/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.service.usercenter.impl;

import java.util.Arrays;

import com.huawei.it.fcst.profits.service.usercenter.impl.LabelModifyService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.huawei.it.fcst.profits.dao.ILabelModifyDao;
import com.huawei.it.fcst.profits.vo.LabelModifyVO;
import com.huawei.it.fcst.profits.vo.request.LabelModifyRequest;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年02月28日
 */
class LabelModifyServiceTest {
    @Mock
    private ILabelModifyDao iLabelModifyDao;

    @InjectMocks
    private LabelModifyService labelModifyService;
    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    void findByPage() {
        LabelModifyRequest request = new LabelModifyRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        request.setTimeInterval("202203");
        request.setPageModule("ALL");
        PagedResult<LabelModifyVO> pageRequest = new PagedResult();
        LabelModifyVO labelModifyVO = new LabelModifyVO();
        labelModifyVO.setPageModule("find");
        pageRequest.setResult(Arrays.asList(labelModifyVO));
        when(iLabelModifyDao.findByPage(any(), any())).thenReturn(pageRequest);
        PagedResult<LabelModifyVO> result = labelModifyService.findByPage(request);
        Assertions.assertEquals("find", result.getResult().get(0).getPageModule());
    }

    @Test
    void findByPageTestOne() {
        LabelModifyRequest request = new LabelModifyRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        request.setTimeInterval("ALL");
        request.setPageModule("ALL");
        PagedResult<LabelModifyVO> pageRequest = new PagedResult();
        LabelModifyVO labelModifyVO = new LabelModifyVO();
        labelModifyVO.setPageModule("find");
        pageRequest.setResult(Arrays.asList(labelModifyVO));
        when(iLabelModifyDao.findByPage(any(), any())).thenReturn(pageRequest);
        PagedResult<LabelModifyVO> result = labelModifyService.findByPage(request);
        Assertions.assertEquals("find", result.getResult().get(0).getPageModule());
    }
    @Test
    void findByPageIsTimeIntervalNull() {
        LabelModifyRequest request = new LabelModifyRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        request.setTimeInterval(null);
        request.setPageModule("ALL");
        PagedResult<LabelModifyVO> pageRequest = new PagedResult();
        LabelModifyVO labelModifyVO = new LabelModifyVO();
        labelModifyVO.setPageModule("find");
        pageRequest.setResult(Arrays.asList(labelModifyVO));
        when(iLabelModifyDao.findByPage(any(), any())).thenReturn(pageRequest);
        PagedResult<LabelModifyVO> result = labelModifyService.findByPage(request);
        Assertions.assertEquals("find", result.getResult().get(0).getPageModule());
    }
    @Test
    void findByPageIsModuleNull() {
        LabelModifyRequest request = new LabelModifyRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        request.setTimeInterval("ALL");
        request.setPageModule(null);
        PagedResult<LabelModifyVO> pageRequest = new PagedResult();
        LabelModifyVO labelModifyVO = new LabelModifyVO();
        labelModifyVO.setPageModule("find");
        pageRequest.setResult(Arrays.asList(labelModifyVO));
        when(iLabelModifyDao.findByPage(any(), any())).thenReturn(pageRequest);
        PagedResult<LabelModifyVO> result = labelModifyService.findByPage(request);
        Assertions.assertEquals("find", result.getResult().get(0).getPageModule());
    }
    @Test
    void findByPageIsModuleTest() {
        LabelModifyRequest request = new LabelModifyRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        request.setTimeInterval("ALL");
        request.setPageModule("all-test");
        PagedResult<LabelModifyVO> pageRequest = new PagedResult();
        LabelModifyVO labelModifyVO = new LabelModifyVO();
        labelModifyVO.setPageModule("find");
        pageRequest.setResult(Arrays.asList(labelModifyVO));
        when(iLabelModifyDao.findByPage(any(), any())).thenReturn(pageRequest);
        PagedResult<LabelModifyVO> result = labelModifyService.findByPage(request);
        Assertions.assertEquals("find", result.getResult().get(0).getPageModule());
    }
}