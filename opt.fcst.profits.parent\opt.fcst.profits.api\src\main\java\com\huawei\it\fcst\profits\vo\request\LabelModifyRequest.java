/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.request;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * The Entity of LabelModifyRequest
 *
 * <AUTHOR>
 * @ by Jalor Studio
 * @since 2022-10-17 19:38:39
 */
@Getter
@Setter
public class LabelModifyRequest extends PageRequest {
    private static final long serialVersionUID = 1L;

    /**
     * 模块
     **/
    private String pageModule;

    /**
     * 创建时间:年月
     */
    private String timeInterval;

    /**
     * 查询时的月初时间条件
     */
    private String beginTime;

    /**
     * 查询时的月末时间条件
     */
    private String endTime;

    private String status;

    private String userId;

    private List<LabelModifyRequest> submitList;
}
