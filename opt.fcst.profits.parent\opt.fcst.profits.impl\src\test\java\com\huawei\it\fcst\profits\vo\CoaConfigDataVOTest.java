package com.huawei.it.fcst.profits.vo;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class CoaConfigDataVOTest {
    @InjectMocks
    CoaConfigDataVO dataVO;
    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
    }
    @Test
    public void getLv1Code() {
        // run the test
        dataVO.setLv1Code("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getLv1Code());
    }

    @Test
    public void getL1Name() {
        // run the test
        dataVO.setL1Name("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getL1Name());
    }

    @Test
    public void getL2Name() {
        // run the test
        dataVO.setL2Name("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getL2Name());
    }

    @Test
    public void getCoaCode() {
        // run the test
        dataVO.setCoaCode("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getCoaCode());
    }
    @Test
    public void testEquals() {
        // run the test
        dataVO.setCoaCode("Test");
        dataVO.setLv1Code("Test");
        dataVO.setLv1Name("Test");
        dataVO.setCoaCode("Test");
        dataVO.setL1Name("Test");
        dataVO.setL2Name("Test");
        CoaConfigDataVO testVo =new CoaConfigDataVO();
        testVo.setCoaCode("Test");
        testVo.setLv1Code("Test");
        testVo.setLv1Name("Test");
        testVo.setCoaCode("Test");
        testVo.setCoaCode("Test");
        testVo.setL1Name("Test");
        testVo.setL2Name("Test");
        // verify the results
        Assert.assertEquals(true, dataVO.equals(testVo));
    }
    @Test
    public void testEqualsA() {
        // verify the results
        Assert.assertEquals(true, dataVO.equals(dataVO));
    }
    @Test
    public void testEqualsB() {
        // verify the results
        Assert.assertEquals(false, dataVO.equals(null));
    }
    @Test
    public void testHashCode() {
        // verify the results
        Assert.assertNotNull(dataVO.hashCode());
    }
}