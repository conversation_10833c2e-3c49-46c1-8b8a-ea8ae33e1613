/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.dao;

import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.response.KrCpfL2Response;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;

import java.util.List;

/**
 * The DAO to access KrCpfL2ActVO entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:26:09
 */
public interface IKrCpfL2Dao {
    /**
     * 获取l2Name
     *
     * @param forecastsRequest forecastsRequest
     * @return List<String>
     */
    List<String> getL2Names(ForecastsRequest forecastsRequest);

    /**
     * 获取l2Name
     *
     * @param forecastsRequest forecastsRequest
     * @return List<String>
     */
    List<String> getHisL2Names(ForecastsRequest forecastsRequest);


    /**
     * 获取 top5L2Name
     *
     * @param forecastsRequest forecastsRequest
     * @return List<String>
     */
    List<String> getTop5L2Name(ForecastsRequest forecastsRequest);


    /**
     * 获取 top5L2Name
     *
     * @param forecastsRequest forecastsRequest
     * @return List<String>
     */
    List<String> getHisTop5L2Name(ForecastsRequest forecastsRequest);

    /**
     * getDataByPage 分页查询
     *
     * @param forecastsRequest forecastsRequest
     * @param pageVO pageVO
     * @return PagedResult<KrCpfL2Response>
     */
    PagedResult<KrCpfL2Response> getDataByPage(ForecastsRequest forecastsRequest, PageVO pageVO);

    /**
     * 获取l1_act分组信息
     *
     * @param forecastsRequest forecastsRequest
     * @return List<String>
     */
    List<KrCpfL2Response> getL2GroupInfo(ForecastsRequest forecastsRequest);

    /**
     * 获取l2_act信息
     *
     * @param forecastsRequest forecastsRequest
     * @return List<String>
     */
    List<KrCpfL2Response> getL2TotalInfo(ForecastsRequest forecastsRequest);

    /**
     * getDataByDimension 根据维度信息查询数据
     *
     * @param forecastsRequest forecastsRequest
     * @return List<KrCpfL2Response>
     */
    List<KrCpfL2Response> getDataByDimension(ForecastsRequest forecastsRequest);

    /**
     * 导出数据
     *
     * @param forecastsRequest 请求参数
     * @return list
     */
    List<KrCpfL2Response>  exportL2Factor (ForecastsRequest forecastsRequest);

    /**
     * 导出数据
     *
     * @param forecastsRequest 请求参数
     * @return int 条数
     */
    int exportL2FactorCount (ForecastsRequest forecastsRequest);
}
