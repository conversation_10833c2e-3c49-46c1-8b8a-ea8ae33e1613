/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.config.impl;

import cn.hutool.core.bean.BeanUtil;

import com.huawei.it.fcst.profits.comm.AbstractConfigDataExporter;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class HolisticViewConfigDataExporter extends AbstractConfigDataExporter {

    @Override
    public void resetDataList(List resultResult, List<Map<String, Object>> infoList) {
        resultResult.clear();
        infoList.stream().forEach(item -> {
            if (item.containsKey("status")) {
                String status = String.valueOf(item.get("status"));
                item.put("status", status.equals("Import")
                    ? CommonConstant.LABEL_CONFIG_STATUS1
                    : CommonConstant.LABEL_CONFIG_STATUS2);
            }
            if (item.containsKey("articulationFlag")) {
                String sceneName = "";
                String status = String.valueOf(item.get("articulationFlag"));
                switch (status) {
                    case "SCENO1":
                        sceneName = CommonConstant.LABEL_CONFIG_ARTICULATION_FLAG1;
                        break;
                    case "SCENO2":
                        sceneName = CommonConstant.LABEL_CONFIG_ARTICULATION_FLAG2;
                        break;
                    default:
                        sceneName = CommonConstant.LABEL_CONFIG_ARTICULATION_FLAG3;
                        break;
                }
                item.put("articulationFlag", sceneName);
            }
            if (item.containsKey("analysisFlag")) {
                String analysisFlag = "";
                if (StringUtils.equals("Y", String.valueOf(item.get("analysisFlag")))) {
                    analysisFlag = "是";
                } else {
                    analysisFlag = "否";
                }
                item.put("analysisFlag", analysisFlag);
            } resultResult.add(BeanUtil.fillBeanWithMap(item, new HolisticViewConfigDataVO(), false));
        });
    }
}
