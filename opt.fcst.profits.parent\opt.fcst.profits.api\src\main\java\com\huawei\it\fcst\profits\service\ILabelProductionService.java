/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.CoaProdInfoVO;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.fcst.profits.vo.HolisticViewProdInfoVO;
import com.huawei.it.fcst.profits.vo.ObjectConfigDataVO;
import com.huawei.it.fcst.profits.vo.PlanComProdInfoVo;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.request.LabelConfigQueryRequest;
import com.huawei.it.fcst.profits.vo.request.SpartInfoRequest;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityL1InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityL2InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityL3InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityLv1InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularitySpartInfoVo;
import com.huawei.it.fcst.profits.vo.response.SpartInfoVo;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.kmssdk.exception.KmsSdkException;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

/**
 * ILabelProductionService
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface ILabelProductionService {

    PagedResult<HolisticViewProdInfoVO> queryIctProductInfoByPage(LabelConfigQueryRequest request)
        throws CommonApplicationException;

    void exportIctProductInfo(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest)
        throws CommonApplicationException;

    PagedResult<CoaProdInfoVO> queryCoaProductInfoByPage(LabelConfigQueryRequest request)
        throws CommonApplicationException;

    void exportCoaProductInfo(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest)
        throws CommonApplicationException;

    PagedResult<PlanComProdInfoVo> queryPlanComProductInfoByPage(LabelConfigQueryRequest request)
        throws CommonApplicationException;

    void exportPlanComProductInfo(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest)
        throws CommonApplicationException;

    List<HolisticViewProdInfoVO> getIctMonitorCondition(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<HolisticViewProdInfoVO> getIctMonitorLastUpdated(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<HolisticViewProdInfoVO> getIctMonitorVersion(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<HolisticViewConfigDataVO> getIctConfigCondition(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<PlanComProdInfoVo> getPlanComMonitorCondition(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<PlanComProdInfoVo> getPlanComMonitorLastUpdated(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<PlanComProdInfoVo> getPlanComMonitorVersion(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<CoaProdInfoVO> getCoaMonitorCondition(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<CoaProdInfoVO> getCoaMonitorLastUpdated(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<CoaProdInfoVO> getCoaMonitorProductName(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<CoaProdInfoVO> getCoaMonitorVersion(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<SpartProfitingRelationVO> getObjectAuditCondition(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<ObjectConfigDataVO> getObjectConfigCondition(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<PlanComProdInfoVo> getPlanComConfigCondition(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<CoaProdInfoVO> getCoaConfigCondition(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<PlanComProdInfoVo> getPlanComSopInfo(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<ProfitGranularityL3InfoVo> getProfitExaminingLv1QueryCondition(ForecastsRequest requestVO)
        throws CommonApplicationException;

    List<ProfitGranularityL3InfoVo> getProfitExaminingL1QueryCondition(ForecastsRequest requestVO)
        throws CommonApplicationException;
    List<ProfitGranularityL3InfoVo> getProfitExaminingL2QueryCondition(ForecastsRequest requestVO)
        throws CommonApplicationException;
    Map<String, List<ProfitGranularityLv1InfoVo>> getProfitExaminingLv1MonthInfo(ForecastsRequest requestVO)
        throws CommonApplicationException;
    Map<String, List<ProfitGranularityLv1InfoVo>> getProfitExaminingLv1YtdInfo(ForecastsRequest requestVO)
        throws CommonApplicationException;
    Map<String, List<ProfitGranularityL1InfoVo>> getProfitExaminingL1MonthInfo(ForecastsRequest requestVO) throws CommonApplicationException;

    Map<String, List<ProfitGranularityL1InfoVo>> getProfitExaminingL1YtdInfo(ForecastsRequest requestVO) throws CommonApplicationException;

    Map<String, List<ProfitGranularityL2InfoVo>> getProfitExaminingL2MonthInfo(ForecastsRequest requestVO,Boolean flag) throws CommonApplicationException;

    Map<String, List<ProfitGranularityL2InfoVo>> getProfitExaminingL2MonthShipInfo(ForecastsRequest requestVO) throws CommonApplicationException;
    Map<String, List<ProfitGranularityL2InfoVo>> getProfitExaminingL2YtdInfo(ForecastsRequest requestVO,Boolean flag) throws CommonApplicationException;

    Map<String, List<ProfitGranularityL2InfoVo>> getProfitExaminingL2YtdShipInfo(ForecastsRequest requestVO) throws CommonApplicationException;
    Map<String, List<ProfitGranularitySpartInfoVo>> getProfitExaminingSpartMonthCostInfo(ForecastsRequest requestVO)
            throws CommonApplicationException, KmsSdkException;
    Map<String, List<ProfitGranularitySpartInfoVo>> getProfitExaminingSpartMonthRevInfo(ForecastsRequest requestVO)
        throws CommonApplicationException;
    Map<String, List<ProfitGranularitySpartInfoVo>> getProfitExaminingSpartYtdCostInfo(ForecastsRequest requestVO)
            throws CommonApplicationException, KmsSdkException;
    Map<String, List<ProfitGranularitySpartInfoVo>> getProfitExaminingSpartYtdRevInfo(ForecastsRequest requestVO)
        throws CommonApplicationException;
    boolean checkUserPermissionAllProd(int roleId);
    Map<String, List<SpartInfoVo>> getProfitExaminingSpartYtdEntries(ForecastsRequest requestVO) throws KmsSdkException;
    boolean checkSpartPermission(int roleId);

    PagedResult getPersonalSpartInfoPage(SpartInfoRequest request, PageVO pageVO);

    CommonResult downloadProfitExaminingSpartInfo(HttpServletResponse response, ForecastsRequest request)
        throws CommonApplicationException;

    Map<String, List<ProfitGranularityL3InfoVo>> getProfitExaminingL3MonthInfo(ForecastsRequest requestVO);

    Map<String, List<ProfitGranularityL3InfoVo>> getProfitExaminingL3YtdInfo(ForecastsRequest requestVO);

    boolean checkUserLv1Permissions(ForecastsRequest requestVO);
}
