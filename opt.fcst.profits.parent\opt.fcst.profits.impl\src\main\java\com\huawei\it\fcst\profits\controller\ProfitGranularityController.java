/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.utils.AesGcmUtil;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.common.vo.ResultCode;
import com.huawei.it.fcst.profits.service.IAccessService;
import com.huawei.it.fcst.profits.service.IFcstProfitsCommonService;
import com.huawei.it.fcst.profits.service.ILabelProductionService;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.request.SpartInfoRequest;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.kmssdk.exception.KmsSdkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 盈利颗粒度相关接口
 */
@JalorResource(code = "profitGranularityController", desc = "profitGranularityController")
@Named("profitGranularityController")
public class ProfitGranularityController implements IProfitGranularityController {
    private static final Logger logger = LoggerFactory.getLogger(ProfitGranularityController.class);

    @Autowired
    private IFcstProfitsCommonService iFcstProfitsCommonService;

    @Autowired
    private ILabelProductionService iLabelProductionService;

    @Autowired
    private ILookupItemQueryService lookupItemQueryService;
    @Autowired
    private IAccessService accessService;

    @JalorOperation(code = "getProfitExaminingLv1QueryCondition", desc = "盈利颗粒度审视列表条件全查-Lv1")
    @Override
    public CommonResult getProfitExaminingLv1QueryCondition(ForecastsRequest requestVO) {
        try {
            Map<String, Object> resultMap = new HashMap();
            boolean refreshFlag = true;
            CommonResult<Object> status = CommUtils.getImportStatusResult(iFcstProfitsCommonService, CommonConstant.REFRESH_RESTRICTION);
            if (status != null) {
                refreshFlag = false;
                resultMap.put("tipMessage", status.getMessage());
            }
            // 此次更新的条数
            resultMap.put("refreshFlag", refreshFlag);
            resultMap.put("spartFlag", accessService.isAccess());
            // 此次更新的条数 分产业、以及条数
            resultMap.put("dataList", iLabelProductionService.getProfitExaminingLv1QueryCondition(requestVO));
            return CommonResult.success(resultMap);
        } catch (ApplicationException ex) {
            logger.error("盈利颗粒度审视列表条件全查-Lv1：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视列表条件全查-Lv1");
        }
    }

    @JalorOperation(code = "getProfitExaminingL1QueryCondition", desc = "盈利颗粒度审视列表条件全查-L1")
    @Override
    public CommonResult getProfitExaminingL1QueryCondition(ForecastsRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getProfitExaminingL1QueryCondition(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("盈利颗粒度审视列表条件全查-L1：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视列表条件全查-L1");
        }
    }

    @JalorOperation(code = "getProfitExaminingL2QueryCondition", desc = "盈利颗粒度审视列表条件全查-L2")
    @Override
    public CommonResult getProfitExaminingL2QueryCondition(ForecastsRequest requestVO) {
        try {
            return CommonResult.success(iLabelProductionService.getProfitExaminingL2QueryCondition(requestVO));
        } catch (CommonApplicationException ex) {
            logger.error("盈利颗粒度审视列表条件全查-L2：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视列表条件全查-L2");
        }
    }
    @Audit(module="profitGranularityController-getProfitExaminingLv1MonthInfo",operation="getProfitExaminingLv1MonthInfo",message="盈利颗粒度审视Lv1指标数据按月度查询")
    @JalorOperation(code = "getProfitExaminingLv1MonthInfo", desc = "盈利颗粒度审视Lv1指标数据按月度查询")
    @Override
    public CommonResult getProfitExaminingLv1MonthInfo(ForecastsRequest requestVO) {
        try {
            Map<String, Object> resultMap = new HashMap();
            resultMap.put("lookUpSetting",lookupItemQueryService.findItemListByZhOrEn("SPART_INDICATORS_LV1","Zh"));
            resultMap.put("dataList",iLabelProductionService.getProfitExaminingLv1MonthInfo(requestVO));
            return CommonResult.success(resultMap);
        } catch (ApplicationException e) {
            logger.error("盈利颗粒度审视Lv1指标数据按月度查询：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视Lv1指标数据按月度查询");
        }
    }

    @Audit(module="profitGranularityController-getProfitExaminingLv1YtdInfo",operation="getProfitExaminingLv1YtdInfo",message="盈利颗粒度审视Lv1指标数据按年度查询")
    @JalorOperation(code = "getProfitExaminingLv1YtdInfo", desc = "盈利颗粒度审视Lv1指标数据按年度查询")
    @Override
    public CommonResult getProfitExaminingLv1YtdInfo(ForecastsRequest requestVO) {
        try {
            Map<String, Object> resultMap = new HashMap();
            resultMap.put("lookUpSetting",lookupItemQueryService.findItemListByZhOrEn("SPART_INDICATORS_LV1","Zh"));
            resultMap.put("dataList",iLabelProductionService.getProfitExaminingLv1YtdInfo(requestVO));
            return CommonResult.success(resultMap);
        } catch (ApplicationException e) {
            logger.error("盈利颗粒度审视Lv1指标数据按年度查询：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视Lv1指标数据按年度查询");
        }
    }

    @Audit(module="profitGranularityController-getProfitExaminingL1MonthInfo",operation="getProfitExaminingL1MonthInfo",message="盈利颗粒度审视L1指标数据按月度查询")
    @JalorOperation(code = "getProfitExaminingL1MonthInfo", desc = "盈利颗粒度审视L1指标数据按月度查询")
    @Override
    public CommonResult getProfitExaminingL1MonthInfo(ForecastsRequest requestVO) {
        try {
            Map<String, Object> resultMap = new HashMap();
            resultMap.put("lookUpSetting",lookupItemQueryService.findItemListByZhOrEn("SPART_INDICATORS_L1","Zh"));
            resultMap.put("dataList",iLabelProductionService.getProfitExaminingL1MonthInfo(requestVO));
            return CommonResult.success(resultMap);
        } catch (ApplicationException ex) {
            logger.error("盈利颗粒度审视L1指标数据按月度查询：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视L1指标数据按月度查询");
        }
    }

    @Audit(module="profitGranularityController-getProfitExaminingL1YtdInfo",operation="getProfitExaminingL1YtdInfo",message="盈利颗粒度审视L1指标数据按年度查询")
    @JalorOperation(code = "getProfitExaminingL1YtdInfo", desc = "盈利颗粒度审视L1指标数据按年度查询")
    @Override
    public CommonResult getProfitExaminingL1YtdInfo(ForecastsRequest requestVO) {
        try {
            Map<String, Object> resultMap = new HashMap();
            resultMap.put("lookUpSetting",lookupItemQueryService.findItemListByZhOrEn("SPART_INDICATORS_L1","Zh"));
            resultMap.put("dataList",iLabelProductionService.getProfitExaminingL1YtdInfo(requestVO));
            return CommonResult.success(resultMap);
        } catch (ApplicationException ex) {
            logger.error("盈利颗粒度审视L1指标数据按年度查询：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视L1指标数据按年度查询");
        }
    }

    @Audit(module="profitGranularityController-getProfitExaminingL2MonthInfo",operation="getProfitExaminingL2MonthInfo",message="盈利颗粒度审视L2指标数据按月度查询")
    @JalorOperation(code = "getProfitExaminingL2MonthInfo", desc = "盈利颗粒度审视L2指标数据按月度查询")
    @Override
    public CommonResult getProfitExaminingL2MonthInfo(ForecastsRequest requestVO) {
        try {
            Map<String, Object> resultMap = new HashMap();
            // 检查数据范围权限
            if(iLabelProductionService.checkUserLv1Permissions(requestVO)){
                resultMap.put("lookUpSetting", Collections.EMPTY_MAP);
                resultMap.put("dataList", Collections.EMPTY_MAP);
                resultMap.put("shipDataList", Collections.EMPTY_MAP);
                return CommonResult.success(resultMap);
            }
            resultMap.put("lookUpSetting",lookupItemQueryService.findItemListByZhOrEn("SPART_INDICATORS_L2","Zh"));
            resultMap.put("dataList",iLabelProductionService.getProfitExaminingL2MonthInfo(requestVO,Boolean.FALSE));
            resultMap.put("shipDataList",iLabelProductionService.getProfitExaminingL2MonthShipInfo(requestVO));
            return CommonResult.success(resultMap);
        } catch (ApplicationException ex) {
            logger.error("盈利颗粒度审视L2指标数据按月度查询：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视L2指标数据按月度查询");
        }
    }

    @Audit(module="profitGranularityController-getProfitExaminingL2YtdInfo",operation="getProfitExaminingL2YtdInfo",message="盈利颗粒度审视L2指标数据按年度查询")
    @JalorOperation(code = "getProfitExaminingL2YtdInfo", desc = "盈利颗粒度审视L2指标数据按年度查询")
    @Override
    public CommonResult getProfitExaminingL2YtdInfo(ForecastsRequest requestVO) {
        try {
            Map<String, Object> resultMap = new HashMap();
            // 检查数据范围权限
            if(iLabelProductionService.checkUserLv1Permissions(requestVO)){
                resultMap.put("lookUpSetting", Collections.EMPTY_MAP);
                resultMap.put("dataList", Collections.EMPTY_MAP);
                resultMap.put("shipDataList", Collections.EMPTY_MAP);
                return CommonResult.success(resultMap);
            }
            resultMap.put("lookUpSetting",lookupItemQueryService.findItemListByZhOrEn("SPART_INDICATORS_L2","Zh"));
            resultMap.put("dataList",iLabelProductionService.getProfitExaminingL2YtdInfo(requestVO,Boolean.FALSE));
            resultMap.put("shipDataList",iLabelProductionService.getProfitExaminingL2YtdShipInfo(requestVO));
            return CommonResult.success(resultMap);
        } catch (ApplicationException ex) {
            logger.error("盈利颗粒度审视L2指标数据按年度查询：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视L2指标数据按年度查询");
        }
    }

    @Audit(module="profitGranularityController-getProfitExaminingSpartMonthInfo",operation="getProfitExaminingSpartMonthInfo",message="盈利颗粒度审视Spart按月度数据查询")
    @JalorOperation(code = "getProfitExaminingSpartInfo", desc = "盈利颗粒度审视Spart按月度数据查询")
    @Override
    public CommonResult getProfitExaminingSpartMonthInfo(ForecastsRequest requestVO) {
        try {
            checkForecastsRequestVO(requestVO);
            Map<String, Object> resultMap = new HashMap();
            if (hasLv1Permission(requestVO, resultMap)) {
                return CommonResult.success(resultMap);
            }
            // 使用安全随机数
            requestVO.setRandom(AesGcmUtil.getSecureRandom());
            // 权限控制（全产业权限+网段权限控制）
            if (iLabelProductionService.checkSpartPermission(requestVO.getRoleId()) && accessService.isAccess()) {
                // L2下的Spart条目
                resultMap.put("spartInfoAndNum", iLabelProductionService.getProfitExaminingSpartYtdEntries(requestVO));
                // 数据集合
                resultMap.put("costList", iLabelProductionService.getProfitExaminingSpartMonthCostInfo(requestVO));
                resultMap.put("revList", iLabelProductionService.getProfitExaminingSpartMonthRevInfo(requestVO));
                resultMap.put("l2CostList",iLabelProductionService.getProfitExaminingL2MonthInfo(requestVO,Boolean.TRUE));
            }
            return CommonResult.success(resultMap);
        } catch (ApplicationException | KmsSdkException ex) {
            logger.error("盈利颗粒度审视Spart按月度数据查询：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视Spart按月度数据查询");
        }
    }

    private boolean hasLv1Permission(ForecastsRequest requestVO, Map<String, Object> resultMap) {
        // 检查数据范围权限
        if (iLabelProductionService.checkUserLv1Permissions(requestVO)) {
            resultMap.put("spartInfoAndNum", Collections.EMPTY_MAP);
            resultMap.put("costList", Collections.EMPTY_MAP);
            resultMap.put("revList", Collections.EMPTY_MAP);
            resultMap.put("l2CostList", Collections.EMPTY_MAP);
            return true;
        }
        return false;
    }

    private void checkForecastsRequestVO(ForecastsRequest requestVO) throws CommonApplicationException {
        if (!CollectionUtil.isNullOrEmpty(requestVO.getCostItems()) && requestVO.getCostItems().size() > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
        if (!CollectionUtil.isNullOrEmpty(requestVO.getRevItems()) && requestVO.getRevItems().size() > 2000) {
            throw new CommonApplicationException("操作的数据过多");
        }
    }


    @Audit(module="profitGranularityController-getProfitExaminingSpartYtdInfo",operation="getProfitExaminingSpartYtdInfo",message="盈利颗粒度审视Spart按年度数据查询")
    @JalorOperation(code = "getProfitExaminingSpartYtdInfo", desc = "盈利颗粒度审视Spart按年度数据查询")
    @Override
    public CommonResult getProfitExaminingSpartYtdInfo(ForecastsRequest requestVO) {
        try {
            checkForecastsRequestVO(requestVO);
            Map<String, Object> resultMap = new HashMap();
            // 检查数据范围权限
            if (hasLv1Permission(requestVO, resultMap)) {
                return CommonResult.success(resultMap);
            }
            // 使用安全随机数
            requestVO.setRandom(AesGcmUtil.getSecureRandom());
            // 权限控制（全产业权限+网段权限控制）
            if (iLabelProductionService.checkSpartPermission(requestVO.getRoleId()) && accessService.isAccess()) {
                // L2下的Spart条目
                resultMap.put("spartInfoAndNum", iLabelProductionService.getProfitExaminingSpartYtdEntries(requestVO));
                // 数据集合
                resultMap.put("costList", iLabelProductionService.getProfitExaminingSpartYtdCostInfo(requestVO));
                resultMap.put("revList", iLabelProductionService.getProfitExaminingSpartYtdRevInfo(requestVO));
                resultMap.put("l2CostList",iLabelProductionService.getProfitExaminingL2YtdInfo(requestVO,Boolean.TRUE));
            }
            return CommonResult.success(resultMap);
        } catch (ApplicationException | KmsSdkException ex) {
            logger.error("盈利颗粒度审视Spart按年度数据查询：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视Spart按年度数据查询");
        }
    }

    @JalorOperation(code = "getPersonalSpartInfoPage", desc = "Jalor数据维度查询Spart维度数据分页")
    @Override
    public PagedResult getPersonalSpartInfo(PageVO pageVO, String spartCode, String spartDesc) {
        SpartInfoRequest request = new SpartInfoRequest(spartCode,spartDesc);
        return iLabelProductionService.getPersonalSpartInfoPage(request,pageVO);
    }

    @Audit(module="profitGranularityController-downloadProfitExaminingSpartInfo",operation="downloadProfitExaminingSpartInfo",message="盈利颗粒度审视详情下载")
    @JalorOperation(code = "downloadProfitExaminingSpartInfo", desc = "盈利颗粒度审视详情下载")
    @Override
    public CommonResult downloadProfitExaminingSpartInfo(HttpServletResponse response, ForecastsRequest request) {
        try {
            return iLabelProductionService.downloadProfitExaminingSpartInfo(response, request);
        } catch (CommonApplicationException ex) {
            logger.error("盈利颗粒度审视详情导出异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, ex.getMessage());
        }
    }

    @Audit(module="profitGranularityController-getProfitExaminingL3MonthInfo",operation="getProfitExaminingL3MonthInfo",message="盈利颗粒度审视L3指标数据按月度查询")
    @JalorOperation(code = "getProfitExaminingL3MonthInfo", desc = "盈利颗粒度审视L3指标数据按月度查询")
    @Override
    public CommonResult getProfitExaminingL3MonthInfo(ForecastsRequest requestVO) {
        try {
            Map<String, Object> resultMap = new HashMap();
            resultMap.put("lookUpSetting",lookupItemQueryService.findItemListByZhOrEn("SPART_INDICATORS_L3","Zh"));
            resultMap.put("dataList",iLabelProductionService.getProfitExaminingL3MonthInfo(requestVO));
            return CommonResult.success(resultMap);
        } catch (ApplicationException ex) {
            logger.error("盈利颗粒度审视L3指标数据按月度查询：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视L3指标数据按月度查询");
        }
    }

    @Audit(module="profitGranularityController-getProfitExaminingL3YtdInfo",operation="getProfitExaminingL3YtdInfo",message="盈利颗粒度审视L3指标数据按年度查询")
    @JalorOperation(code = "getProfitExaminingL3YtdInfo", desc = "盈利颗粒度审视L3指标数据按年度查询")
    @Override
    public CommonResult getProfitExaminingL3YtdInfo(ForecastsRequest requestVO) {
        try {
            Map<String, Object> resultMap = new HashMap();
            resultMap.put("lookUpSetting",lookupItemQueryService.findItemListByZhOrEn("SPART_INDICATORS_L3","Zh"));
            resultMap.put("dataList",iLabelProductionService.getProfitExaminingL3YtdInfo(requestVO));
            return CommonResult.success(resultMap);
        } catch (ApplicationException ex) {
            logger.error("盈利颗粒度审视L3指标数据按年度查询：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "盈利颗粒度审视L2指标数据按年度查询");
        }
    }

}
