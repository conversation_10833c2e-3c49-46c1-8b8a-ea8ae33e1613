/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.huawei.it.fcst.profits.common.utils.FcstJsonFormatVisitorWrapper;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The Entity of KrCpfL1Response
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:25:11
 */
@Getter
@Setter
public class KrCpfL1Response {

    /**
     * 重量级团队LV1编码
     **/
    private String lv1Code;

    /**
     * 目标时点
     **/
    private String targetPeriod;

    /**
     * 结转率
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal carryoverRatio;

    /**
     * BG编码
     **/
    private String bgCode;

    /**
     * L1名称
     **/
    private String l1Name;

    /**
     * 重量级团队LV1描述
     **/
    private String lv1Name;

    /**
     * MCA调整率
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal mcaAdjustRatio;

    /**
     * 制毛调整率
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal mgpAdjustRatio;

    /**
     * BG名称
     **/
    private String bgName;

    /**
     * 单位价格
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitPrice;

    /**
     * 币种
     **/
    private String currency;

    /**
     * 会计期
     **/
    private Long periodId;

    /**
     * 重量级团队LV2名称
     **/
    private String lv2Name;

    /**
     * 重量级团队LV2编码
     **/
    private String lv2Code;

    /**
     * 单位成本
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitCost;

    /**
     * 对价后设备收入预测_上标
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitPriceUpper;

    /**
     * 对价后设备收入预测_下标
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitPriceLower;

    /**
     * 单位成本预测_时序_上标
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitCostUpper;

    /**
     * 单位成本预测_时序_下标
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitCostLower;

    /**
     * 价格预测准确值
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal  unitPriceAcc;

    /**
     * 成本预测准确值
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal  unitCostAcc;


    private String groupKey;

    /**
     * 预测方法（分月法+YTD法）
     */
    private String fcstType;

    private String fcstStep;

    /**
     * sop期次
     */
    private String phaseDate;

    /**
     *
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal equipRevAfter;

    /**
     *
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal mgpRateAfter;

    private String overseaDesc ;

    // 区分预测，预算
    private String dataType;
}
