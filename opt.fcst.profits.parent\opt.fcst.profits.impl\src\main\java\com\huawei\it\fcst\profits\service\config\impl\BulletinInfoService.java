/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.service.config.impl;

import com.huawei.it.fcst.profits.common.utils.UserHandle;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.dao.IBulletinInfoDao;
import com.huawei.it.fcst.profits.service.IBulletinInfoService;
import com.huawei.it.fcst.profits.vo.BulletinInfoVO;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.Objects;

import javax.inject.Inject;

@Service
@Slf4j
public class BulletinInfoService implements IBulletinInfoService{

    @Inject
    private IBulletinInfoDao iBulletinInfoDao;

    /**
     * 获取当前用户公告相关信息
     *
     * @param bulletinInfoVO
     * @return
     */
    @Override
    public String getStatusByUserId(BulletinInfoVO bulletinInfoVO) {
        bulletinInfoVO.setUserId(String.valueOf(UserHandle.getUserId()));
        String status = iBulletinInfoDao.getStatusByUserId(bulletinInfoVO);
        if (!Objects.isNull(status)) {
            return status;
        }else {
            return "N";
        }
    }

    /**
     * 保存当前用户的公告阅读状态
     *
     * @param vo
     * @return
     */
    @Override
    public CommonResult saveStatus(BulletinInfoVO vo) {
        vo.setUserId(String.valueOf(UserHandle.getUserId()));
        // 插入前先查询
        if (iBulletinInfoDao.checkDataIfExist(vo) != 0) {
            return CommonResult.failed("公告已读");
        } else{
            iBulletinInfoDao.save(vo);
            return CommonResult.success("信息已保存");
        }
    }
}
