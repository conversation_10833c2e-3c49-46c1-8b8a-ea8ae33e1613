/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.huawei.it.fcst.profits.vo.request.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * The Entity of LabelModifyVO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-17 19:38:39
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LabelModifyVO extends PageRequest {
    private static final long serialVersionUID = 1L;

    /**
     * 最后更新人
     **/
    @JsonProperty("last_updated_by")
    private String lastUpdatedBy;

    /**
     * 模块
     **/
    @JsonProperty("page_module")
    private String pageModule;

    /**
     * 修改前
     **/
    @JsonProperty("status")
    private String status;

    private String id;

    /**
     * 创建时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("creation_date")
    private Timestamp creationDate;

    /**
     * 创建人
     **/
    @JsonProperty("created_by")
    private String createdBy;

    /**
     * 最后更新时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("last_update_date")
    private Timestamp lastUpdateDate;

    @JsonProperty("file_source_key")
    private String fileSourceKey;

    @JsonProperty("file_name")
    private String fileName;
}
