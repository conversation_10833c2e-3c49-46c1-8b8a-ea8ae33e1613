<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.profits.dao.ISpartProfitingRelationDao">
    <resultMap type="com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO" id="resultMap">
        <result property="itemCode" column="item_code"/>
        <result property="l2Name" column="l2_name"/>
        <result property="lv1Code" column="lv1_code"/>
        <result property="lv1Name" column="lv1_name"/>
        <result property="remark" column="remark"/>
        <result property="l1CoefficientProb" column="l1_coefficient_prob"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="l3Name" column="l3_name"/>
        <result property="l3Coefficient" column="l3_coefficient"/>
        <result property="periodId" column="period_id"/>
        <result property="l3NameProb" column="l3_name_prob"/>
        <result property="l2Coefficient" column="l2_coefficient"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="itemDesc" column="item_desc"/>
        <result property="l1Coefficient" column="l1_coefficient"/>
        <result property="l2CoefficientProb" column="l2_coefficient_prob"/>
        <result property="l1Name" column="l1_name"/>
        <result property="updateFlag" column="update_flag"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="l2NameProb" column="l2_name_prob"/>
        <result property="dataType" column="data_type"/>
        <result property="l3CoefficientProb" column="l3_coefficient_prob"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="allFields">
        item_code,
        l2_name,
        lv1_name,
        remark,
        l1_coefficient_prob,
        last_updated_by,
        l3_name,
        l3_coefficient,
        period_id,
        l3_name_prob,
        l2_coefficient,
        last_update_date,
        lv1_code,
        del_flag,
        item_desc,
        l1_coefficient,
        l2_coefficient_prob,
        l1_name,
        update_flag,
        creation_date,
        created_by,
        l2_name_prob,
        data_type,
        l3_coefficient_prob,
        status
    </sql>

    <sql id="allValues">
        #{itemCode,jdbcType=VARCHAR}
        ,
        #{l2Name,jdbcType=VARCHAR},
        #{lv1Name,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{l1CoefficientProb,jdbcType=NUMERIC},
        #{lastUpdatedBy,jdbcType=BIGINT},
        #{l3Name,jdbcType=VARCHAR},
        #{l3Coefficient,jdbcType=NUMERIC},
        #{periodId,jdbcType=NUMERIC},
        #{l3NameProb,jdbcType=NUMERIC},
        #{l2Coefficient,jdbcType=NUMERIC},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{lv1Code,jdbcType=VARCHAR},
        #{delFlag,jdbcType=VARCHAR},
        #{itemDesc,jdbcType=VARCHAR},
        #{l1Coefficient,jdbcType=NUMERIC},
        #{l2CoefficientProb,jdbcType=NUMERIC},
        #{l1Name,jdbcType=VARCHAR},
        #{updateFlag,jdbcType=VARCHAR},
        #{creationDate,jdbcType=TIMESTAMP},
        #{createdBy,jdbcType=BIGINT},
        #{l2NameProb,jdbcType=NUMERIC},
        #{dataType,jdbcType=VARCHAR},
        #{l3CoefficientProb,jdbcType=NUMERIC},
        #{status,jdbcType=VARCHAR}
    </sql>

    <select id="findByPage" resultMap="resultMap">
        select item_code,l2_name,lv1_name,l1_coefficient_prob,last_updated_by,l3_name,
        l3_coefficient,period_id,l3_name_prob,l2_coefficient,last_update_date,lv1_code,
        item_desc,l1_coefficient,l2_coefficient_prob,l1_name,
        creation_date,created_by,l2_name_prob,
        l3_coefficient_prob,status,data_type
        from (select item_code,l2_name,lv1_name,ifnull(round(l1_coefficient_prob,6),0)l1_coefficient_prob,last_updated_by,l3_name,
        ifnull(round(l3_coefficient,6),0)l3_coefficient,period_id,ifnull(round(l3_name_prob,6),0)l3_name_prob,ifnull(round(l2_coefficient,6),0)l2_coefficient,last_update_date,lv1_code,
        item_desc,ifnull(round(l1_coefficient,6),0)l1_coefficient,ifnull(round(l2_coefficient_prob,6),0)l2_coefficient_prob,l1_name,
        creation_date,created_by,ifnull(round(l2_name_prob,6),0)l2_name_prob,
        ifnull(round(l3_coefficient_prob,6),0)l3_coefficient_prob,status,data_type,
        row_number() over(partition by period_id,lv1_name ,l1_name,item_code,status order by update_flag desc,last_update_date desc) as rn
        from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag = 'N' and period_id =#{0.periodId,jdbcType=VARCHAR}
        ) A
        where rn = 1
        <if test='_parameter.get("0").lastUpdatedBys != null and _parameter.get("0").lastUpdatedBys.size() > 0'>
            and (A.last_updated_by in
            <foreach collection = '_parameter.get("0").lastUpdatedBys' item="itemVal" open="(" separator="," close=")">
                #{itemVal}
            </foreach>
            <foreach collection = '_parameter.get("0").lastUpdatedBys' item="itemVal" index="index" >
                <if test="itemVal == -2">
                    or A.last_updated_by is null
                </if>
            </foreach>
            )
        </if>
        <if test='_parameter.get("0").dataTypeList != null   and _parameter.get("0").dataTypeList.size()>0'>
            and A.data_type in
            <foreach collection='_parameter.get("0").dataTypeList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").lv1List != null  and _parameter.get("0").lv1List.size()>0'>
            and A.lv1_name in
            <foreach collection='_parameter.get("0").lv1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l1List != null and _parameter.get("0").l1List.size()>0'>
            and A.l1_name in
            <foreach collection='_parameter.get("0").l1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l2List != null and _parameter.get("0").l2List.size()>0'>
            and A.l2_name in
            <foreach collection='_parameter.get("0").l2List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l3List != null and _parameter.get("0").l3List.size()>0'>
            and (A.l3_name in
            <foreach collection='_parameter.get("0").l3List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            OR A.l3_name is null)
        </if>
        <if test='_parameter.get("0").statusList != null and _parameter.get("0").statusList.size()>0'>
            and A.status in
            <foreach collection='_parameter.get("0").statusList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by A.last_update_date desc ,A.lv1_name asc
        LIMIT #{1.pageSize} OFFSET #{1.mysqlStartIndex}
    </select>

    <select id="findUserIdsByParam" resultType="com.huawei.it.fcst.profits.vo.request.LabelConfigRequest">
        SELECT A.last_updated_by lastUpdatedBy,A.created_by createdBy
        FROM
        FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        A RIGHT JOIN ( SELECT period_id, item_code, l1_name, MAX(last_update_date) last_update_date
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag = 'N'
        <if test='periodId != null'>
            AND period_id =#{periodId,jdbcType=VARCHAR}
        </if>
        <if test='dataTypeList != null'>
            and data_type in
            <foreach collection='dataTypeList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='lv1List != null'>
            and lv1_name in
            <foreach collection='lv1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l1List != null'>
            and l1_name in
            <foreach collection='l1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l2List != null'>
            and l2_name in
            <foreach collection='l2List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l3List != null'>
            and (l3_name in
            <foreach collection='l3List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            OR l3_name IS NULL)
        </if>
        <if test='status != null'>
            and status ='Submit'
        </if>
        GROUP BY period_id, item_code, l1_name
        ) B ON A.period_id = B.period_id
        AND A.item_code = B.item_code
        AND A.l1_name = B.l1_name
        AND A.last_update_date = B.last_update_date
        where del_flag = 'N'
        <if test='periodId != null'>
            AND A.period_id =#{periodId,jdbcType=VARCHAR}
        </if>
        <if test='dataTypeList != null'>
            and A.data_type in
            <foreach collection='dataTypeList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='lv1List != null'>
            and A.lv1_name in
            <foreach collection='lv1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l1List != null'>
            and A.l1_name in
            <foreach collection='l1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l2List != null'>
            and A.l2_name in
            <foreach collection='l2List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l3List != null'>
            and (A.l3_name in
            <foreach collection='l3List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            OR A.l3_name is null)
        </if>
        <if test='status != null'>
            and A.status ='Submit'
        </if>
        GROUP BY A.last_updated_by,A.created_by
    </select>

    <select id="findByPageCount" resultType="int">
        select  count(*)
        from (select item_code,l2_name,lv1_name,l1_coefficient_prob,last_updated_by,l3_name,
        l3_coefficient,period_id,l3_name_prob,l2_coefficient,last_update_date,lv1_code,
        item_desc,l1_coefficient,l2_coefficient_prob,l1_name,
        creation_date,created_by,l2_name_prob,
        l3_coefficient_prob,status,data_type,
        row_number() over(partition by period_id,lv1_name ,l1_name,item_code,status order by update_flag desc,last_update_date desc) as rn
        from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag = 'N' and period_id =#{0.periodId,jdbcType=VARCHAR}
        ) A
        where rn = 1
        <if test='_parameter.get("0").lastUpdatedBys != null and _parameter.get("0").lastUpdatedBys.size() > 0'>
            and (A.last_updated_by in
            <foreach collection = '_parameter.get("0").lastUpdatedBys' item="itemVal" open="(" separator="," close=")">
                #{itemVal}
            </foreach>
            <foreach collection = '_parameter.get("0").lastUpdatedBys' item="itemVal" index="index" >
                <if test="itemVal == -2">
                    or A.last_updated_by is null
                </if>
            </foreach>
            )
        </if>
        <if test='_parameter.get("0").dataTypeList != null and _parameter.get("0").dataTypeList.size()>0'>
            and A.data_type in
            <foreach collection='_parameter.get("0").dataTypeList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").lv1List != null  and _parameter.get("0").lv1List.size()>0'>
            and A.lv1_name in
            <foreach collection='_parameter.get("0").lv1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l1List != null  and _parameter.get("0").l1List.size()>0'>
            and A.l1_name in
            <foreach collection='_parameter.get("0").l1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l2List != null  and _parameter.get("0").l2List.size()>0'>
            and A.l2_name in
            <foreach collection='_parameter.get("0").l2List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l3List != null  and _parameter.get("0").l3List.size()>0'>
            and (A.l3_name in
            <foreach collection='_parameter.get("0").l3List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test='_parameter.get("0").l3List.contains("NULL")'>
                or A.l3_name is null
            </if>
        )
        </if>
        <if test='_parameter.get("0").statusList != null and _parameter.get("0").statusList.size()>0'>
            and A.status in
            <foreach collection='_parameter.get("0").statusList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findConfigByPage" resultMap="resultMap">
        SELECT lv1_name,l1_name,l2_name,l3_name
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE status = 'Submit' AND del_flag = 'N' and data_type='His' and period_id='202211'
        <include refid="pageCondition1"></include>
        group by lv1_name,l1_name,l2_name,l3_name
        order by lv1_name,l1_name,l2_name,l3_name
        LIMIT #{1.pageSize}
        OFFSET #{1.mysqlStartIndex}
    </select>

    <select id="findConfigByPageCount" resultType="int">
        SELECT COUNT(1) FROM (select lv1_name,l1_name,l2_name,l3_name FROM
        FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE status = 'Submit' AND del_flag = 'N' and data_type='His' and period_id='202211'
        <include refid="pageCondition1"></include>
        group by lv1_name,l1_name,l2_name,l3_name)
    </select>

    <sql id="pageCondition">
        <if test='_parameter.get("0").dataTypeList != null'>
            and data_type in
            <foreach collection='_parameter.get("0").dataTypeList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").lv1List != null'>
            and lv1_name in
            <foreach collection='_parameter.get("0").lv1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l1List != null'>
            and l1_name in
            <foreach collection='_parameter.get("0").l1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l2List != null'>
            and l2_name in
            <foreach collection='_parameter.get("0").l2List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l3List != null'>
            and l3_name in
            <foreach collection='_parameter.get("0").l3List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").status != null'>
            and status ='Submit'
        </if>
    </sql>

    <sql id="pageCondition1">
        <if test='_parameter.get("0").dataTypeList != null and _parameter.get("0").dataTypeList.size()>0'>
            and data_type in
            <foreach collection='_parameter.get("0").dataTypeList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").lv1List != null  and _parameter.get("0").lv1List.size()>0'>
            and lv1_name in
            <foreach collection='_parameter.get("0").lv1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l1List != null   and _parameter.get("0").l1List.size()>0'>
            and l1_name in
            <foreach collection='_parameter.get("0").l1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l2List != null   and _parameter.get("0").l2List.size()>0'>
            and l2_name in
            <foreach collection='_parameter.get("0").l2List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='_parameter.get("0").l3List != null  and _parameter.get("0").l3List.size()>0'>
            and (l3_name in
            <foreach collection='_parameter.get("0").l3List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test='_parameter.get("0").l3List.contains("NULL")'>
                or l3_name is null
            </if>
            )
        </if>
        <if test='_parameter.get("0").statusList != null and _parameter.get("0").statusList.size()>0'>
            and status in
            <foreach collection='_parameter.get("0").statusList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="findLv1Data" resultType="com.huawei.it.fcst.profits.vo.LabelCountVO">
        SELECT lv1_name lv1Name
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE status = 'Submit' AND del_flag = 'N' and data_type='Add'
        <include refid="yearCondition"></include>
        group by lv1_name
    </select>

    <select id="getYearStatics" resultType="com.huawei.it.fcst.profits.vo.LabelCountVO">

        select A.lv1Name, A.periodId, A.totalNumber, ifnull(B.modifyNumber, 0) modifyNumber, round(ifnull(B.modifyNumber, 0)/ A.totalNumber, 6) modifyRate
        from (SELECT lv1_name lv1Name, period_id periodId, count(*) totalNumber
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE status = 'Submit'
        AND del_flag = 'N'
        and data_type = 'Add'
        and update_flag = 'N'
        <include refid="yearCondition"></include>
        group by lv1_name, period_id ) A
        left join (select lv1_name, period_id periodId, count(*) modifyNumber
        from (SELECT item_code, lv1_name,l1_name, l2_name, l3_name ,ifnull(round(l1_coefficient, 6), 0.000000), ifnull(round(l2_coefficient, 6), 0.000000), ifnull(round(l3_coefficient, 6), 0.000000), period_id
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE status = 'Submit'
        AND del_flag = 'N'
        and data_type = 'Add'
        and update_flag = 'Y'
        <include refid="yearCondition"></include>
        minus
        SELECT item_code, lv1_name,l1_name, l2_name, l3_name, ifnull(round(l1_coefficient, 6), 0.000000), ifnull(round(l2_coefficient, 6), 0.000000), ifnull(round(l3_coefficient, 6), 0.000000), period_id
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE status = 'Submit'
        AND del_flag = 'N'
        and data_type = 'Add'
        and update_flag = 'N'
        <include refid="yearCondition"></include>
        ) group by lv1_name, period_id) B on A.lv1Name = B.lv1_name and A.periodId = B.periodId order by a.lv1Name
    </select>

    <sql id="yearCondition">
        <if test='lv1List != null and lv1List.size() > 0'>
            and lv1_name in
            <foreach collection='lv1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='list != null and list.size() > 0'>
            AND period_id IN
            <foreach collection='list' item="item" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>

    <insert id="createList" parameterType="java.util.List">
        INSERT INTO FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        (period_id,item_code,item_desc,lv1_code,lv1_name,l1_name,l2_name,l3_name,l1_coefficient,l2_coefficient,l3_coefficient,creation_date,created_by,update_flag,del_flag,data_type,status,last_updated_by,last_update_date)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.periodId,jdbcType=NUMERIC},#{item.itemCode,jdbcType=VARCHAR},#{item.itemDesc,jdbcType=VARCHAR},#{item.lv1Code,jdbcType=VARCHAR},#{item.lv1Name,jdbcType=VARCHAR},#{item.l1Name,jdbcType=VARCHAR},#{item.l2Name,jdbcType=VARCHAR},
             #{item.l3Name,jdbcType=VARCHAR},#{item.l1Coefficient,jdbcType=NUMERIC},#{item.l2Coefficient,jdbcType=NUMERIC},#{item.l3Coefficient,jdbcType=NUMERIC},#{item.creationDate,jdbcType=TIMESTAMP},#{item.createdBy,jdbcType=BIGINT},#{item.updateFlag,jdbcType=VARCHAR},
             'N',#{item.dataType,jdbcType=VARCHAR},#{item.status,jdbcType=VARCHAR},#{item.lastUpdatedBy,jdbcType=BIGINT},#{item.lastUpdateDate,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <delete id="uptTempList" parameterType="java.util.List">
        delete from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE status='Save' and del_flag='N'
        and (period_id,item_code,l1_name) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.periodId,jdbcType=VARCHAR},#{item.itemCode,jdbcType=VARCHAR},#{item.l1Name,jdbcType=VARCHAR})
        </foreach>
    </delete>

    <delete id="uptCoverSubmitList" parameterType="java.util.List">
        delete from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE del_flag='N' and status = 'Submit'
        and (period_id,item_code,l1_name,creation_date) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.periodId,jdbcType=VARCHAR},#{item.itemCode,jdbcType=VARCHAR},#{item.l1Name,jdbcType=VARCHAR},#{item.creationDate,jdbcType=TIMESTAMP})
        </foreach>
    </delete>

    <delete id="uptMoveList" parameterType="java.util.List">
        delete from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE del_flag='N' and status='Save'
        <if test='list != null and list != "" and list.size() > 0'>
            and (period_id,item_code,l1_name) IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                (#{item.periodId,jdbcType=VARCHAR},#{item.itemCode,jdbcType=VARCHAR},#{item.l1Name,jdbcType=VARCHAR})
            </foreach>
        </if>
    </delete>

    <update id="updateMoveList">
        UPDATE FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t set status='Submit',
        <if test='lastUpdatedBy != null'>
            last_updated_by = #{lastUpdatedBy,jdbcType=BIGINT},
        </if>
        last_update_date=#{lastUpdateDate,jdbcType=TIMESTAMP}
        WHERE del_flag='N' and status='Save'
        <if test='updateFlag != null and updateFlag != ""'>
            and update_flag =#{updateFlag,jdbcType=VARCHAR}
        </if>
        <if test='list != null and list != "" and list.size() > 0'>
            and (period_id,item_code,l1_name) IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                (#{item.periodId,jdbcType=VARCHAR},#{item.itemCode,jdbcType=VARCHAR},#{item.l1Name,jdbcType=VARCHAR})
            </foreach>
        </if>
    </update>

    <delete id="updateOldList">
        delete from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t where del_flag='N'
        <if test='list != null and list != "" and list.size() > 0'>
            and (period_id,item_code,l1_name,status) IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                (#{item.periodId,jdbcType=VARCHAR},#{item.itemCode,jdbcType=VARCHAR},#{item.l1Name,jdbcType=VARCHAR},'Save')
            </foreach>
        </if>
    </delete>

    <delete id="deleteInvalidList"  parameterType="com.huawei.it.fcst.profits.vo.request.LabelInfoRequest">
        delete from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t where del_flag='N'
        <if test='list != null and list != "" and list.size() > 0'>
            and (period_id,item_code,lv1_name,l1_name,nvl(l2_name,'snull'),nvl(l3_name,'snull')) IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                (#{item.periodId,jdbcType=VARCHAR},#{item.itemCode,jdbcType=VARCHAR},#{item.lv1Name,jdbcType=VARCHAR},#{item.l1Name,jdbcType=VARCHAR},#{item.l2Name,jdbcType=VARCHAR},#{item.l3Name,jdbcType=VARCHAR})
            </foreach>
        </if>
    </delete>

    <update id="updateList" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
            set
            <choose>
                <when test='item.optType != null and item.optType == "add"'>
                    status='Submit',
                </when>
                <when test='item.optType != null and item.optType == "move"'>
                    status='Submit',
                </when>
                <when test='item.optType != null and item.optType == "cover"'>
                    <if test='item.l2Name != null'>
                        l2_name = #{item.l2Name,jdbcType=VARCHAR},
                    </if>
                    <if test='item.l3Name != null'>
                        l3_name = #{item.l3Name,jdbcType=VARCHAR},
                    </if>
                    <if test='item.l1Coefficient != null'>
                        l1_coefficient = #{item.l1Coefficient,jdbcType=NUMERIC},
                    </if>
                    <if test='item.l2Coefficient != null'>
                        l2_coefficient = #{item.l2Coefficient,jdbcType=NUMERIC},
                    </if>
                    <if test='item.l3Coefficient != null'>
                        l3_coefficient = #{item.l3Coefficient,jdbcType=NUMERIC},
                    </if>
                </when>
                <otherwise>
                    status=#{item.status,jdbcType=VARCHAR},
                    <if test='item.l2Name != null'>
                        l2_name = #{item.l2Name,jdbcType=VARCHAR},
                    </if>
                    <if test='item.l3Name != null'>
                        l3_name = #{item.l3Name,jdbcType=VARCHAR},
                    </if>
                    <if test='item.l1Coefficient != null'>
                        l1_coefficient = #{item.l1Coefficient,jdbcType=NUMERIC},
                    </if>
                    <if test='item.l2Coefficient != null'>
                        l2_coefficient = #{item.l2Coefficient,jdbcType=NUMERIC},
                    </if>
                    <if test='item.l3Coefficient != null'>
                        l3_coefficient = #{item.l3Coefficient,jdbcType=NUMERIC},
                    </if>
                </otherwise>
            </choose>
            <if test='item.lastUpdatedBy != null'>
                last_updated_by = #{item.lastUpdatedBy,jdbcType=BIGINT},
            </if>
            last_update_date=#{item.lastUpdateDate,jdbcType=TIMESTAMP}
            WHERE del_flag='N'
            <choose>
                <when test='item.optType != null and item.optType == "add"'>
                    and status='Save' and update_flag ='N'
                </when>
                <when test='item.optType != null and item.optType == "move"'>
                    and status='Save' and update_flag ='Y'
                </when>
                <when test='item.optType != null and item.optType == "cover"'>
                    and status='Submit' and update_flag ='Y' and creation_date = #{item.creationDate,jdbcType=TIMESTAMP}
                </when>
                <otherwise>
                    and status='Save'
                </otherwise>
            </choose>
            <if test='item.periodId != null'>
                and period_id = #{item.periodId,jdbcType=VARCHAR}
            </if>
            <if test='item.itemCode != null'>
                and item_code = #{item.itemCode,jdbcType=VARCHAR}
            </if>
            <if test='item.l1Name != null'>
                AND l1_name = #{item.l1Name,jdbcType=VARCHAR}
            </if>
        </foreach>
    </update>

    <select id="getLv1List" parameterType="com.huawei.it.fcst.profits.vo.request.LabelConfigRequest"
            resultMap="resultMap">
        select lv1_name
        from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where status = 'Submit'
        and (lv1_name is not null)
        AND del_flag = 'N' and data_type='His' and period_id='202211'
        <if test='lv1List != null and lv1List.size() > 0'>
            AND lv1_name IN
            <foreach collection='lv1List' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by lv1_name
    </select>

    <select id="getAuditLv1Names" parameterType="com.huawei.it.fcst.profits.vo.request.LabelConfigRequest"
            resultMap="resultMap">
        select lv1_name
        from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where (lv1_name is not null)
        AND del_flag = 'N'
        <if test='periodId != null and periodId != ""'>
            and period_id = #{periodId}
        </if>
        <if test='lv1List != null and lv1List.size() > 0'>
            AND lv1_name IN
            <foreach collection='lv1List' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by lv1_name
    </select>

    <select id="getSpartConfInfo" resultType="java.lang.String">
        select distinct concat(lv1_name, l1_name, l2_name)
        FROM FIN_DM_OPT_FOP.apd_fop_spart_label_config_t
        WHERE status = 'Submit' AND del_flag = 'N'
    </select>

    <select id="getItemCodeL1Info" resultMap="resultMap">
        SELECT item_code, l1_name
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE status = 'Submit' AND del_flag = 'N'
        <if test='periodId != null and periodId != ""'>
            and period_id = #{periodId}
        </if>
        group by item_code,l1_name
    </select>

    <select id="getL1NameList" resultMap="resultMap"
            parameterType="com.huawei.it.fcst.profits.vo.request.LabelConfigRequest">
        select l1_name from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag ='N' and status='Submit' and (l1_name is not null)
        and data_type='His' and period_id='202211'
        <include refid="selectCondition"></include>
        group by l1_name
        order by l1_name
    </select>

    <select id="getAuditL1Names" resultMap="resultMap"
            parameterType="com.huawei.it.fcst.profits.vo.request.LabelConfigRequest">
        select l1_name from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag ='N' and (l1_name is not null)
        <if test='periodId != null and periodId != ""'>
            and period_id = #{periodId}
        </if>
        <include refid="selectCondition"></include>
        group by l1_name
        order by l1_name
    </select>

    <insert id="copyList">
        INSERT INTO FIN_DM_OPT_FOP.dm_fop_spart_rel_t(period_id, item_code, item_desc, lv1_code, lv1_name, l1_name,
                                                l2_name, l3_name, l1_coefficient, l2_coefficient, l3_coefficient,
                                                creation_date, created_by, update_flag, del_flag, data_type, status,
                                                handle_type,last_updated_by)
        SELECT period_id,
               item_code,
               item_desc,
               lv1_code,
               lv1_name,
               l1_name,
               l2_name,
               l3_name,
               l1_coefficient,
               l2_coefficient,
               l3_coefficient,
               creation_date,
               created_by,
               update_flag,
               del_flag,
               data_type,
               status,
               '0',
               last_updated_by
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE del_flag = 'N'
          AND status = 'Save'
          and last_updated_by =#{lastUpdatedBy}
    </insert>

    <update id="updateCopyStatus" parameterType="java.util.List">
        UPDATE FIN_DM_OPT_FOP.dm_fop_spart_rel_t set handle_type ='1'
        WHERE status='Save' and del_flag='N' and handle_type ='0'
        <if test='list != null  and list != "" and list.size() > 0'>
            and (period_id,item_code,l1_name) in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                (#{item.periodId,jdbcType=NUMERIC},#{item.itemCode,jdbcType=VARCHAR},#{item.l1Name,jdbcType=VARCHAR})
            </foreach>
        </if>
    </update>

    <delete id="uptCopyList">
        delete from FIN_DM_OPT_FOP.dm_fop_spart_rel_t
        WHERE status='Save' and del_flag='N' and last_updated_by =#{lastUpdatedBy}
        <if test='handleType != null and handleType != ""'>
            and handle_type=#{handleType,jdbcType=VARCHAR}
        </if>
        <if test='items != null and items != "" and items.size() > 0'>
            and (period_id,item_code,l1_name) IN
            <foreach collection="items" item="item" open="(" separator="," close=")">
                (#{item.periodId,jdbcType=VARCHAR},#{item.itemCode,jdbcType=VARCHAR},#{item.l1Name,jdbcType=VARCHAR})
            </foreach>
        </if>
    </delete>

    <delete id="delCopyListByUser">
        delete from FIN_DM_OPT_FOP.dm_fop_spart_rel_t
        WHERE status='Save' and del_flag='N' and last_updated_by =#{lastUpdatedBy}
    </delete>

    <delete id="uptCoverList">
        delete from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE del_flag='N'
        <if test='items != null and items != "" and items.size() > 0'>
            and (period_id,item_code,l1_name) IN
            <foreach collection="items" item="item" open="(" separator="," close=")">
                (#{item.periodId,jdbcType=VARCHAR},#{item.itemCode,jdbcType=VARCHAR},#{item.l1Name,jdbcType=VARCHAR})
            </foreach>
        </if>
    </delete>

    <select id="findCopyDataByParam" resultMap="resultMap" parameterType="java.lang.String">
        SELECT item_code,l2_name,lv1_name,remark,l1_coefficient_prob,last_updated_by,l3_name,l3_coefficient,period_id,
        l3_name_prob,l2_coefficient,last_update_date,lv1_code,del_flag,item_desc,l1_coefficient,l2_coefficient_prob,l1_name,
        update_flag,creation_date,created_by,l2_name_prob,data_type,l3_coefficient_prob,status,handle_type
        FROM FIN_DM_OPT_FOP.dm_fop_spart_rel_t
        where del_flag='N' AND status='Save' and last_updated_by =#{lastUpdatedBy}
        <if test='handleType != null and handleType != ""'>
            and handle_type=#{handleType,jdbcType=VARCHAR}
        </if>
        limit 0,6000
    </select>

    <select id="findCopyDataCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT count(1) from FIN_DM_OPT_FOP.dm_fop_spart_rel_t
        where del_flag='N' AND status='Save'
        <if test='lastUpdatedBy != null'>
            and last_updated_by = #{lastUpdatedBy}
        </if>
        <if test='handleType != null and handleType != ""'>
            and handle_type=#{handleType,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getL2NameList" resultMap="resultMap"
            parameterType="com.huawei.it.fcst.profits.vo.request.LabelConfigRequest">
        select l2_name from FIN_DM_OPT_FOP.apd_fop_spart_label_config_t
        where del_flag ='N' and status='Submit' and (l2_name is not null)
        <include refid="selectCondition"></include>
        group by l2_name
        order by l2_name
    </select>

    <select id="getAuditL2Names" resultMap="resultMap"
            parameterType="com.huawei.it.fcst.profits.vo.request.LabelConfigRequest">
        select l2_name from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where (period_id,item_code,l1_name) in(
        select period_id,item_code,l1_name from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag ='N' and (l1_name is not null)
        <if test='periodId != null and periodId != ""'>
            and period_id = #{periodId}
        </if>
        <include refid="selectCondition"></include>
        group by period_id,item_code,l1_name
        HAVING count(*)=1) GROUP BY l2_name
        union
        select l2_name from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where (period_id,item_code,l1_name,last_update_date) in(
        select period_id,item_code,l1_name,max(last_update_date) from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag ='N' and (l1_name is not null)
        <if test='periodId != null and periodId != ""'>
            and period_id = #{periodId}
        </if>
        <include refid="selectCondition"></include>
        group by period_id,item_code,l1_name
        HAVING count(*)>=2) GROUP BY l2_name
    </select>

    <select id="getL3NameList" resultMap="resultMap"
            parameterType="com.huawei.it.fcst.profits.vo.request.LabelConfigRequest">
        select l3_name from FIN_DM_OPT_FOP.apd_fop_spart_label_config_t
        where del_flag ='N' and status='Submit'
        <include refid="selectCondition"></include>
        group by l3_name
        order by l3_name
    </select>

    <select id="getAuditL3Names" resultMap="resultMap"
            parameterType="com.huawei.it.fcst.profits.vo.request.LabelConfigRequest">
        select l3_name from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where (period_id,item_code,l1_name) in(
        select period_id,item_code,l1_name from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag ='N' and (l2_name is not null)
        <if test='periodId != null and periodId != ""'>
            and period_id = #{periodId}
        </if>
        <include refid="selectCondition"></include>
        group by period_id,item_code,l1_name
        HAVING count(*)=1) GROUP BY l3_name
        union
        select l3_name from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where (period_id,item_code,l1_name,last_update_date) in(
        select period_id,item_code,l1_name,max(last_update_date) from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag ='N' and (l2_name is not null)
        <if test='periodId != null and periodId != ""'>
            and period_id = #{periodId}
        </if>
        <include refid="selectCondition"></include>
        group by period_id,item_code,l1_name
        HAVING count(*)>=2) GROUP BY l3_name
    </select>

    <sql id="selectCondition">
        <if test='lv1List != null'>
            <choose>
                <when test='lv1List.size>0'>
                    and lv1_name in
                    <foreach collection='lv1List' item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and lv1_name is null
                </otherwise>
            </choose>
        </if>
        <if test='l1List != null'>
            <choose>
                <when test='l1List.size>0'>
                    and l1_name in
                    <foreach collection='l1List' item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and l1_name is null
                </otherwise>
            </choose>
        </if>
        <if test='l2List != null'>
            <choose>
                <when test='l2List.size>0'>
                    and l2_name in
                    <foreach collection='l2List' item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and l2_name is null
                </otherwise>
            </choose>
        </if>
        <if test='l3List != null'>
            <choose>
                <when test='l3List.size>0'>
                    and l3_name in
                    <foreach collection='l3List' item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and l3_name is null
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="getVersions" resultType="java.lang.String">
        select period_id
        from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag = 'N'
          and status = 'Submit'
        group by period_id
        order by period_id desc
    </select>

    <select id="findDataByList" parameterType="java.util.List" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE del_flag='N'
        <if test='list != null'>
            and (period_id,item_code,l1_name) in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                (#{item.periodId,jdbcType=VARCHAR},#{item.itemCode,jdbcType=VARCHAR},
                #{item.l1Name,jdbcType=VARCHAR})
            </foreach>
        </if>
    </select>

    <select id="getWaitCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        WHERE del_flag = 'N'
          AND status = 'Save'
          and last_updated_by = #{lastUpdatedBy}
    </select>

    <select id="findDataByParam" resultMap="resultMap"
            parameterType="com.huawei.it.fcst.profits.vo.request.LabelInfoRequest">
        SELECT
        <include refid="allFields"/>
        FROM FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag = 'N'
        <choose>
            <when test='status != null and status == "Save"'>
                AND (period_id,item_code,l1_name,status) in ( SELECT period_id,item_code,l1_name,status FROM
                FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t WHERE del_flag='N' AND status='Save')
            </when>
            <otherwise>
                AND (period_id,item_code,l1_name,status) IN(SELECT period_id,item_code,l1_name,status FROM
                FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t WHERE del_flag='N' AND status='Submit'
                <if test='submitList != null'>
                    and (period_id,item_code,l1_name) in
                    <foreach collection="submitList" item="item" separator="," open="(" close=")">
                        (#{item.periodId,jdbcType=NUMERIC},#{item.itemCode,jdbcType=VARCHAR},
                        #{item.l1Name,jdbcType=VARCHAR})
                    </foreach>
                </if>
                )
            </otherwise>
        </choose>
        <if test='optType != null and optType == 1'>
            limit #{startIndex},#{offset}
        </if>
    </select>

    <select id="getLv1Code" resultType="com.huawei.it.fcst.profits.vo.request.LabelInfoRequest">
        SELECT lv1_prod_rnd_team_code lv1Code,lv1_prod_rd_team_cn_name lv1Name
        FROM dmdim.dm_dim_product_d
        WHERE del_flag = 'N' AND lv1_prod_rd_team_cn_name
        IN ( SELECT lv1_name FROM FIN_DM_OPT_FOP.apd_fop_ict_fcst_holistic_view_t WHERE del_flag = 'N' GROUP BY lv1_name )
        GROUP BY lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name
    </select>

    <select id="countRecord" resultType="int" parameterType="com.huawei.it.fcst.profits.vo.request.LabelConfigRequest">
        SELECT count(1) from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t where del_flag = 'N'
        <if test='itemCode != null'>
            and item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='l1Name != null'>
            AND l1_name = #{l1Name,jdbcType=VARCHAR}
        </if>
        <if test='lv1Name != null'>
            AND lv1_name = #{lv1Name,jdbcType=VARCHAR}
        </if>
        <if test='itemDesc != null'>
            AND item_desc = #{itemDesc,jdbcType=VARCHAR}
        </if>
        <if test='periodId != null'>
            AND period_id = #{periodId,jdbcType=VARCHAR}
        </if>
    </select>

    <sql id="configConditions">
        <if test='periods != null'>
            AND period_id IN
            <foreach collection='periods' item="item" separator="," open="(" close=")" index="">
                <![CDATA[
                    #{item,jdbcType=NUMERIC}
                ]]>
            </foreach>
        </if>
        <if test='itemCodes != null'>
            AND item_code IN
            <foreach collection='itemCodes' item="item" separator="," open="(" close=")" index="">
                <![CDATA[
                    #{item,jdbcType=VARCHAR}
                ]]>
            </foreach>
        </if>
        <if test='lv1Names != null'>
            AND lv1_name IN
            <foreach collection='lv1Names' item="item" separator="," open="(" close=")" index="">
                <![CDATA[
                    #{item,jdbcType=VARCHAR}
                ]]>
            </foreach>
        </if>
        <if test='l1Names != null'>
            AND l1_name IN
            <foreach collection='l1Names' item="item" separator="," open="(" close=")" index="">
                <![CDATA[
                    #{item,jdbcType=VARCHAR}
                ]]>
            </foreach>
        </if>
        <if test='l2Names != null'>
            AND l2_name IN
            <foreach collection='l2Names' item="item" separator="," open="(" close=")" index="">
                <![CDATA[
                    #{item,jdbcType=VARCHAR}
                ]]>
            </foreach>
        </if>
        <if test='l3Names != null'>
            AND l3_name IN
            <foreach collection='l3Names' item="item" separator="," open="(" close=")" index="">
                <![CDATA[
                    #{item,jdbcType=VARCHAR}
                ]]>
            </foreach>
        </if>
    </sql>

    <select id="getUpdateDataTimeInfo" resultType="com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO">
        select max(last_update_date) as lastUpdateDate
        from FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t
        where del_flag = 'N'
        and status = 'Submit'
        and last_update_date <![CDATA[ < ]]> #{targetPeriod,jdbcType=VARCHAR}
    </select>

    <select id="getCurrentDataTimeInfo" resultType="com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO">
        select max(last_update_date) as lastUpdateDate
        from FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t
        where del_flag = 'N'
    </select>

    <select id="getLastUpdatedBys" resultType="java.lang.Long">
        select DISTINCT decode(last_updated_by,null,-2,last_updated_by) from  FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t where del_flag = 'N'
        <if test='periodId != null'>
            AND period_id =#{periodId,jdbcType=VARCHAR}
        </if>
        <if test='dataTypeList != null'>
            and data_type in
            <foreach collection='dataTypeList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='lv1List != null'>
            and lv1_name in
            <foreach collection='lv1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l1List != null'>
            and l1_name in
            <foreach collection='l1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l2List != null'>
            and l2_name in
            <foreach collection='l2List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l3List != null'>
            and (l3_name in
            <foreach collection='l3List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            OR l3_name IS NULL)
        </if>
    </select>
    <select id="getStatusList" resultType="com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO">
        select DISTINCT status
        from  FIN_DM_OPT_FOP.dm_dim_fop_spart_profiting_relation_t where del_flag = 'N'
        <if test='periodId != null'>
            AND period_id =#{periodId,jdbcType=VARCHAR}
        </if>
        <if test='dataTypeList != null'>
            and data_type in
            <foreach collection='dataTypeList' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='lv1List != null'>
            and lv1_name in
            <foreach collection='lv1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l1List != null'>
            and l1_name in
            <foreach collection='l1List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l2List != null'>
            and l2_name in
            <foreach collection='l2List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test='l3List != null'>
            and (l3_name in
            <foreach collection='l3List' item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            OR l3_name IS NULL)
        </if>
    </select>
    <select id="findItemCode" resultType="string">
        SELECT DISTINCT ITEM_CODE FROM FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T  where del_flag = 'N' and  item_code in
        <foreach collection='items' item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
