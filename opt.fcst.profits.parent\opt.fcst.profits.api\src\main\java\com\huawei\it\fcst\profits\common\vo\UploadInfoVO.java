/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.common.vo;

import com.huawei.it.fcst.profits.vo.DmFopRecordVO;
import com.huawei.it.fcst.profits.vo.request.LabelInfoRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.io.InputStream;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/11/4
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UploadInfoVO implements Serializable {

    private static final long serialVersionUID = -5394978246348938140L;
    private String fileName;

    private String sheetName;

    private long fileSize;

    private Map<String, Object> params;

    private int rowNumber;

    private String version;

    private Long userId;

    private DmFopRecordVO dmFoiImpExpRecord;

    private String fileKey;

    private boolean optFlag;

    private List<LabelInfoRequest> dataList;

    private List<LabelInfoRequest> verifyList;

    private List<String> checkList;

    private String errorTips;

    private Integer count;

    private InputStream inputStream;

    private String suffix;

    private Timestamp startTime;

}
