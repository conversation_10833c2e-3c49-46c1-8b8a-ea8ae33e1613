/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * The Entity of LabelConfigRequest
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LabelConfigQueryRequest extends PageRequest {
    /**
     * 会计期
     **/
    private Long periodId;

    /**
     * LV1编码
     **/
    private String[] lv1Name;

    private String[] lv1Code;

    private String[] lv2Name;

    private String[] lv2Code;

    private String[] lv3Name;

    private String[] lv3Code;

    /**
     * L1名称
     **/
    private String[] l1Name;

    private String[] l1Code;

    /**
     * L2名称
     **/
    private String[] l2Name;

    private String[] l2Code;

    /**
     * L3名称
     **/
    private String[] l3Name;

    private String[] l3Code;

    private String[] planComLv1;

    private String[] planComLv2;

    private String[] planComLv3;

    private String[] busiLv4;

    private String[] coaCode;

    private String[] articulationFlag;

    private String dataType;

    private String status;

    private Integer roleId;

    private Long[] lastUpdatedBy;

    private Long createdBy;

    private List<String> lv1Names;

    private String versionCode;

    private String[] lv0Name;

    private String[] prodName;

    private String phaseDate;

    private List<String> bgCodes;

    private String[] analysisFlag;

    private String overseaDesc;

    private String phaseDateType;
}
