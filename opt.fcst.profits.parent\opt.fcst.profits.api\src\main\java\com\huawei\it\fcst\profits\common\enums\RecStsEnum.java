/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * RecStsEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum RecStsEnum {
    PROGRESSING("ING", "处理中"),
    SUCCESS("OK", "成功"),
    FAIL("FAIL", "失败");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    RecStsEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * getByCode
     *
     * @param code code
     * @return StepTypeEnum
     */
    public static RecStsEnum getByCode(String code) {
        for (RecStsEnum value : RecStsEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

