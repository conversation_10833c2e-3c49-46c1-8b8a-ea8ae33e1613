/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.vo;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 功能描述预测步长
 *
 * <AUTHOR>
 * @since 2023年04月12日
 */
@Getter
@Setter
public class ForecastsStepVO {
    // 预测步长
    private String fcstStep;
    private String fcstStepDisplay;

    // 月度预测，年度预算页签值
    private String predictionType;

    // l1,l2图表页签区分值
    private String tabGraphType;

    // 排序值
    private int index;

    // 方法集合
    private List<ForecastsMethodVO> forecastsMethods;
}
