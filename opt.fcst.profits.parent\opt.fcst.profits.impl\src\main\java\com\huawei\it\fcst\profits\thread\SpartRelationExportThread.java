/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.thread;

import com.huawei.it.fcst.profits.common.constants.RelationConstants;
import com.huawei.it.fcst.profits.dao.ISpartProfitingRelationDao;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.LabelConfigRequest;
import com.huawei.it.jalor5.core.base.PageConfig;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;

/**
 * 标签功能
 *
 * <AUTHOR>
 * @since 2022/10/02.
 */
public class SpartRelationExportThread implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(SpartRelationExportThread.class);

    private LabelConfigRequest requestVO;

    private final Object lock = new Object();

    private CountDownLatch countDownLatch;

    private CopyOnWriteArrayList<SpartProfitingRelationVO> copyList;

    public SpartRelationExportThread(LabelConfigRequest requestVO, CountDownLatch countDownLatch, CopyOnWriteArrayList<SpartProfitingRelationVO> copyList) {
        this.countDownLatch = countDownLatch;
        this.requestVO = requestVO;
        this.copyList = copyList;
    }

    public List<SpartProfitingRelationVO> getlv1Data(LabelConfigRequest requestVO) throws CommonApplicationException {
        PageVO pageVO = new PageVO();
        PageConfig.DEFAULT.setMaxPageSize(600000);
        pageVO.setPageSize(requestVO.getPageSize() > RelationConstants.PAGE_MAX_SIZE ? RelationConstants.PAGE_MAX_SIZE : requestVO.getPageSize());
        pageVO.setCurPage(requestVO.getPageIndex());
        CommUtils.buildReqParams(requestVO);
        Object relationDao = Jalor.getContext().getBean("ISpartProfitingRelationDao");
        if (Objects.isNull(relationDao)) {
            return new ArrayList<>();
        }
        PagedResult<SpartProfitingRelationVO> spartProfitingResult = ((ISpartProfitingRelationDao) relationDao).findByPage(requestVO, pageVO);
        List<SpartProfitingRelationVO> result = spartProfitingResult.getResult();
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result;
    }

    @Override
    public void run() {
        synchronized (lock) {
            try {
                copyList.addAll(getlv1Data(requestVO));
                countDownLatch.countDown();
            } catch (CommonApplicationException ex) {
                countDownLatch.countDown();
                logger.error("SpartRelationExportThread occurs error: {}", ex);
            }
        }
    }
}
