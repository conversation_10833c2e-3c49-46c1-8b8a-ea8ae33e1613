/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.constants;

import com.huawei.it.fcst.profits.common.enums.Lv1Enum;

import java.util.LinkedHashMap;
import java.util.Map;

public class RelationConstants {

    public static final Map<String, String> AUDIT_CHILD_IMP_HEADER = new LinkedHashMap<>();

    public static final Map<String, String> LV1_FULL_MAPS = new LinkedHashMap<>();

    public static final int PAGE_MAX_SIZE = 600000;

    public static final String CACHE_SPART_VERSION = "SPART_VERSION";

    public static final String CACHE_KEY_SPART_VERSION = "SPART_KEY_VERSION";

    public static final String CACHE_FOREASE_VERSION = "SPART_VERSION";

    public static final String CACHE_KEY_FOREASE_VERSION = "SPART_KEY_VERSION";

    // 批量操作计算开线程参数设置
    public static final int BATCH_COUNT = 600;

    static {
        AUDIT_CHILD_IMP_HEADER.put("是否有效", "isVaild");
        AUDIT_CHILD_IMP_HEADER.put("Spart编码", "itemCode");
        AUDIT_CHILD_IMP_HEADER.put("Spart描述", "itemDesc");
        AUDIT_CHILD_IMP_HEADER.put("产业", "lv1Name");
        AUDIT_CHILD_IMP_HEADER.put("L1名称", "l1Name");
        AUDIT_CHILD_IMP_HEADER.put("L2名称", "l2Name");
        AUDIT_CHILD_IMP_HEADER.put("L3名称", "l3Name");
        AUDIT_CHILD_IMP_HEADER.put("L1系数", "l1Coefficient");
        AUDIT_CHILD_IMP_HEADER.put("L2系数", "l2Coefficient");
        AUDIT_CHILD_IMP_HEADER.put("L3系数", "l3Coefficient");

        LV1_FULL_MAPS.put(Lv1Enum.LV1_1.getCode(),Lv1Enum.LV1_1.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_2.getCode(),Lv1Enum.LV1_2.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_3.getCode(),Lv1Enum.LV1_3.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_4.getCode(),Lv1Enum.LV1_4.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_5.getCode(),Lv1Enum.LV1_5.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_6.getCode(),Lv1Enum.LV1_6.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_7.getCode(),Lv1Enum.LV1_7.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_8.getCode(),Lv1Enum.LV1_8.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_9.getCode(),Lv1Enum.LV1_9.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_10.getCode(),Lv1Enum.LV1_10.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_11.getCode(),Lv1Enum.LV1_11.getDesc());
        LV1_FULL_MAPS.put(Lv1Enum.LV1_12.getCode(),Lv1Enum.LV1_12.getDesc());
    }

}
