/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.common.vo.ResultCode;
import com.huawei.it.fcst.profits.service.IGroupAnalystsService;
import com.huawei.it.fcst.profits.vo.GroupAnalystsVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;

import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

/**
 * 集团分析师预测
 */
@JalorResource(code = "groupAnalystsController", desc = "GroupAnalystsController")
@Named("groupAnalystsController")
public class GroupAnalystsController implements IGroupAnalystsController {

    private static final Logger logger = LoggerFactory.getLogger(GroupAnalystsController.class);

    @Autowired
    private IGroupAnalystsService iGroupAnalystsService;

    /**
     * 集团分析师预测查询下拉框功能
     *
     * @param requestVO 任务ID
     * @return CommonResult
     */
    @JalorOperation(code = "dropDownBox", desc = "查询下拉框功能")
    @Override
    public CommonResult dropDownBox(GroupAnalystsVO requestVO) throws CommonApplicationException {
        return CommonResult.success(iGroupAnalystsService.getDropDownBox(requestVO));
    }

    /**
     * 集团分析师预测分页查询功能
     *
     * @param request
     * @return CommonResult
     */
    @JalorOperation(code = "getGroupAnalystsPageInfo", desc = "集团分析师预测分页查询功能")
    @Override
    public CommonResult getGroupAnalystsPageInfo(GroupAnalystsVO request) {
        return CommonResult.success(iGroupAnalystsService.getGroupAnalystsPageInfo(request));
    }

    /**
     * 集团分析师预测导出功能
     *
     * @param response
     * @param request
     * @return CommonResult
     */
    @Audit(module="groupAnalystsController-exportGroupAnalystsInfo",operation="exportGroupAnalystsInfo",message="集团分析师预测导出功能")
    @JalorOperation(code = "exportGroupAnalystsInfo", desc = "集团分析师预测导出功能")
    @Override
    public CommonResult exportGroupAnalystsInfo(HttpServletResponse response, GroupAnalystsVO request) {
        try {
            return iGroupAnalystsService.exportGroupAnalystsInfo(response, request);
        } catch (CommonApplicationException e) {
            logger.error("导出异常：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    /**
     * 集团分析师预测导入功能
     *
     * @param file
     * @return CommonResult
     */
    @Audit(module="groupAnalystsController-importGroupAnalystsInfo",operation="importGroupAnalystsInfo",message="集团分析师预测导入功能")
    @JalorOperation(code = "importGroupAnalystsInfo", desc = "集团分析师预测导入功能")
    @Override
    public CommonResult importGroupAnalystsInfo(Attachment file) {
        try {
            return iGroupAnalystsService.importGroupAnalystsInfo(file);
        } catch (CommonApplicationException ex) {
            logger.error("导入异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    /**
     * 集团分析师预测全量导入功能
     *
     * @param file
     * @return CommonResult
     */
    @Audit(module="groupAnalystsController-fullImportGroupAnalystsInfo",operation="fullImportGroupAnalystsInfo",message="集团分析师预测全量导入功能")
    @JalorOperation(code = "fullImportGroupAnalystsInfo", desc = "集团分析师预测全量导入功能")
    @Override
    public CommonResult fullImportGroupAnalystsInfo(Attachment file) {
        try {
            return iGroupAnalystsService.fullImportGroupAnalystsInfo(file);
        } catch (CommonApplicationException e) {
            logger.error("导入异常：{}", e);
            return CommonResult.failed(ResultCode.CHECK_TIP, "导入失败");
        }
    }

    /**
     * 集团分析师预测模板下载
     *
     * @param response
     * @return CommonResult
     */
    @Audit(module="groupAnalystsController-groupAnalystsTemplateDownload",operation="groupAnalystsTemplateDownload",message="集团分析师预测模板下载")
    @JalorOperation(code = "groupAnalystsTemplateDownload", desc = "集团分析师预测模板下载")
    @Override
    public CommonResult groupAnalystsTemplateDownload(HttpServletResponse response) {
        try {
            iGroupAnalystsService.groupAnalystsTemplateDownload(response);
            return CommonResult.success("下载成功");
        } catch (CommonApplicationException ex) {
            logger.error("下载模板异常：{}", ex);
            return CommonResult.failed(ResultCode.CHECK_TIP, "下载失败");
        }
    }

    /**
     * 集团分析师预测查询任务状态信息
     *
     * @param objectId
     * @return CommonResult
     */
    @JalorOperation(code = "queryDataRefreshStatus", desc = "集团分析师预测查询任务状态信息")
    @Override
    public CommonResult queryDataRefreshStatus(String objectId) {
        return CommonResult.success(iGroupAnalystsService.getOperateRecordLogStatus(objectId));
    }

    /**
     * 集团分析师预测-提交
     *
     * @return CommonResult
     */
    @Audit(module="groupAnalystsController-submit",operation="submit",message="集团分析师预测-提交")
    @JalorOperation(code = "submit", desc = "集团分析师预测-提交")
    @Override
    public CommonResult submit() throws ApplicationException {
        return CommonResult.success(iGroupAnalystsService.submit());
    }
}
