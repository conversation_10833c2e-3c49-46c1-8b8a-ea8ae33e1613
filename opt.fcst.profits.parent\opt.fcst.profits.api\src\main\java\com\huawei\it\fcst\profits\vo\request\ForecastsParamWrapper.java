/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2024. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.request;

import com.huawei.it.fcst.profits.common.utils.CalcUtils;
import com.huawei.it.fcst.profits.common.utils.DateUtil;
import com.huawei.it.fcst.profits.common.utils.biz.DataHelperUtils;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.vo.response.AxisVO;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.huawei.it.fcst.profits.common.utils.CalcUtils.setMonthTabGraph;

public class ForecastsParamWrapper {
    public static String getSplicedPredictionTabGraphValue(ForecastsRequest forecastsRequest) {
        return forecastsRequest.getPredictionType() + "_" + forecastsRequest.getTabGraphType();
    }
    /**
     * 处理预测步长和预测方法所生成的参数构建
     */
    @Getter
    public enum ForceastParamEnum {
        MONTHLY_FORECAST_Y("MONTH_Y", "月度预测-年") {
            @Override
            public ForecastsRequest beforeBuildParam(ForecastsRequest forecastsRequest) {
                monthlyForecast(forecastsRequest);
                forecastsRequest.setActPeriods(DataHelperUtils.getFullHisYear(forecastsRequest.getCurYear(), forecastsRequest.getCurMonth(), ""));
                forecastsRequest.setFcstPeriods(Arrays.asList(String.valueOf(forecastsRequest.getCurYear())));
                forecastsRequest.setDisplayName(forecastsRequest.getFcstPeriods());
                return forecastsRequest;
            }

            @Override
            public Map<String, AxisVO> afterBuildParam(ForecastsRequest forecastsRequest) {
                return monthlyForecastResult(forecastsRequest);
            }
        },
        MONTHLY_FORECAST_H("MONTH_H", "月度预测-半年度") {
            @Override
            public ForecastsRequest beforeBuildParam(ForecastsRequest forecastsRequest) {
                monthlyForecast(forecastsRequest);
                String monthType = "H1"; // 上半年
                if (forecastsRequest.getCurMonth() > 6) {
                    monthType = "H2"; // 下半年
                }
                forecastsRequest.setActStepPeriod((forecastsRequest.getCurYear() - 1) + monthType);
                forecastsRequest.setFcstStepPeriod(forecastsRequest.getCurYear() + monthType);
                forecastsRequest.setActPeriods(DataHelperUtils.getHalfYearHisYear(forecastsRequest.getCurYear(), forecastsRequest.getCurMonth()));
                forecastsRequest.setFcstPeriods(DataHelperUtils.getHalfYearFutureYear(forecastsRequest.getCurYear(), forecastsRequest.getCurMonth()));
                return forecastsRequest;
            }

            @Override
            public Map<String, AxisVO> afterBuildParam(ForecastsRequest forecastsRequest) {
                return monthlyForecastResult(forecastsRequest);
            }
        },
        MONTHLY_FORECAST_Q("MONTH_Q", "月度预测-季度") {
            @Override
            public ForecastsRequest beforeBuildParam(ForecastsRequest forecastsRequest) {
                monthlyForecast(forecastsRequest);
                forecastsRequest.setActPeriods(DataHelperUtils.getFullQn(forecastsRequest));
                forecastsRequest.setFcstPeriods(DataHelperUtils.getFutureQuarter(forecastsRequest));
                forecastsRequest.setDisplayName(forecastsRequest.getFcstPeriods());
                return forecastsRequest;
            }

            @Override
            public Map<String, AxisVO> afterBuildParam(ForecastsRequest forecastsRequest) {
                return monthlyForecastResult(forecastsRequest);
            }
        },
        MONTHLY_FORECAST_M("MONTH_M", "月度预测-月度") {
            @Override
            public ForecastsRequest beforeBuildParam(ForecastsRequest forecastsRequest) {
                monthlyForecast(forecastsRequest);
                List<String> actTargets = new ArrayList<>();
                setMonthTabGraph(forecastsRequest, actTargets);
                List<String> fcstPeriods = new ArrayList<>();
                List<String> displayList = new ArrayList<>();
                for (int k = forecastsRequest.getCurMonth(); k <= 12; k++) {
                    String targetPeriod = forecastsRequest.getCurYear() + StringUtils.leftPad(String.valueOf(k), 2, "0");
                    fcstPeriods.add(targetPeriod);
                }
                forecastsRequest.setActPeriods(actTargets);
                forecastsRequest.setFcstPeriods(fcstPeriods);
                forecastsRequest.setMonthFcstPeriods(fcstPeriods);
                forecastsRequest.setDisplayName(displayList);
                return forecastsRequest;
            }

            @Override
            public Map<String, AxisVO> afterBuildParam(ForecastsRequest forecastsRequest) {
                Map<String, AxisVO> objectMap = new LinkedHashMap<>();
                forecastsRequest.getActPeriods().stream().forEach(item -> {
                    AxisVO axisVO = AxisVO.builder().axisName(item + "ACT").axisDisplayName(item.replace("YTD", "")).attr(item).build();
                    objectMap.put(item + "ACT", axisVO);
                });
                forecastsRequest.getFcstPeriods().stream().forEach(item -> {
                    AxisVO axisVO = AxisVO.builder().axisName(item + "FCST").attr(item).axisDisplayName(item + "/AI auto").build();
                    objectMap.put(item + "FCST", axisVO);
                });
                return objectMap;
            }
        },
        ANNUAL_BUDGET_Y("YEAR_Y", "年度预算-年") {
            @Override
            public ForecastsRequest beforeBuildParam(ForecastsRequest forecastsRequest) {
                annualBudgetDef(forecastsRequest);
                Calendar calendar = DateUtil.getPeriodCalendar(String.valueOf(forecastsRequest.getPeriodId()));
                Calendar systemCruYear = DateUtil.getPeriodCalendar(null);
                int periodYear = calendar.get(Calendar.YEAR);
                int month = calendar.get(Calendar.MONTH) + 1;
                forecastsRequest.setCurMonth(calendar.get(month));
                forecastsRequest.setCurYear(periodYear);
                forecastsRequest.setActPeriods(
                        DataHelperUtils.getFullHisYear(forecastsRequest.getCurYear(), forecastsRequest.getCurMonth(), ""));
                if (periodYear == systemCruYear.get(Calendar.YEAR) && month < 10) {
                    forecastsRequest.setFcstPeriods(Arrays.asList(String.valueOf(forecastsRequest.getCurYear())));
                    forecastsRequest.setAuto2Year(String.valueOf(forecastsRequest.getCurYear()));
                    forecastsRequest.setMaxPeriods(Arrays.asList(String.valueOf(forecastsRequest.getCurYear())));
                } else {
                    forecastsRequest.setFcstPeriods(Arrays.asList(String.valueOf(forecastsRequest.getCurYear()), String.valueOf(forecastsRequest.getCurYear() + 1)));
                    forecastsRequest.setAuto2Year(String.valueOf(forecastsRequest.getCurYear() + 1));
                    forecastsRequest.setMaxPeriods(Arrays.asList(String.valueOf(forecastsRequest.getCurYear() + 1)));
                }
                // 标识年度预算-预测数据查询条件
                forecastsRequest.setSpecAutoTarget(String.valueOf(periodYear));
                return forecastsRequest;
            }

            @Override
            public Map<String, AxisVO> afterBuildParam(ForecastsRequest forecastsRequest) {
                Map<String, AxisVO> objectMap = new LinkedHashMap<>();
                forecastsRequest.getActPeriods().stream().forEach(item -> {
                    AxisVO axisVO = AxisVO.builder().build();
                    axisVO.setAxisName(item + "ACT");
                    axisVO.setAttr(item);
                    if ("当年累计".equals(item)) {
                        axisVO.setAxisDisplayName(forecastsRequest.getCurYear() + "/YTD");
                    } else {
                        axisVO.setAxisDisplayName(item);
                    }
                    objectMap.put(item + "ACT", axisVO);
                });
                if (DateUtil.timeRange(forecastsRequest.getPeriodId())) {
                    String fcstYear = String.valueOf(forecastsRequest.getCurYear());
                    AxisVO fcst = AxisVO.builder().axisName(fcstYear + "FCST").axisDisplayName(fcstYear + "/AI预测").attr(fcstYear).build();
                    objectMap.put(fcstYear + "FCST", fcst);
                    AxisVO budget = AxisVO.builder().axisName(fcstYear + "BUDGET").axisDisplayName(fcstYear + "/AI预算").attr(fcstYear).build();
                    objectMap.put(forecastsRequest.getCurYear() + "BUDGET", budget);
                } else {
                    String fcstYear = String.valueOf(forecastsRequest.getCurYear());
                    AxisVO fcst = AxisVO.builder().axisName(fcstYear + "FCST").axisDisplayName(fcstYear + "/AI预测").attr(fcstYear).build();
                    objectMap.put(forecastsRequest.getCurYear() + "FCST", fcst);
                    String bud = String.valueOf(forecastsRequest.getCurYear() + 1);
                    AxisVO budget = AxisVO.builder().axisName(bud + "BUDGET").axisDisplayName(bud + "/AI预算").attr(bud).build();
                    objectMap.put(bud + "BUDGET", budget);
                }
                return objectMap;
            }
        },
        ANNUAL_BUDGET_M("YEAR_M", "年度预算-月") {
            @Override
            public ForecastsRequest beforeBuildParam(ForecastsRequest yearMonth) {
                final List<String> MONTH_DATE = Stream.of("01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12").collect(Collectors.toList());
                annualBudgetDef(yearMonth);
                Calendar calendar = DateUtil.getPeriodCalendar(String.valueOf(yearMonth.getPeriodId()));
                Calendar systemCruYear = DateUtil.getPeriodCalendar(null);
                int periodYear = calendar.get(Calendar.YEAR);
                int month = calendar.get(Calendar.MONTH) + 1;
                yearMonth.setCurYear(periodYear);
                yearMonth.setCurMonth(month);
                List<String> actTargets = new ArrayList<>();
                setMonthTabGraph(yearMonth, actTargets);
                List<String> fcstPeriods = new ArrayList<>();
                // 处理预测月份生成，例如：会计期202401
                if (periodYear == systemCruYear.get(Calendar.YEAR) && month < 10) {
                    fcstPeriods.addAll(MONTH_DATE.stream().map(item -> yearMonth.getCurYear() + item).collect(Collectors.toSet()));
                } else {
                    // 预测年+1
                    int fastYear = yearMonth.getCurYear() + 1;
                    for (int i = fastYear; i >= fastYear - 1; i--) {
                        int finalI = i;
                        if (finalI == yearMonth.getCurYear()) {
                            fcstPeriods.addAll(MONTH_DATE.stream().filter(item -> Integer.valueOf(item) >= yearMonth.getCurMonth()).map(item -> finalI + item).collect(Collectors.toSet()));
                        } else {
                            fcstPeriods.addAll(MONTH_DATE.stream().map(item -> finalI + item).collect(Collectors.toSet()));
                        }
                    }
                }
                List<String> collect = fcstPeriods.stream().sorted().collect(Collectors.toList());
                yearMonth.setActPeriods(actTargets);
                yearMonth.setFcstPeriods(collect);
                yearMonth.setMonthFcstPeriods(collect);
                return yearMonth;
            }

            @Override
            public Map<String, AxisVO> afterBuildParam(ForecastsRequest forecastsRequest) {
                Map<String, AxisVO> objectMap = new LinkedHashMap<>();
                forecastsRequest.getActPeriods().stream().forEach(item -> {
                    AxisVO axisVO = AxisVO.builder().axisName(item + "ACT").attr(item).axisDisplayName(item.replace("YTD", "")).build();
                    objectMap.put(item + "ACT", axisVO);
                });
                forecastsRequest.getFcstPeriods().stream().forEach(item -> {
                    AxisVO axisVO = AxisVO.builder().axisName(item + "BUDGET").attr(item).axisDisplayName(item + "/AI auto").build();
                    objectMap.put(item + "BUDGET", axisVO);
                });
                return objectMap;
            }
        };
        private String code;

        private String desc;

        ForceastParamEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public abstract ForecastsRequest beforeBuildParam(ForecastsRequest forecastsRequest);


        public abstract Map<String, AxisVO> afterBuildParam(ForecastsRequest forecastsRequest);

        /**
         * 默认方法
         * @param forecastsRequest
         * @return
         */
        public ForecastsRequest monthlyForecast(ForecastsRequest forecastsRequest) {
            // 转换前段传值预测步长方法映射值
            CalcUtils.setFcstTypeValue(forecastsRequest);
            int year = TimeUtils.getCurYear(forecastsRequest.getPeriodId());
            int month = TimeUtils.getCurMonth(forecastsRequest.getPeriodId());
            forecastsRequest.setCurYear(year);
            forecastsRequest.setCurMonth(month);
            forecastsRequest.setIncludeFlag("1");
            forecastsRequest.setMaxPeriods(Arrays.asList(String.valueOf(year)));
            forecastsRequest.setDataType("FCST");
            return forecastsRequest;
        }

        /**
         * 默认方法
         *
         * @param forecastsRequest
         * @return ForecastsRequest
         */
        public ForecastsRequest annualBudgetDef(ForecastsRequest forecastsRequest) {
            // 转换前段传值预测步长方法映射值
            CalcUtils.setFcstTypeValue(forecastsRequest);
            forecastsRequest.setIncludeFlag("1");
            return forecastsRequest;
        }

        /**
         * getByCode
         *
         * @param code code
         * @return StepTypeEnum
         */
        public static ForceastParamEnum getByCode(String code) {
            for (ForceastParamEnum value : ForceastParamEnum.values()) {
                if (value.getCode().equalsIgnoreCase(code)) {
                    return value;
                }
            }
            return null;
        }

        public static Map<String, AxisVO> monthlyForecastResult(ForecastsRequest forecastsRequest) {
            Map<String, AxisVO> objectMap = new LinkedHashMap<>();
            forecastsRequest.getActPeriods().stream().forEach(item -> {
                AxisVO axisVO = AxisVO.builder().axisName(item + "ACT").attr(item).build();
                if ("当年累计".equals(item)) {
                    axisVO.setAxisDisplayName(forecastsRequest.getCurYear() + "/YTD");
                } else {
                    axisVO.setAxisDisplayName(item.replaceAll("Q", "/Q").replaceAll("H", "/H"));
                }
                objectMap.put(item + "ACT", axisVO);
            });
            forecastsRequest.getFcstPeriods().stream().forEach(item -> {
                AxisVO axisVO = AxisVO.builder().axisName(item + "FCST").attr(item).axisDisplayName(item + "/AI auto").build();
                objectMap.put(item + "FCST", axisVO);
            });
            return objectMap;
        }
    }
}
