/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The Entity of GroupL1L2VO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-15 16:00:24
 */
@Getter
@Setter
public class GroupL1L2VO {

    private String targetPeriod;

    private Long period;

    private String bgCode;

    private String l1Name;

    private String l2Name;

    private String lv1Code;

    private String lv1Name;

    private String lv2Name;

    private String lv2Code;

    private BigDecimal mgpRateBefore;

    private BigDecimal revPercent;

    private BigDecimal carryoverAmount;

    private BigDecimal shipQty;

    private String key;
}
