/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.utils;

import com.huawei.it.jalor5.core.base.BaseResourceVO;
import com.huawei.it.jalor5.core.request.IUserPrincipal;
import com.huawei.it.jalor5.core.request.RequestContextException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * UserInfo
 *
 * <AUTHOR>
 * @since 2020 -7-15
 */
@Component
public class UserInfo {
    /**
     * getCurrentEmployeeNo
     *
     * @return String current employee no
     */
    public static String getCurrentEmployeeNo() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getEmployeeNumber();
        }
        throw new RequestContextException();
    }

    /**
     * getUserId
     *
     * @return Long user id
     */
    public static Long getUserId() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getUserId();
        }
        throw new RequestContextException();
    }

    /**
     * getUserId
     *
     * @return Long user id
     */
    public static UserVO getUser() {
        UserVO user = (UserVO) RequestContext.getCurrent().getUser();
        if (user != null) {
            return user;
        }
        throw new RequestContextException();
    }

    /**
     * getUserCN
     *
     * @return String user cn
     */
    public static String getUserCN() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getUserCN();
        }
        throw new RequestContextException();
    }

    /**
     * getUserAccount
     *
     * @return String user account
     */
    public static String getUserAccount() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getUserAccount();
        }
        throw new RequestContextException();
    }

    /**
     * Gets user email.
     *
     * @return the user email
     */
    public static String getUserEmail() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getEmail();
        }
        throw new RequestContextException();
    }

    /**
     * Add owner info.
     *
     * @param <T>    the type parameter
     * @param object the object
     */
    public static <T extends BaseResourceVO> void addOwnerInfo(T object) {
        Long userId = UserInfo.getUserId();
        String userCn = UserInfo.getUserCN();
        object.setCreatedBy(userId);
        object.setCreationUserCN(userCn);
        object.setLastUpdatedBy(userId);
        object.setLastUpdateUserCN(userCn);
    }

    /**
     * Get w 3 name string.
     *
     * @param userCn the user cn
     * @return the string
     */
    public static String getW3Name(String userCn) {
        String userName = null;
        if (StringUtils.isNotEmpty(userCn)) {
            String[] userSplit = userCn.split(" ");
            if (userSplit.length > 1) {
                userName = userSplit[0].charAt(0) + userSplit[1];
            }
        }
        return userName;
    }
}
