/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * BgEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum RoleEnum {

    ADMIN("profits_admin", "盈利管理员"),
    BG_ANALYST("Profits_BG_Analyst", "BG分析师"),
    GROUP_ANALYST_GENERAL("Profits_Group_Analyst_ General", "集团分析师（普通）"),
    INDUSTRY_ANALYST("Profits_Industry_Analyst", "产业分析师"),
    GROUP_ANALYST_ADMIN("Profits_Group_Analyst_Admin", "集团分析师（配置管理）");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    RoleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * getByCode
     *
     * @param code code
     * @return StepTypeEnum
     */
    public static RoleEnum getByCode(String code) {
        for (RoleEnum value : RoleEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

