/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.config.impl;

import com.huawei.it.fcst.profits.comm.AbstractConfigDataExporter;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.poi.ExcelUtilPro;
import com.huawei.it.fcst.profits.common.poi.PoiEnum;
import com.huawei.it.fcst.profits.common.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.utils.HeaderUtils;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.StreamUtil;

import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

@Component
public class SpartInfoExporter extends AbstractConfigDataExporter {

    private static final Logger logger = LoggerFactory.getLogger(SpartInfoExporter.class);
    @Autowired
    private ExcelUtilPro excelUtilPro;

    @Override
    public void resetDataList(List resultResult, List<Map<String, Object>> infoList) {
        // 增加步长颗粒度
        infoList.stream().forEach(vo -> {
            if (vo.containsKey("targetPeriod")) {
                vo.put("periodId", String.valueOf(vo.get("targetPeriod")));
                vo.put("targetPeriod", CommUtils.getPredictingType(String.valueOf(vo.get("targetPeriod"))));
            }
            if (vo.containsKey("bgName")) {
                vo.put("bgName", CommUtils.getBgEnglishName(String.valueOf(vo.get("bgName"))));
            }
        });
    }

    public void setSpartInfoMap(Map<String, Object> params, Set<String> selectedTitles,
        List<AbstractExcelTitleVO> excelTitleVOS, List<?> list, String sheetName) {
        params.put("objs", list);
        params.put("selTitles", selectedTitles);
        params.put("excelTitleVOS", excelTitleVOS);
        params.put("sheetName", sheetName);
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = new ArrayList<>();
        int titleRowCount = excelUtilPro.adjustTitleVoList(excelTitleVOS, selectedTitles, selectedLeafExcelTitleVO);
        params.put("titleRowCount", titleRowCount);
        params.put("selectedLeafExcelTitleVO", selectedLeafExcelTitleVO);
    }

    @Override
    public void exportData(Map<String, Object> paramsMap, List<?> resultList, HttpServletResponse response)
        throws CommonApplicationException {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        try {
            int sheetNum = 0;
            // 创建导出的表头
            List<AbstractExcelTitleVO> l1TitleList = new ArrayList<>();
            Set<String> l1TitleNames = new LinkedHashSet<>();
            // 构建导出Excel的表头信息
            HeaderUtils.buildHeader(l1TitleList, l1TitleNames, HeaderUtils.buildHeader(HeaderUtils.SPART_INFO_L1_HEADER));
            // paramsMap 重新赋值
            workbook.createSheet(CommonConstant.SPART_INFO_SHEET_L1_NAME);
            workbook.setSheetOrder(CommonConstant.SPART_INFO_SHEET_L1_NAME, sheetNum);
            // 补充导出数据需要的相关数据
            List l1DataList1 = (List) paramsMap.get("l1Info");
            resetDataList(null, l1DataList1);
            setSpartInfoMap(paramsMap, l1TitleNames, l1TitleList, l1DataList1, CommonConstant.SPART_INFO_SHEET_L1_NAME);
            PoiEnum.exportExcel(workbook, sheetNum, paramsMap);
            // 若无不输出L2相关信息
            if (paramsMap.containsKey("l2Info")) {
                // 保持正确的sheet顺序数
                sheetNum++;
                Set<String> l2TitleNames = new LinkedHashSet<>();
                List<AbstractExcelTitleVO> l2TitleList = new ArrayList<>();
                HeaderUtils.buildHeader(l2TitleList, l2TitleNames,
                    HeaderUtils.buildHeader(HeaderUtils.SPART_INFO_L2_HEADER));
                workbook.createSheet(CommonConstant.SPART_INFO_SHEET_L2_NAME);
                workbook.setSheetOrder(CommonConstant.SPART_INFO_SHEET_L2_NAME, sheetNum);
                List l2DataList = (List) paramsMap.get("l2Info");
                resetDataList(null, l2DataList);
                // paramsMap 重新赋值
                setSpartInfoMap(paramsMap, l2TitleNames, l2TitleList, l2DataList, CommonConstant.SPART_INFO_SHEET_L2_NAME);
                PoiEnum.exportExcel(workbook, sheetNum, paramsMap);
            }
            // 若无不输出L2相关信息
            if (paramsMap.containsKey("l3Info")) {
                // 保持正确的sheet顺序数
                sheetNum++;
                Set<String> l3TitleNames = new LinkedHashSet<>();
                List<AbstractExcelTitleVO> l3TitleList = new ArrayList<>();
                HeaderUtils.buildHeader(l3TitleList, l3TitleNames,
                    HeaderUtils.buildHeader(HeaderUtils.SPART_INFO_L3_HEADER));
                workbook.createSheet(CommonConstant.SPART_INFO_SHEET_L3_NAME);
                workbook.setSheetOrder(CommonConstant.SPART_INFO_SHEET_L3_NAME, sheetNum);
                List l3DataList = (List) paramsMap.get("l3Info");
                resetDataList(null, l3DataList);
                // paramsMap 重新赋值
                setSpartInfoMap(paramsMap, l3TitleNames, l3TitleList, l3DataList,
                    CommonConstant.SPART_INFO_SHEET_L3_NAME);
                PoiEnum.exportExcel(workbook, sheetNum, paramsMap);
            }
            // 上传文件以及关闭相关流
            excelUtil.downloadAndUploadS3Excel(response, workbook, paramsMap);
            if (workbook != null) {
                workbook.dispose();
            }
        } catch (CommonApplicationException ex) {
            logger.error(CommonConstant.STREAM_IN_CLOSE_FAILLED);
        } finally {
            StreamUtil.closeStreams(workbook);
        }
    }
}
