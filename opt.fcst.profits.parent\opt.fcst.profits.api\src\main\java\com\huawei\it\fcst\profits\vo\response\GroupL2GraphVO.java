/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * The Entity of GroupL2GraphVO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-15 16:00:24
 */
@Getter
@Setter
public class GroupL2GraphVO {

    private Map<String, LinkedList<KrCpfL2Response>> axisData = new LinkedHashMap<>();

    private List<String> l2Names;

    private int curYear;

    private LinkedList<AxisVO> axisHeader = new LinkedList<>();
}
