/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.huawei.it.fcst.profits.common.utils.FcstJsonFormatVisitorWrapper;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The Entity of ProfitGranularityL2InfoVo
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:26:09
 */
@Getter
@Setter
public class ProfitGranularitySpartInfoVo {

    /**
     * 重量级团队LV1编码
     **/
    private String lv1Code;

    /**
     * L1名称
     **/
    private String l1Name;

    /**
     * L2名称
     **/
    private String l2Name;

    /**
     * BG编码
     **/
    private String bgCode;


    /**
     * 重量级团队LV1描述
     **/
    private String lv1Name;

    /**
     * BG名称
     **/
    private String bgName;

    /**
     * 币种
     **/
    private String currency;

    /**
     * 场景
     **/
    private String targetPeriod;

    /**
     * 会计期
     **/
    private Long periodId;

    /**
     * 重量级团队LV2编码
     **/

    private String lv2Code;

    /**
     * 重量级团队LV2名称
     **/
    private String lv2Name;

    /**
     * Spart代码
     */
    private String spartCode;

    /**
     *  Spart描述
     */
    private String spartDesc;

    /**
     * 单位价格
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal avgPrice;

    /**
     * 单位成本
     **/
    private String avgCost;

    /**
     * 收入占比
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal revPercent;

    /**
     * 成本占比
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal costPercent;

    /**
     *  解密key
     */
    private String key;

    /**
     *  地区
     */
    private String overseaDesc;
}
