/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
public class LabelConfigVo {
    /**
     * 备注
     */
    private String remark;

    private String lv1Name;

    /**
     * 创建时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp creationDate;

    /**
     * 创建人
     **/
    private String createdBy;

    private String createdByName;

    /**
     * 最后更新时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp lastUpdateDate;

    /**
     * 最后更新人
     **/
    private String lastUpdatedBy;

    private String lastUpdatedByName;

    /**
     * 是否删除
     **/
    private String delFlag;

    private String errorMsg;

    private String status;
}
