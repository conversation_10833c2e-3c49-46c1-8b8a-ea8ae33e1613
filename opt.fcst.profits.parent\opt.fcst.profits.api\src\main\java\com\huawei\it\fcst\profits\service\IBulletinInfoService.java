/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.BulletinInfoVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

/**
 * IBulletinInfoService
 *
 */
public interface IBulletinInfoService {

    /**
     * 获取当前用户公告相关信息
     *
     * @param bulletinInfoVO
     * @return
     * @throws CommonApplicationException
     */
    String getStatusByUserId(BulletinInfoVO bulletinInfoVO) throws CommonApplicationException;

    /**
     * 保存当前用户的公告阅读状态
     *
     * @param request
     * @return
     */
    CommonResult saveStatus(BulletinInfoVO request) throws CommonApplicationException;

}
