/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */
 
package com.huawei.it.fcst.profits.service.usercenter.impl;

import com.huawei.it.fcst.profits.dao.IDimensionAssociatDao;
import com.huawei.it.fcst.profits.dao.IDmFopRecordDao;
import com.huawei.it.fcst.profits.service.usercenter.impl.FopRecordService;
import com.huawei.it.fcst.profits.vo.DmFopRecordVO;
import com.huawei.it.fcst.profits.vo.DmPfDimensionAssociatVO;
import com.huawei.it.fcst.profits.vo.request.FopRecordRequest;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
 
/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年02月23日
 */
class FopRecordServiceTest {
    @InjectMocks
    private FopRecordService fopRecordService;
 
    @Mock
    private IDmFopRecordDao iDmFopRecordDao;
 
    @Mock
    private IDimensionAssociatDao iDimensionAssociatDao;
 
    @BeforeEach
    public void before() {
        MockitoAnnotations.initMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }
 
    @Test
    void findByPageTest() throws CommonApplicationException {
        FopRecordRequest request  = new FopRecordRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        request.setTimeInterval("ALL");
        request.setPageModule("ALL");
        PagedResult<DmFopRecordVO> test = new PagedResult<>();
        DmFopRecordVO dmFopRecordVO = new DmFopRecordVO();
        dmFopRecordVO.setOptType("TTT");
        test.setResult(Arrays.asList(dmFopRecordVO));
        when(iDmFopRecordDao.findByPage(any(), any())).thenReturn(test);
        PagedResult<DmFopRecordVO> result = fopRecordService.findByPage(request);
        Assertions.assertEquals("TTT", result.getResult().get(0).getOptType());
    }



    @Test
    void findByPageIsTimeIntervalNullTest() throws CommonApplicationException {
        FopRecordRequest request  = new FopRecordRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        request.setTimeInterval(null);
        request.setPageModule("ALL-test");
        PagedResult<DmFopRecordVO> test = new PagedResult<>();
        DmFopRecordVO dmFopRecordVO = new DmFopRecordVO();
        dmFopRecordVO.setOptType("TTT");
        test.setResult(Arrays.asList(dmFopRecordVO));
        when(iDmFopRecordDao.findByPage(any(), any())).thenReturn(test);
        PagedResult<DmFopRecordVO> result = fopRecordService.findByPage(request);
        Assertions.assertEquals("TTT", result.getResult().get(0).getOptType());
    }

    @Test
    void findByPageIsModuleNullTest() throws CommonApplicationException {
        FopRecordRequest request  = new FopRecordRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        request.setTimeInterval(null);
        request.setPageModule(null);
        PagedResult<DmFopRecordVO> test = new PagedResult<>();
        DmFopRecordVO dmFopRecordVO = new DmFopRecordVO();
        dmFopRecordVO.setOptType("TTT");
        test.setResult(Arrays.asList(dmFopRecordVO));
        when(iDmFopRecordDao.findByPage(any(), any())).thenReturn(test);
        PagedResult<DmFopRecordVO> result = fopRecordService.findByPage(request);
        Assertions.assertEquals("TTT", result.getResult().get(0).getOptType());
    }
 
    @Test
    void findByPageTest1() throws CommonApplicationException {
        FopRecordRequest request  = new FopRecordRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        request.setTimeInterval("202203");
        request.setPageModule("ALL");
        PagedResult<DmFopRecordVO> test = new PagedResult<>();
        DmFopRecordVO dmFopRecordVO = new DmFopRecordVO();
        dmFopRecordVO.setOptType("TTT");
        test.setResult(Arrays.asList(dmFopRecordVO));
        when(iDmFopRecordDao.findByPage(any(), any())).thenReturn(test);
        PagedResult<DmFopRecordVO> result = fopRecordService.findByPage(request);
        Assertions.assertEquals("TTT", result.getResult().get(0).getOptType());
    }
 
    @Test
    void selectLv1() throws ApplicationException {
        PagedResult<DmPfDimensionAssociatVO> pagedResult = new PagedResult<>();
        DmPfDimensionAssociatVO dmPfDimensionAssociatVO = new DmPfDimensionAssociatVO();
        dmPfDimensionAssociatVO.setBgCode("CNBG");
        pagedResult.setResult(Arrays.asList(dmPfDimensionAssociatVO));
        when(iDimensionAssociatDao.findLv1ByPage(any(),any(),any())).thenReturn(pagedResult);
        PagedResult<DmPfDimensionAssociatVO> result = fopRecordService.selectLv1(any(),any(),any());
        Assertions.assertEquals("CNBG",result.getResult().get(0).getBgCode());
    }
 
    @Test
    void selectLv2() throws ApplicationException {
        PagedResult<DmPfDimensionAssociatVO> pagedResult = new PagedResult<>();
        DmPfDimensionAssociatVO dmPfDimensionAssociatVO = new DmPfDimensionAssociatVO();
        dmPfDimensionAssociatVO.setBgCode("CNBG");
        pagedResult.setResult(Arrays.asList(dmPfDimensionAssociatVO));
        when(iDimensionAssociatDao.findLv2ByPage(any(),any(),any())).thenReturn(pagedResult);
        PagedResult<DmPfDimensionAssociatVO> result = fopRecordService.selectLv2(any(),any(),any());
        Assertions.assertEquals("CNBG",result.getResult().get(0).getBgCode());
    }
 
    @Test
    void selectBg() throws ApplicationException {
        PagedResult<DmPfDimensionAssociatVO> pagedResult = new PagedResult<>();
        DmPfDimensionAssociatVO dmPfDimensionAssociatVO = new DmPfDimensionAssociatVO();
        dmPfDimensionAssociatVO.setBgCode("CNBG");
        pagedResult.setResult(Arrays.asList(dmPfDimensionAssociatVO));
        when(iDimensionAssociatDao.findBgByPage(any(),any(),any())).thenReturn(pagedResult);
        PagedResult<DmPfDimensionAssociatVO> result = fopRecordService.selectBg(any(),any(),any());
        Assertions.assertEquals("CNBG",result.getResult().get(0).getBgCode());
    }
}