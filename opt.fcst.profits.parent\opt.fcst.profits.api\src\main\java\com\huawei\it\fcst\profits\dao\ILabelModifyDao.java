/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.dao;

import com.huawei.it.fcst.profits.vo.LabelCountVO;
import com.huawei.it.fcst.profits.vo.LabelModifyStaticsVO;
import com.huawei.it.fcst.profits.vo.LabelModifyVO;
import com.huawei.it.fcst.profits.vo.request.LabelModifyRequest;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * The DAO to access FcstLabelModifyVO entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-17 19:38:39
 */
public interface ILabelModifyDao {
    /**
     * Find by page FcstLabelModifyVO records.
     *
     * @param vo          is vo
     * @param paramPageVO is param
     * @return FcstLabelModifyVO
     */
    PagedResult<LabelModifyVO> findByPage(LabelModifyRequest vo, PageVO paramPageVO);

    /**
     * Find FcstLabelModifyVO by Year.
     *
     * @return list
     */
    List<LabelModifyVO> findDataByParam();

    /**
     * Insert a new FcstLabelModifyVO record.
     *
     * @param modifyList is modifyList
     * @return int
     */
    int createList(List<LabelModifyVO> modifyList);

    /**
     * updateList
     *
     * @param modifyList modifyList
     * @return int
     */
    int updateList(@Param("lastUpdatedBy") String lastUpdatedBy, @Param("lastUpdateDate") Timestamp lastUpdateDate, @Param("modifyList") List<LabelModifyVO> modifyList) throws CommonApplicationException;


    List<LabelCountVO> groupDataByParam(@Param("periodIds") List<String> periodIds);


    /**
     * Find FcstLabelModifyVO by Year.
     * <p>
     * * @return list
     */
    List<String> findStaticDataByParam();

    /**
     * Insert a new FcstLabelModifyVO record.
     *
     * @param modifyList is modifyList
     * @return int
     */
    int createStaticList(List<LabelModifyStaticsVO> modifyList);


    /**
     * updateList
     *
     * @param lastUpdatedBy modifyList
     * @return int
     */
    int updateStaticList(@Param("lastUpdatedBy") String lastUpdatedBy);


}
