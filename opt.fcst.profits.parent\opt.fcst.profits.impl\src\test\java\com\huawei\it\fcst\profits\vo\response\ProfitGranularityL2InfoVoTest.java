/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.huawei.it.fcst.profits.vo.BaseVOCoverUtilsTest;

class ProfitGranularityL2InfoVoTest extends BaseVOCoverUtilsTest<ProfitGranularityL2InfoVo> {
    @Override
    protected Class<ProfitGranularityL2InfoVo> getTClass() {
        return ProfitGranularityL2InfoVo.class;
    }
}