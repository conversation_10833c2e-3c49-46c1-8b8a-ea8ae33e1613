/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The Entity of SpartInfoVo
 *
 * <AUTHOR>
 * @since 2022-10-18 09:26:09
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SpartInfoRequest {

    /**
     * spartCode
     **/
    private String spartCode;

    /**
     * spartDesc
     **/
    private String spartDesc;

}
