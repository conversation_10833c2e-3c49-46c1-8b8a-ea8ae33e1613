/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * PeriodTypeEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum PeriodTypeEnum {
    LAST_YEAR_ACTURE("lastYearActure", "去年实际"),
    CUR_YEAR_SUM("curYearSum", "当年累计"),
    LAST_YEAR_PERIOD("lastYearPeriod", "去年同期"),
    AI_AUTO("aiAuto", "AI AUTO");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    PeriodTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * getByCode
     *
     * @param code code
     * @return StepTypeEnum
     */
    public static PeriodTypeEnum getByCode(String code) {
        for (PeriodTypeEnum value : PeriodTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

