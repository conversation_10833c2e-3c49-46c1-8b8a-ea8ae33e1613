/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.predict.impl;

import cn.hutool.core.date.format.FastDateFormat;
import com.huawei.it.fcst.profits.comm.AbstractService;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.BgEnum;
import com.huawei.it.fcst.profits.common.enums.BoolEnum;
import com.huawei.it.fcst.profits.common.enums.ModuleEnum;
import com.huawei.it.fcst.profits.common.enums.OptTypeEnum;
import com.huawei.it.fcst.profits.common.enums.PredictionEnum;
import com.huawei.it.fcst.profits.common.enums.RecStsEnum;
import com.huawei.it.fcst.profits.common.enums.RoleEnum;
import com.huawei.it.fcst.profits.common.enums.SaveTypeEnum;
import com.huawei.it.fcst.profits.common.utils.CalcUtils;
import com.huawei.it.fcst.profits.common.utils.DateUtil;
import com.huawei.it.fcst.profits.common.utils.FcstGlobalParameterUtil;
import com.huawei.it.fcst.profits.common.utils.UserHandle;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.common.vo.TableHeaderVo;
import com.huawei.it.fcst.profits.service.IExportService;
import com.huawei.it.fcst.profits.service.IKrCpfLv1AggrService;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.utils.HeaderUtils;
import com.huawei.it.fcst.profits.vo.ForecastsMethodVO;
import com.huawei.it.fcst.profits.vo.ForecastsStepVO;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.ConfDefRequest;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse;
import com.huawei.it.fcst.profits.vo.response.QueryVersionResponse;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class KrCpfLv1AggrService extends AbstractService implements IKrCpfLv1AggrService {

    private static final Logger logger = LoggerFactory.getLogger(KrCpfLv1AggrService.class);

    private static final String ANNUAL_BUDGET = "App.Config.Profits.AnnualBudget";

    @Autowired
    private IExportService iExportService;

    @Autowired
    private ILookupItemQueryService iLookupItemQueryService;

    /**
     * 获取产品信息
     *
     * @return list
     * @throws CommonApplicationException
     */
    @Override
    public List<KrCpfLv1AggrResponse> getProductLine(ForecastsRequest forecastsRequest) {
        forecastsRequest.buildFcstStepQueryParam();
        // 年度预算值AI预算
        if (StringUtils.equals(forecastsRequest.getPredictionType(), PredictionEnum.YEAR.getCode())) {
            forecastsRequest.setOrderTargetPeriodId(String.valueOf(forecastsRequest.getCurYear() + 1));
        } else {
            // 设置月度预测（aiot -会计期）
            forecastsRequest.setOrderTargetPeriodId(String.valueOf(forecastsRequest.getCurYear()));
        }
        return iKrCpfLv1AggrDao.getLv1List(forecastsRequest);
    }


    /**
     * 获取BG信息
     *
     * @return list
     * @throws CommonApplicationException
     */
    @Override
    public List<ConfDefRequest> getBgInfo(List<String> bgList) {
        List<ConfDefRequest> resultList = new ArrayList<>();
        bgList.forEach(bg -> buildBgList(resultList, bg));
        Collections.sort(resultList);
        return resultList;
    }

    /**
     * 获取产品信息
     *
     * @return list
     * @throws CommonApplicationException
     */
    @Override
    public List<KrCpfLv1AggrResponse> getSopInfo(ForecastsRequest forecastsRequest) {
        List<KrCpfLv1AggrResponse> resultList = new ArrayList<>();
        List<String> sopInfos = iKrCpfLv1AggrDao.getSopInfo(forecastsRequest);
        if (CollectionUtils.isEmpty(sopInfos)) {
            return resultList;
        }
        sopInfos.forEach(sop -> {
            if (StringUtils.equals(forecastsRequest.getPredictionType(), PredictionEnum.YEAR.getCode()) && sop.indexOf("-") >= 0
                    || StringUtils.equals(forecastsRequest.getPredictionType(), PredictionEnum.MONTH.getCode()) && sop.indexOf("-") < 0) {
                KrCpfLv1AggrResponse lv1 = new KrCpfLv1AggrResponse();
                lv1.setPhaseDate(sop);
                resultList.add(lv1);
            }
        });
        return resultList;
    }

    /**
     * 构建不同BG
     *
     * @param resultList
     * @param bg
     */
    public void buildBgList(List<ConfDefRequest> resultList, String bg) {
        ConfDefRequest confDefRequest = new ConfDefRequest();
        if (StringUtils.equals(BgEnum.GROUP.getCode(), bg)) {
            confDefRequest.setBgCode(BgEnum.GROUP.getCode());
            confDefRequest.setBgName(BgEnum.GROUP.getDesc());
            resultList.add(confDefRequest);
        }
        if (StringUtils.equals(BgEnum.CNBG.getCode(), bg)) {
            confDefRequest.setBgCode(BgEnum.CNBG.getCode());
            confDefRequest.setBgName(BgEnum.CNBG.getDesc());
            resultList.add(confDefRequest);
        }
        if (StringUtils.equals(BgEnum.EBG.getCode(), bg)) {
            confDefRequest.setBgCode(BgEnum.EBG.getCode());
            confDefRequest.setBgName(BgEnum.EBG.getDesc());
            resultList.add(confDefRequest);
        }
    }

    /**
     * 分页获取LV1数据信息
     *
     * @param forecastsRequest 预测请求
     * @return PagedResult分页响应
     */
    @Override
    public PagedResult<KrCpfLv1AggrResponse> findDataByPage(ForecastsRequest forecastsRequest) throws
            CommonApplicationException {
        PagedResult<KrCpfLv1AggrResponse> pagedResult = null;
        try {
            if (checkLv1Result(forecastsRequest)) {
                return new PagedResult<>();
            }
            PageVO pageVO = new PageVO();
            pageVO.setCurPage(forecastsRequest.getPageIndex());
            pageVO.setPageSize(forecastsRequest.getPageSize());
            pagedResult = iKrCpfLv1AggrDao.findLevelByPage(forecastsRequest, pageVO);
            List<KrCpfLv1AggrResponse> resultLv1List = new ArrayList();
            if (CollectionUtils.isEmpty(pagedResult.getResult())) {
                pagedResult.setResult(resultLv1List);
                return pagedResult;
            }
            forecastsRequest.setLevel("01");
            AtomicReference<Map<String, List<KrCpfLv1AggrResponse>>> referenceVal = new AtomicReference<>();
            referenceVal.set(pagedResult.getResult().stream().collect(Collectors.groupingBy(result -> buildTempKey(result, null, null))));
            if (StringUtils.equals(forecastsRequest.getPredictionType(), PredictionEnum.MONTH.getCode())) {
                montyFetchDataByLevel(referenceVal, resultLv1List, forecastsRequest);
            }else {
                fetchDataByLevel(referenceVal, resultLv1List, forecastsRequest);
            }
            pagedResult.setResult(resultLv1List);
        } catch (Exception e) {
            logger.error("查询列表数据异常: {0}", e);
            throw new CommonApplicationException("查询列表数据异常");
        }
        return pagedResult;
    }

    public void resortLv1(ForecastsRequest forecastsRequest, List<KrCpfLv1AggrResponse> resultLv1List, List<String> recordOrders) {
        checkBgcodeBeforeQuery(forecastsRequest);
        if (CollectionUtils.isEmpty(recordOrders)) {
            return;
        }
        Set<String> linkedSet = new LinkedHashSet<>(recordOrders); // 排序列表
        List<KrCpfLv1AggrResponse> finalResultList = new ArrayList<>();
        // 注释：可能会出现findFirst失败 linkedSet.forEach(lv1 -> finalResultList.add(resultLv1List.stream().filter(data -> StringUtils.equals(lv1,data.getLv1Code())).findFirst().get()));
        linkedSet.forEach(lv1 -> {
            KrCpfLv1AggrResponse krCpfLv1AggrResponse = Optional.ofNullable(resultLv1List.stream().filter(data -> StringUtils.equals(lv1, data.getLv1Code()))).get().findFirst().orElse(null);
            if (krCpfLv1AggrResponse != null) {
                finalResultList.add(krCpfLv1AggrResponse);
            }
        });
        List<KrCpfLv1AggrResponse> finalOtherList = resultLv1List.stream().filter(data -> !linkedSet.contains(data.getLv1Code())).collect(Collectors.toList());
        resultLv1List.clear();
        finalResultList.addAll(finalOtherList);
        List<KrCpfLv1AggrResponse> resultList = finalResultList.stream()
                .skip((forecastsRequest.getPageIndex() - 1) * forecastsRequest.getPageSize())
                .limit(forecastsRequest.getPageSize())
                .collect(Collectors.toList());
        resultLv1List.addAll(resultList);
    }

    public void checkBgcodeBeforeQuery(ForecastsRequest forecastsRequest) {
        // 当出现两个以前的BgCode时，排序会造成混乱。原因：CNBG与EBG有排序字段重叠，当指定其中的重叠的字段会是排序混乱
        if (forecastsRequest.getBgCode().length >= 2) {
            // 解决方案，根据前端传入指定字段判别查询的BgCode段
            List<String> bgcodes = new ArrayList<>();
            if (forecastsRequest.getOrderField().startsWith("cnbg")) {
                bgcodes.add(BgEnum.CNBG.getCode());
            } else if (forecastsRequest.getOrderField().startsWith("ebg")) {
                bgcodes.add(BgEnum.EBG.getCode());
            }
            forecastsRequest.setBgCodes(bgcodes);
        }
    }

    public void fetchDataByLevel
            (AtomicReference<Map<String, List<KrCpfLv1AggrResponse>>> referenceVal, List<KrCpfLv1AggrResponse> resultLv1List, ForecastsRequest
                    forecastsRequest) throws CommonApplicationException {
        Map<String, List<KrCpfLv1AggrResponse>> lv1Group = groupByKey(forecastsRequest, null, null, referenceVal);
        mergDiffYearData(forecastsRequest, resultLv1List, lv1Group);
        // 因排序需修改BgCodes,此处取出Bgcodes的值，排序完后重新放回。
        List<String> bgCodes = forecastsRequest.getBgCodes();
        resortLv1(forecastsRequest, resultLv1List, recordOrders(forecastsRequest));
        forecastsRequest.setBgCodes(bgCodes);
        forecastsRequest.setLevel("1");
        Map<String, List<KrCpfLv1AggrResponse>> tempLv2Map = groupByKey(forecastsRequest, BoolEnum.TRUE.getCode(), null, null);
        forecastsRequest.setLevel("2");
        Map<String, List<KrCpfLv1AggrResponse>> templ1Map = groupByKey(forecastsRequest, BoolEnum.TRUE.getCode(), BoolEnum.TRUE.getCode(), null);
        buildFullRows(tempLv2Map, templ1Map, resultLv1List, forecastsRequest);
    }

    /**
     * 月度添加 ai，集团 ，实际，预测 四个表数据查询
     * @param referenceVal referenceVal
     * @param resultLv1List resultLv1List
     * @param forecastsRequest forecastsRequest
     * @throws CommonApplicationException 异常
     */
    private void montyFetchDataByLevel
    (AtomicReference<Map<String, List<KrCpfLv1AggrResponse>>> referenceVal, List<KrCpfLv1AggrResponse> resultLv1List, ForecastsRequest
            forecastsRequest) throws CommonApplicationException, ExecutionException, InterruptedException {
        Map<String, List<KrCpfLv1AggrResponse>> lv1Group = getData(referenceVal, forecastsRequest, null, null);
        mergDiffYearData(forecastsRequest, resultLv1List, lv1Group);
        // 因排序需修改BgCodes,此处取出Bgcodes的值，排序完后重新放回。
        List<String> bgCodes = forecastsRequest.getBgCodes();
        resortLv1(forecastsRequest, resultLv1List, recordOrders(forecastsRequest));
        forecastsRequest.setBgCodes(bgCodes);
        forecastsRequest.setLevel("1");
        Map<String, List<KrCpfLv1AggrResponse>> tempLv2Map = getData(null, forecastsRequest, BoolEnum.TRUE.getCode(), null);
        forecastsRequest.setLevel("2");
        Map<String, List<KrCpfLv1AggrResponse>> templ1Map = getData(null, forecastsRequest, BoolEnum.TRUE.getCode(), BoolEnum.TRUE.getCode());
        buildFullRows(tempLv2Map, templ1Map, resultLv1List, forecastsRequest);
    }

    private List<String> recordOrders(ForecastsRequest forecastsRequest) {
        List<String> recordOrders;
        switch (forecastsRequest.getQueryType()) {
            // ai融合表排序
            case "kr_cpf_lv1_aggr_fcst_comb_t":
                recordOrders = iKrCpfLv1AggrDao.findAiCombinedRecordOrder(forecastsRequest);
                break;
            // 集团配置表排序
            case "dm_fop_group_analysts_result_t":
                recordOrders = iKrCpfLv1AggrDao.findGroupAnalystsRecordOrder(forecastsRequest);
                break;
            // "H","F" 实际数和预测表排序
            default:
                recordOrders = iKrCpfLv1AggrDao.findRecordOrder(forecastsRequest);
                break;
        }
        return recordOrders;
    }

    Map<String, List<KrCpfLv1AggrResponse>> getData(AtomicReference<Map<String, List<KrCpfLv1AggrResponse>>> referenceVal, ForecastsRequest forecastsRequest, String lv2Code, String l1Name) throws ExecutionException, InterruptedException {
        List<KrCpfLv1AggrResponse> list = new ArrayList();
        // 查询实际表，预测表数据
        CompletableFuture<List<KrCpfLv1AggrResponse>> sourceData = CompletableFuture.supplyAsync(() -> iKrCpfLv1AggrDao.findDataByTop(forecastsRequest));
        CompletableFuture<List<KrCpfLv1AggrResponse>> aiCombinedData = null;
        CompletableFuture<List<KrCpfLv1AggrResponse>> groupAnalystsData = null;
        if ("01".equals(forecastsRequest.getLevel()) || "1".equals(forecastsRequest.getLevel())) {
            // 查询ai融合
            aiCombinedData = CompletableFuture.supplyAsync(() -> iKrCpfLv1AggrDao.findAiCombinedData(forecastsRequest));

            if (StringUtils.equals(RoleEnum.GROUP_ANALYST_ADMIN.getCode(), forecastsRequest.getRightVO().getRoleName())
                    && forecastsRequest.getBgCodes().contains("PROD0002")
            ) {
                // 查询集团配置表信息
                groupAnalystsData = CompletableFuture.supplyAsync(() -> iKrCpfLv1AggrDao.findGroupAnalystsData(forecastsRequest));
            }
        }
        list.addAll(sourceData.get());
        if(aiCombinedData != null){
            list.addAll(aiCombinedData.get());
        }
        if(groupAnalystsData != null){
            list.addAll(groupAnalystsData.get());
        }
        return list.stream().filter(item -> {
            String tempKey = buildTempKey(item, lv2Code, l1Name);
            item.setGroupKey(tempKey);
            if (StringUtils.equals("01", forecastsRequest.getLevel())) {
                return referenceVal.get().containsKey(tempKey);
            }
            return true;
        }).collect(Collectors.groupingBy(act -> act.getGroupKey()));
    }


    /**
     * 构建表行信息
     *
     * @param forecastsRequest 预测请求
     * @param resultLv1List    层级
     */
    public void buildFullRows
    (Map<String, List<KrCpfLv1AggrResponse>> tempLv2Map, Map<String, List<KrCpfLv1AggrResponse>> templ1Map, List<KrCpfLv1AggrResponse> resultLv1List, ForecastsRequest
            forecastsRequest) throws CommonApplicationException {
        for (KrCpfLv1AggrResponse lv1 : resultLv1List) {
            Map<String, List<KrCpfLv1AggrResponse>> lv2Group = new LinkedHashMap<>();
            tempLv2Map.entrySet().forEach(group -> {
                if (group.getKey().indexOf(lv1.getGroupKey()) >= 0) {
                    lv2Group.put(group.getKey(), group.getValue());
                }
            });
            List<KrCpfLv1AggrResponse> tempList = new ArrayList<>();
            mergDiffYearData(forecastsRequest, tempList, lv2Group);
            if (null != templ1Map && (StringUtils.equals(RoleEnum.GROUP_ANALYST_ADMIN.getCode(), forecastsRequest.getRightVO().getRoleName())
                    || StringUtils.equals(RoleEnum.ADMIN.getCode(), forecastsRequest.getRightVO().getRoleName())
                    || StringUtils.equals(RoleEnum.GROUP_ANALYST_GENERAL.getCode(), forecastsRequest.getRightVO().getRoleName()))) {
                buildFullRows(templ1Map, null, tempList, forecastsRequest);
            }
            lv1.setChildren(tempList);
        }
    }

    /**
     * 设置各列的值
     *
     * @param forecastsRequest
     * @param resultList
     * @param group
     */
    public void mergDiffYearData(ForecastsRequest forecastsRequest, List<KrCpfLv1AggrResponse> resultList,
                                 Map<String, List<KrCpfLv1AggrResponse>> group) {
        group.entrySet().forEach(entry -> {
            KrCpfLv1AggrResponse lv2Row = buildLv1Info(entry.getValue().get(0));
            lv2Row.setGroupKey(entry.getKey());
            entry.getValue().forEach(idenity -> {
                setModuleData(forecastsRequest, idenity, lv2Row);
            });
            resultList.add(lv2Row);
        });
    }


    /**
     * 获取版本信息
     *
     * @return List
     */
    @Override
    public List<QueryVersionResponse> getVersions(ForecastsRequest forecastsRequest) throws
            CommonApplicationException {
        List<Long> resultLists = null;
        // 封装成Bean返回
        List<QueryVersionResponse> versionResponseList = new ArrayList<>();
        // 年度修改版本查询逻辑 2023-11
        if (PredictionEnum.YEAR.getCode().equals(forecastsRequest.getPredictionType())) {
            // 获取不展示PeriodVersion 版本控制不展示版本
            String params = FcstGlobalParameterUtil.getRegistryEspaceValue(ANNUAL_BUDGET, "PeriodVersion", "");
            List<String> templist = Arrays.asList(params.split(","));
            resultLists = iKrCpfLv1AggrDao.getFutureYearVersions().stream().filter(item -> !templist.contains(String.valueOf(item))).collect(Collectors.toList());
        } else {
            resultLists = iKrCpfLv1AggrDao.getVersions();
        }
        resultLists.forEach(result -> versionResponseList.add(QueryVersionResponse.builder()
                .period(result)
                .predictionType(forecastsRequest.getPredictionType())
                .periodDescrption(String.valueOf(result))
                .build()));
        return versionResponseList;
    }

    /**
     * 获取底部的小计数据信息
     *
     * @param forecastsRequest 预测请求
     * @return KrCpfLv1AggrResponse
     */
    @Override
    public KrCpfLv1AggrResponse getSubTotalInfo(ForecastsRequest forecastsRequest) throws
            CommonApplicationException, ExecutionException, InterruptedException {
        KrCpfLv1AggrResponse tailRow = new KrCpfLv1AggrResponse();
        List<KrCpfLv1AggrResponse> subTotalList;
        if (StringUtils.equals(forecastsRequest.getPredictionType(), PredictionEnum.MONTH.getCode())) {
            subTotalList = getMonthSubTotalInfo(forecastsRequest);
        }else {
            subTotalList = iKrCpfLv1AggrDao.findSubtotalGroup(forecastsRequest);
        }
        if (CollectionUtils.isEmpty(subTotalList)) {
            return tailRow;
        }
        subTotalList.forEach(sub -> {
            sub.setMgpRateAfter(CalcUtils.div(sub.getMgpRateAfter(), sub.getEquipRevAfter()));
            setModuleData(forecastsRequest, sub, tailRow);
        });
        return tailRow;
    }

    /**
     * 月度数据查询逻辑处理
     *
     * @param forecastsRequest 查询条件
     * @return List
     * @throws CommonApplicationException 异常
     * @throws ExecutionException 异常
     * @throws InterruptedException 异常
     */
    private List<KrCpfLv1AggrResponse> getMonthSubTotalInfo(ForecastsRequest forecastsRequest) throws ExecutionException, InterruptedException {
        List<KrCpfLv1AggrResponse> totalList = new ArrayList<>();
        // 实际，预测表数据求和项查询
        CompletableFuture<List<KrCpfLv1AggrResponse>> actAndFcstList = CompletableFuture.supplyAsync(() ->
            iKrCpfLv1AggrDao.findSubtotalGroup(forecastsRequest));
        // ai 融合预测表求和查询
        CompletableFuture<List<KrCpfLv1AggrResponse>> subtotalAiCombinedData = CompletableFuture.supplyAsync(() -> iKrCpfLv1AggrDao.findSubtotalAiCombinedData(forecastsRequest));
        // 配置表求和项查询
        if (StringUtils.equals(RoleEnum.GROUP_ANALYST_ADMIN.getCode(), forecastsRequest.getRightVO().getRoleName())
                && forecastsRequest.getBgCodes().contains("PROD0002")
        ) {
            CompletableFuture<List<KrCpfLv1AggrResponse>> groupAnalystsData = CompletableFuture.supplyAsync(() -> iKrCpfLv1AggrDao.findSubtotalGroupAnalystsData(forecastsRequest));
            totalList.addAll(groupAnalystsData.get());
        }
        totalList.addAll(actAndFcstList.get());
        totalList.addAll(subtotalAiCombinedData.get());
        return totalList;
    }


    /**
     * 导出lv1数据
     *
     * @param forecastsRequest
     * @param response
     * @throws CommonApplicationException
     */
    @Override
    public CommonResult exportLv1Data(ForecastsRequest forecastsRequest, List<KrCpfLv1AggrResponse> resultList, HttpServletResponse response) throws
            CommonApplicationException, ExecutionException, InterruptedException {
        String predictionType = forecastsRequest.getPredictionType();
        List<String> bgCodes = Arrays.asList(forecastsRequest.getBgCode());
        List<KrCpfLv1AggrResponse> totalList = convertTreeTotalList(resultList);
        String bgCode = CommUtils.getBgCode(bgCodes);
        // bg code is null 中断操作
        if (CollectionUtil.isNullOrEmpty(bgCodes)) {
            return CommonResult.success("ok");
        }
        String stepType = CommonConstant.FCST_YEAR_STEP;
        if (StringUtils.equals(PredictionEnum.MONTH.getCode(), predictionType) && StringUtils.equals(forecastsRequest.getFcstStep(), CommonConstant.FCST_HALF_YEAR_STEP)) {
            stepType = DateUtil.getSemiAnnual(String.valueOf(forecastsRequest.getPeriodId()));
        } else if (StringUtils.equals(PredictionEnum.MONTH.getCode(), predictionType) && StringUtils.equals(forecastsRequest.getFcstStep(), CommonConstant.FCST_QUARTER_STEP)) {
            stepType = DateUtil.getQuarter(String.valueOf(forecastsRequest.getPeriodId()));
        }
        List<TableHeaderVo> lv1Header = HeaderUtils.buildMonthHeadersByBg(bgCode, stepType, forecastsRequest.getPeriodId(), predictionType);
        String type = "1";
        if (StringUtils.equals(BgEnum.GROUP.getCode(), bgCode)) {
            type = "2";
        }
        KrCpfLv1AggrResponse subTotalInfo = getSubTotalInfo(forecastsRequest);
        subTotalInfo.setLv1Name("产业小计");
        totalList.add(subTotalInfo);
        Map<String, Object> params = new ConcurrentHashMap<>();
        String fileName = getFileNameByBg(forecastsRequest, "1");
        params.put("fileName", fileName);
        params.put("userId", UserHandle.getUserId());
        iExportService.exportLv1Data(type, totalList, lv1Header, response, params);
        Long size = (Long) Optional.ofNullable(params.get("fileSize")).orElse(0L);
        creatRecord(fileName, size,
                SaveTypeEnum.SAVE.getCode(), "", resultList.size(), String.valueOf(forecastsRequest.getPeriodId()),
                String.valueOf(params.get("fileKey")), null, OptTypeEnum.EXPORT.getCode(), RecStsEnum.SUCCESS.getCode(), ModuleEnum.MODULE_FORECASTS.getDesc());

        return CommonResult.success("ok");
    }

    /**
     * 列表树结构转化为行数据
     *
     * @param treeList
     * @return
     */
    public List<KrCpfLv1AggrResponse> convertTreeTotalList(List<KrCpfLv1AggrResponse> treeList) throws
            CommonApplicationException {
        List<KrCpfLv1AggrResponse> allLineList = new ArrayList<>();
        treeList.forEach(result -> {
            allLineList.add(result);
            if (CollectionUtils.isEmpty(result.getChildren())) {
                return;
            }
            result.getChildren().forEach(child -> {
                allLineList.add(child);
                if (CollectionUtils.isEmpty(child.getChildren())) {
                    return;
                }
                child.getChildren().forEach(subChild -> {
                    allLineList.add(subChild);
                });
            });
        });
        return allLineList;
    }

    /**
     * 获取标签调度刷新结果的时间
     *
     * @return list
     * @throws CommonApplicationException
     */
    @Override
    public String getUpdateDataTimeInfo(ForecastsRequest forecastsRequest) throws CommonApplicationException {
        try {
            // 修改规则：1.lv1表最新时间是下午，时间展示当天上午12点前的最新的数据。 2.lv1表最新时间时间是上午，时间展示最近一天的上午12点前的最新的数据。
            SpartProfitingRelationVO currentTimeInfo = iSpartProfitingRelationDao.getCurrentDataTimeInfo(forecastsRequest);
            Timestamp currentTime = currentTimeInfo.getLastUpdateDate();
            Date nowTime = new Date(currentTime.getTime());
            String todayFormat = FastDateFormat.getInstance("yyyy-MM-dd").format(currentTime);
            Date compareTime = FastDateFormat.getInstance("yyyy-MM-dd-HH:mm:ss").parse(todayFormat + "-12:00:00");
            // 当前时间早于今日的12：00，取最近一天的12点前的数据。
            if (nowTime.before(compareTime)) {
                forecastsRequest.setTargetPeriod(todayFormat + " 00:00:00");
            } else {
                forecastsRequest.setTargetPeriod(todayFormat + " 12:00:00");
            }
            SpartProfitingRelationVO updateTimeInfo = iSpartProfitingRelationDao.getUpdateDataTimeInfo(forecastsRequest);
            return null == updateTimeInfo.getLastUpdateDate()
                    ? ""
                    : String.valueOf(updateTimeInfo.getLastUpdateDate())
                    .substring(0, String.valueOf(updateTimeInfo.getLastUpdateDate()).indexOf("."));
        } catch (Exception ex) {
            logger.error("标签调度刷新时间获取失败: {}", ex);
            return "";
        }
    }

    /**
     * 获取指定产业可展开l1详情(l1下拉框)
     *
     * @param forecastsRequest
     * @return
     * @throws CommonApplicationException
     */
    @Override
    public List<KrCpfLv1AggrResponse> getSelectedProductLv1Detail(ForecastsRequest forecastsRequest) throws
            CommonApplicationException {
        List<KrCpfLv1AggrResponse> resultLv1List = new ArrayList<>();
        forecastsRequest.setLevel("2");
        forecastsRequest.buildFcstStepQueryParam();
        forecastsRequest.withOverseaDescValue();
        forecastsRequest.setLv1s(Arrays.asList(forecastsRequest.getLv1Codes()));
        if (checkLv1PermissonResult(forecastsRequest)) {
            return resultLv1List;
        }
        Map<String, List<KrCpfLv1AggrResponse>> l1Map = groupByKey(forecastsRequest, BoolEnum.TRUE.getCode(), BoolEnum.TRUE.getCode(), null);
        if (CollectionUtils.isEmpty(l1Map)) {
            return resultLv1List;
        }
        l1Map.entrySet().forEach(entry -> {
            KrCpfLv1AggrResponse l1Row = buildLv1Info(entry.getValue().get(0));
            l1Row.setGroupKey(entry.getKey());
            if (StringUtils.equals("1", l1Row.getArticulationFlag()) || "2".equals(l1Row.getArticulationFlag())) {
                resultLv1List.add(l1Row);
            }
        });
        return resultLv1List;
    }

    @Override
    public List<ForecastsStepVO> getForecastsStepMethod(ForecastsStepVO forecastsStepVO) throws ApplicationException {
        String language = RequestContext.getCurrent().getUserLanguage();
        final List<LookupItemVO> step = iLookupItemQueryService.findItemListByZhOrEn("FCST_STEP_TYPE", language);
        final List<LookupItemVO> itemType = iLookupItemQueryService.findItemListByZhOrEn("FCST_METHOD_TYPE", language);
        if (CollectionUtils.isEmpty(itemType) || CollectionUtils.isEmpty(step)) {
            return Collections.EMPTY_LIST;
        }
        // 预测方法设置
        final Map<Object, List<ForecastsMethodVO>> fcstTypeMap = itemType.stream().map(lookupItemVO -> {
            ForecastsMethodVO vo = new ForecastsMethodVO();
            vo.setFcstType(lookupItemVO.getItemCode());
            vo.setFcstTypeDisplay(lookupItemVO.getItemName());
            vo.setFcstStep(lookupItemVO.getParentItem().getItemCode());
            vo.setIndex(lookupItemVO.getItemIndex());
            return vo;
        }).collect(Collectors.groupingBy(ForecastsMethodVO::getFcstStep));
        // 预测步长设置
        return step.stream().map(lookupItemVO -> {
            ForecastsStepVO vo = new ForecastsStepVO();
            vo.setFcstStep(lookupItemVO.getItemName());
            vo.setPredictionType(lookupItemVO.getItemAttr1());
            vo.setTabGraphType(lookupItemVO.getItemAttr2());
            vo.setFcstStepDisplay(lookupItemVO.getItemName());
            vo.setIndex(lookupItemVO.getItemIndex());
            vo.setForecastsMethods(fcstTypeMap.get(lookupItemVO.getItemCode()));
            return vo;
        }).collect(Collectors.toList());
    }

    //  权限检验
    private boolean checkLv1PermissonResult(ForecastsRequest requestVO) {
        UserVO curUser = (UserVO) RequestContext.getCurrent(true).getUser();
        RoleVO currentRole = curUser.getCurrentRole();
        DataPermissionsVO rightVO = getUserRoleOpt(currentRole.getRoleId());
        // 校验权限信息
        if (!rightVO.isOptLv1Right()) {
            return true;
        }
        if (!StringUtils.equals("ALL", rightVO.getLv1DataType())) {
            List<String> lv1s = Arrays.asList(rightVO.getLv1DimensionSet().toArray(new String[0]));
            // 无产业权限
            if (CollectionUtils.isEmpty(lv1s)) {
                return true;
            }
            //  查询为全量产业，则设置查询为当前权限范围
            if (Objects.isNull(requestVO.getLv1s())) {
                requestVO.setLv1s(lv1s);
            } else {
                //  否则查询产业为交集
                List<String> lv1List = lv1s.stream().filter(requestVO.getLv1s()::contains).collect(Collectors.toList());
                // 交集为空，返回也为空
                if (CollectionUtil.isNullOrEmpty(lv1List)) {
                    return true;
                }
                requestVO.setLv1s(lv1List);
            }
        }
        return false;
    }
}