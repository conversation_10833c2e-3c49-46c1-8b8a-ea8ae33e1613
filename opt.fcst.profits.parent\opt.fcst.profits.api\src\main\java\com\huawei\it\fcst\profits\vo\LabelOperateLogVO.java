/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.vo;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年04月08日
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LabelOperateLogVO implements Serializable {
    private static final long serialVersionUID = 6695383790847716568L;
    private String methodName;
    private String module;
    private Boolean isSkip;
    private Long userId;
    private String objectId;
    private int countNum;
    private int upsertNum;
    private String status;

}
