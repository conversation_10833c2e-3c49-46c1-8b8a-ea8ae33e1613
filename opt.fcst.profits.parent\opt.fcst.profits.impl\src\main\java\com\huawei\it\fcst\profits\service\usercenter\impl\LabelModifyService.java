/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.usercenter.impl;

import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.utils.UserHandle;
import com.huawei.it.fcst.profits.dao.ILabelModifyDao;
import com.huawei.it.fcst.profits.service.ILabelModifyService;
import com.huawei.it.fcst.profits.vo.LabelModifyVO;
import com.huawei.it.fcst.profits.vo.request.LabelModifyRequest;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

/**
 * The Service of LabelModifyService entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-17 19:38:39
 */
@Service
public class LabelModifyService implements ILabelModifyService {

    private static final Logger logger = LoggerFactory.getLogger(LabelModifyService.class);

    @Inject
    private ILabelModifyDao iLabelModifyDao;

    /**
     * 分页查找数据信息
     *
     * @param labelRequest 标签请求
     * @return PagedResult分页信息结果
     */
    @Override
    public PagedResult<LabelModifyVO> findByPage(LabelModifyRequest labelRequest) {
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(labelRequest.getPageSize());
        pageVO.setCurPage(labelRequest.getPageIndex());
        // 构建创建时间月头/月尾查询条件参数
        if (StringUtils.isNotBlank(labelRequest.getTimeInterval())) {
            labelRequest.setBeginTime(null);
            labelRequest.setEndTime(null);
            if (!StringUtils.equals("ALL", labelRequest.getTimeInterval())) {
                labelRequest.setBeginTime(TimeUtils.getBeginTime(labelRequest.getTimeInterval()));
                labelRequest.setEndTime(TimeUtils.getEndTime(labelRequest.getTimeInterval()));
            }
        }
        if (StringUtils.isNotBlank(labelRequest.getPageModule()) && StringUtils.equals("ALL", labelRequest.getPageModule())) {
            labelRequest.setPageModule(null);
        }
        // 根据userId筛选，只展示自己操作的记录
        labelRequest.setUserId(String.valueOf(UserHandle.getUserId()));
        return iLabelModifyDao.findByPage(labelRequest, pageVO);
    }

}
