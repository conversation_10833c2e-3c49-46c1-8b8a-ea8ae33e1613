package com.huawei.it.fcst.profits.vo;

import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class PlanComConfigDataVOTest {
    PlanComConfigDataVO dataVO = new PlanComConfigDataVO();

    @Test
    void getLv1Code() {
        // run the test
        dataVO.setLv1Code("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getLv1Code());
    }

    @Test
    void getPlanComLv1A() {
        // run the test
        dataVO.setPlanComLv1("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getPlanComLv1());
    }


    @Test
    void getPlanComLv2B() {
        // run the test
        dataVO.setPlanComLv2("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getPlanComLv2());
    }

    @Test
    void getPlanComLv3C() {
        // run the test
        dataVO.setPlanComLv3("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getPlanComLv3());
    }

    @Test
    void getBusiLv4D() {
        // run the test
        dataVO.setBusiLv4("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getBusiLv4());
    }

    @Test
    void getL1Name() {
        // run the test
        dataVO.setL1Name("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getL1Name());
    }

    @Test
    void getL2Name() {
        // run the test
        dataVO.setL2Name("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getL2Name());
    }

    @Test
    void getL2Coefficient() {
        // run the test
        dataVO.setL2Coefficient("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getL2Coefficient());
    }
    @org.junit.Test
    public void testEquals() {
        // run the test
        dataVO.setLv1Name("Test");
        dataVO.setPlanComLv1("Test");
        dataVO.setPlanComLv2("Test");
        dataVO.setPlanComLv3("Test");
        dataVO.setBusiLv4("Test");
        dataVO.setL1Name("Test");
        PlanComConfigDataVO testVo =new PlanComConfigDataVO();
        testVo.setLv1Name("Test");
        testVo.setPlanComLv1("Test");
        testVo.setPlanComLv2("Test");
        testVo.setPlanComLv3("Test");
        testVo.setBusiLv4("Test");
        testVo.setL1Name("Test");
        // verify the results
        Assert.assertEquals(true, dataVO.equals(testVo));
    }
    @org.junit.Test
    public void testEqualsA() {
        // verify the results
        Assert.assertEquals(true, dataVO.equals(dataVO));
    }
    @org.junit.Test
    public void testEqualsB() {
        // verify the results
        Assert.assertEquals(false, dataVO.equals(null));
    }
    @org.junit.Test
    public void testHashCode() {
        // verify the results
        Assert.assertNotNull(dataVO.hashCode());
    }

}