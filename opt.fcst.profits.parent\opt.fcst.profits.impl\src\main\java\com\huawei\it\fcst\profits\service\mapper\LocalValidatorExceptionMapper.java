/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-202024. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.service.mapper;

import com.huawei.it.fcst.profits.common.vo.ResultCode;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Path;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 参数校验异常信息转换类
 *
 * @since 3.0
 */
public class LocalValidatorExceptionMapper implements ExceptionMapper<ConstraintViolationException> {

    @Override
    public Response toResponse(ConstraintViolationException ex) {
        Set constraintViolations = ex.getConstraintViolations();
        Map<String, Object> map = setErrorInfo(constraintViolations);
        Response.ResponseBuilder builder = Response.status(Response.Status.OK);
        builder.type(MediaType.APPLICATION_JSON);
        builder.entity(map);
        return builder.build();
    }

    private static <T> Map<String, Object> setErrorInfo(Set<ConstraintViolation<T>> constraintViolations) {
        Map<String, Object> error = new HashMap<>();
        error.put("code", ResultCode.FAILED.getCode());
        List<Map> errorList = new ArrayList<>();
        for (ConstraintViolation<?> constraintViolation : constraintViolations) {
            Map<String, Object> errorMap = new HashMap<>();
            String errorCode = constraintViolation.getMessage();
            Path propertyPath = constraintViolation.getPropertyPath();
            String property = propertyPath.toString();
            property = property.substring(property.lastIndexOf(".") + 1);
            Object value = constraintViolation.getInvalidValue();
            errorMap.put("errorMessage", errorCode);
            errorMap.put("businessArgs", Collections.singletonList(toString(property + ":" + value)));
            errorList.add(errorMap);
        }
        error.put("msg", errorList);
        return error;
    }

    private static String toString(Object obj) {
        return obj == null ? "" : obj.toString();
    }
}
