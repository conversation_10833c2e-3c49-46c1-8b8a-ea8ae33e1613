/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2026. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.response.QueryVersionResponse;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Named;

@JalorResource(code = "forecastDimensionController", desc = "量纲层级盈利预测")
@Named("forecastDimensionController")
public class ForecastDimensionController implements IForecastDimensionController {

    private static final Logger logger = LoggerFactory.getLogger(ForecastDimensionController.class);


    @JalorOperation(code = "getBudgetVersionInfo", desc = "预测/预算版本信息获取")
    @Override
    public CommonResult<QueryVersionResponse> getBudgetVersionInfo(ForecastsRequest forecastsRequest) {
        return null;
    }
}
