/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.dao;

import com.huawei.it.fcst.profits.vo.CoaProdInfoVO;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.fcst.profits.vo.HolisticViewProdInfoVO;
import com.huawei.it.fcst.profits.vo.ObjectConfigDataVO;
import com.huawei.it.fcst.profits.vo.PlanComProdInfoVo;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.request.LabelConfigQueryRequest;
import com.huawei.it.fcst.profits.vo.request.SpartInfoRequest;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityL1InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityL2InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityL3InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularityLv1InfoVo;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularitySpartInfoVo;
import com.huawei.it.fcst.profits.vo.response.SpartInfoVo;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The DAO to access KrCpfL1ActVO entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:25:12
 */
public interface ILabelProductionDao {

    PagedResult<HolisticViewProdInfoVO> findIctProductInfoByPage(LabelConfigQueryRequest request, PageVO pageVO,
        @Param("l1Flag") boolean l1QueryFlag, @Param("artFlag") boolean artQueryFlag);

    PagedResult<PlanComProdInfoVo> findPlanComProductInfoByPage(LabelConfigQueryRequest request, PageVO pageVO,
        @Param("l1Flag") boolean l1QueryFlag, @Param("l2Flag") boolean l2QueryFlag,
        @Param("busiLv4Flag") boolean busiLv4Flag);

    PagedResult<CoaProdInfoVO> findCoaProductInfoByPage(LabelConfigQueryRequest request, PageVO pageVO,
        @Param("l1Flag") boolean l1QueryFlag, @Param("l2Flag") boolean l2QueryFlag);

    List<HolisticViewProdInfoVO> getIctMonitorCondition(LabelConfigQueryRequest requestVO);

    List<HolisticViewProdInfoVO> getIctMonitorLastUpdated(LabelConfigQueryRequest requestVO,@Param("l1Flag") boolean l1QueryFlag, @Param("artFlag") boolean artQueryFlag);
    List<HolisticViewProdInfoVO> getIctMonitorVersion(LabelConfigQueryRequest requestVO);
    List<HolisticViewConfigDataVO> getIctConfigCondition(LabelConfigQueryRequest requestVO);

    List<PlanComProdInfoVo> getPlanComMonitorCondition(LabelConfigQueryRequest requestVO);

    List<PlanComProdInfoVo> getPlanComMonitorLastUpdated(LabelConfigQueryRequest requestVO, @Param("l1Flag") boolean l1Flag,
        @Param("l2Flag")boolean l2Flag, @Param("busiLv4Flag")boolean busiLv4Flag);

    List<PlanComProdInfoVo> getPlanComMonitorVersion(LabelConfigQueryRequest requestVO);

    List<CoaProdInfoVO> getCoaMonitorCondition(LabelConfigQueryRequest requestVO);

    List<CoaProdInfoVO> getCoaMonitorLastUpdated(LabelConfigQueryRequest requestVO,
        @Param("l1Flag") boolean l1QueryFlag, @Param("l2Flag") boolean l2QueryFlag);

    List<CoaProdInfoVO> getCoaMonitorProductName(LabelConfigQueryRequest requestVO);

    List<CoaProdInfoVO> getCoaMonitorVersion(LabelConfigQueryRequest requestVO);
    List<SpartProfitingRelationVO> getObjectAuditCondition(LabelConfigQueryRequest requestVO);

    List<ObjectConfigDataVO> getObjectConfigCondition(LabelConfigQueryRequest requestVO);

    List<PlanComProdInfoVo> getPlanComConfigCondition(LabelConfigQueryRequest requestVO);

    List<CoaProdInfoVO> getCoaConfigCondition(LabelConfigQueryRequest requestVO);

    List<PlanComProdInfoVo> getPlanComSopInfo(LabelConfigQueryRequest requestVO);

    List<ProfitGranularityL3InfoVo> getProfitExaminingLv1QueryCondition(ForecastsRequest requestVO);

    List<ProfitGranularityLv1InfoVo> getProfitExaminingLv1MonthInfo(ForecastsRequest requestVO);

    List<ProfitGranularityLv1InfoVo> getProfitExaminingLv1YtdInfo(ForecastsRequest requestVO);

    List<ProfitGranularityL1InfoVo> getProfitExaminingL1MonthInfo(ForecastsRequest requestVO);

    List<ProfitGranularityL1InfoVo> getProfitExaminingL1YtdInfo(ForecastsRequest requestVO);

    List<ProfitGranularityL2InfoVo> getProfitExaminingL2MonthInfo(ForecastsRequest requestVO);

    List<ProfitGranularityL2InfoVo> getProfitExaminingL2MonthShipInfo(ForecastsRequest requestVO);

    List<ProfitGranularityL2InfoVo> getProfitExaminingL2YtdInfo(ForecastsRequest requestVO);

    List<ProfitGranularityL2InfoVo> getProfitExaminingL2YtdShipInfo(ForecastsRequest requestVO);

    List<ProfitGranularitySpartInfoVo> getProfitExaminingSpartMonthCostInfo(ForecastsRequest requestVO);

    List<ProfitGranularitySpartInfoVo> getProfitExaminingSpartMonthRevInfo(ForecastsRequest requestVO);

    List<ProfitGranularitySpartInfoVo> getProfitExaminingSpartYtdCostInfo(ForecastsRequest requestVO);
    List<ProfitGranularitySpartInfoVo> getProfitExaminingSpartYtdRevInfo(ForecastsRequest requestVO);

    List<SpartInfoVo> getProfitExaminingSpartYtdEntries(ForecastsRequest requestVO);
    PagedResult<SpartInfoVo> getPersonalSpartInfoPage(SpartInfoRequest request, PageVO vo);

    List<ProfitGranularityL3InfoVo> getProfitExaminingLv1QueryOrder(ForecastsRequest requestVO);
    List<ProfitGranularityL3InfoVo> getProfitExaminingLv2QueryOrder(ForecastsRequest requestVO);
    List<ProfitGranularityL3InfoVo> getProfitExaminingL1QueryOrder(ForecastsRequest requestVO);

    List<ProfitGranularityL3InfoVo> getProfitExaminingL2QueryOrder(ForecastsRequest requestVO);

    List<ProfitGranularityL3InfoVo> getProfitExaminingL3QueryOrder(ForecastsRequest requestVO);

    List<ProfitGranularityL1InfoVo> getProfitExaminingL1UnionAllInfo(ForecastsRequest requestVO);
    List<ProfitGranularityL2InfoVo> getProfitExaminingL2UnionAllInfo(ForecastsRequest requestVO);
    List<ProfitGranularityL3InfoVo> getProfitExaminingL3MonthInfo(ForecastsRequest requestVO);
    List<ProfitGranularityL3InfoVo> getProfitExaminingL3YtdInfo(ForecastsRequest requestVO);
    List<ProfitGranularityL3InfoVo> getProfitExaminingL3UnionAllInfo(ForecastsRequest request);
}
