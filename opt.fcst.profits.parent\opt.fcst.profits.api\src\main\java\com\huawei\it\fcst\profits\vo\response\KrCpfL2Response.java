/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.huawei.it.fcst.profits.common.utils.FcstJsonFormatVisitorWrapper;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The Entity of KrCpfL2Response
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:26:09
 */
@Getter
@Setter
public class KrCpfL2Response {

    /**
     * 重量级团队LV1编码
     **/
    private String lv1Code;

    /**
     * BG编码
     **/
    private String bgCode;

    /**
     * L1名称
     **/
    private String l1Name;

    /**
     * L2名称
     **/
    private String l2Name;

    /**
     * 重量级团队LV1描述
     **/
    private String lv1Name;

    /**
     * BG名称
     **/
    private String bgName;

    /**
     * 场景
     **/
    private String targetPeriod;

    /**
     * 预测方法
     */
    private String fcstType;

    /**
     * 时点
     */
    private String fcstStep;

    /**
     * sop期次
     */
    private String phaseDate;

    /**
     * 币种
     **/
    private String currency;

    /**
     * 会计期
     **/
    private Long periodId;

    /**
     * 重量级团队LV2名称
     **/
    private String lv2Name;

    /**
     * 重量级团队LV2编码
     **/
    private String lv2Code;

    /**
     * 单位价格
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitPrice;

    /**
     * 单位成本
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitCost;

    /**
     * 对价前制毛率
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal mgpRateBefore;

    /**
     * 收入占比
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal revPercent;

    /**
     * 结转量
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal carryoverAmount;

    /**
     * 发货量
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal shipQty;

    /**
     * 单位价格预测_上标
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitPriceUpper;

    /**
     * 单位价格预测_下标
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitPriceLower;

    /**
     * 单位成本预测_上标
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitCostUpper;

    /**
     * 单位成本预测_下标
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitCostLower;

    /**
     * 收入占比
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal ratioRevPercent;

    /**
     * 结转量
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal ratioCarryoverAmount;

    /**
     * 价格预测准确值
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitPriceAcc;

    /**
     * 成本预测准确值
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal unitCostAcc;

    /**
     * 发货量
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal ratioShipQty;

    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal equipRev;

    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal ratioEquipRev;

    private String groupKey;

    private KrCpfL2Response lastYearActure;

    private KrCpfL2Response curYearSum;

    private KrCpfL2Response lastYearPeriod;

    private KrCpfL2Response aiAuto;

    private KrCpfL2Response ai2Auto;

    private KrCpfL2Response cnbgLastYearActure;

    private KrCpfL2Response cnbgCurYearSum;

    private KrCpfL2Response cnbgLastYearPeriod;

    private KrCpfL2Response cnbgAiAuto;

    private KrCpfL2Response cnbgAi2Auto;

    private KrCpfL2Response ebgLastYearActure;

    private KrCpfL2Response ebgCurYearSum;

    private KrCpfL2Response ebgLastYearPeriod;

    private KrCpfL2Response ebgAiAuto;

    private KrCpfL2Response ebgAi2Auto;

    /**
     * MCA调整率
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal mcaAdjustRatio;

    /**
     * 制毛调整率
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal mgpAdjustRatio;

    /**
     * 设备收入额（对价前）
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal equipRevBefore ;

    /**
     * 设备成本额（对价前）
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal equipCostBefore;

    // 区域字段
    private String overseaDesc;

    // 区分预测，预算
    private String dataType;

    /**
     * ran 占比字段
     */
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal revPercentRan;
}
