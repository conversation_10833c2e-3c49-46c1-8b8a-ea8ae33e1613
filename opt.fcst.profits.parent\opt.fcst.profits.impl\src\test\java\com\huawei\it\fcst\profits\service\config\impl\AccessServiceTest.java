/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.service.config.impl;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;
import com.huawei.it.jalor5.ws.RestResponse;
import com.huawei.it.jalor5.ws.service.IRestClientService;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
class AccessServiceTest {
    @InjectMocks
    private AccessService accessServiceUnderTest;
    @Mock
    private IRestClientService iRestClientService;

    @Mock
    HttpServletRequest httpServletRequest;
    private AutoCloseable mockitoCloseable;

    @BeforeEach
    void setUp() {
        mockitoCloseable = openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        testRequestContext.setUserIp("**********");
        RequestContextManager.setCurrent(testRequestContext);
    }

    @AfterEach
    void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    void testIsAccess() throws Exception {
        // Setup
        // Configure IRestClientService.doSecurityPost(...).
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("code",200);
        JSONArray jsonArray = JSON.parseArray("[{\"country\":\"sz\",\"city\":\"xx\",\"access_type\":\"iAccess\"}]");
        responseEntity.put("data", jsonArray);
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(200);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityPost(any(), any(),
                any())).thenReturn(restResponse);
        // Run the test
        final Boolean result = accessServiceUnderTest.isAccess();
        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testIsAccessTrue() throws Exception {
        // Setup
        // Configure IRestClientService.doSecurityPost(...).
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("code",200);
        JSONArray jsonArray = JSON.parseArray("[{\"country\":\"sz\",\"city\":\"xx\",\"access_type\":\"VIP\"}]");
        responseEntity.put("data", jsonArray);
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(200);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityPost(any(), any(),
                any())).thenReturn(restResponse);
        // Run the test
        final Boolean result = accessServiceUnderTest.isAccess();
        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    void testIsAccessDataCode() throws Exception {
        // Setup
        // Configure IRestClientService.doSecurityPost(...).
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("code",500);
        JSONArray jsonArray = JSON.parseArray("[{\"country\":\"sz\",\"city\":\"xx\",\"access_type\":\"iAccess\"}]");
        responseEntity.put("data", jsonArray);
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(200);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityPost(any(), any(),
                any())).thenReturn(restResponse);
        // Run the test
        final Boolean result = accessServiceUnderTest.isAccess();
        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testIsAccessResultData() throws Exception {
        // Setup
        // Configure IRestClientService.doSecurityPost(...).
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("code",200);
        responseEntity.put("data", new HashMap<>());
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(200);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityPost(any(), any(),
                any())).thenReturn(restResponse);
        // Run the test
        final Boolean result = accessServiceUnderTest.isAccess();
        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testIsAccessStatusCode() throws Exception {
        // Setup
        // Configure IRestClientService.doSecurityPost(...).
        final RestResponse restResponse = new RestResponse();
        restResponse.setResponseText("{}");
        Map<String,Object> responseEntity =  new HashMap<>();
        responseEntity.put("code",200);
        JSONArray jsonArray = JSON.parseArray("[{\"country\":\"sz\",\"city\":\"xx\",\"access_type\":\"iAccess\"}]");
        responseEntity.put("data", jsonArray);
        restResponse.setResponseEntity(responseEntity);
        restResponse.setStatusCode(500);
        restResponse.setStatusText("statusText");
        when(iRestClientService.doSecurityPost(any(), any(),
                any())).thenReturn(restResponse);
        // Run the test
        final Boolean result = accessServiceUnderTest.isAccess();
        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testIsSessionAccess() throws Exception {
        Map<String, String> sessionMap = new HashMap<>();
        sessionMap.put("hostName", "");
        sessionMap.put("access_type", "iAccess");
        RequestContext.getCurrent().getSession().put("IP_CLIENT_SESSION_IACCESS_KEY" + "**********", (Serializable) sessionMap);
        final Boolean result = accessServiceUnderTest.isAccess();
        // Verify the results
        assertThat(result).isFalse();
    }

}
