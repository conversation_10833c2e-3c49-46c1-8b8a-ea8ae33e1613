package com.huawei.it.fcst.profits.vo.request;

import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class LabelConfigQueryRequestTest {

    LabelConfigQueryRequest request = new LabelConfigQueryRequest();

    @Test
    void getPeriodId() {
        request.setPeriodId(1001L);
        Assert.assertEquals((Long)1001L, request.getPeriodId());
    }

    @Test
    void getLv1Name() {
        String[] strings = {"Test"};
        request.setLv1Name(strings);
        assertEquals(strings, request.getLv1Name());
    }

    @Test
    void getLv1Code() {
        String[] strings = {"Test"};
        request.setLv1Code(strings);
        assertEquals(strings, request.getLv1Code());
    }

    @Test
    void getLv2Name() {
        String[] strings = {"Test"};
        request.setLv2Name(strings);
        assertEquals(strings, request.getLv2Name());
    }

    @Test
    void getLv2Code() {
        String[] strings = {"Test"};
        request.setLv2Code(strings);
        assertEquals(strings, request.getLv2Code());
    }

    @Test
    void getLv3Name() {
        String[] strings = {"Test"};
        request.setLv3Name(strings);
        assertEquals(strings, request.getLv3Name());
    }

    @Test
    void getLv3Code() {
        String[] strings = {"Test"};
        request.setLv3Code(strings);
        assertEquals(strings, request.getLv3Code());
    }

    @Test
    void getL1Name() {
        String[] strings = {"Test"};
        request.setL1Name(strings);
        assertEquals(strings, request.getL1Name());
    }

    @Test
    void getL1Code() {
        String[] strings = {"Test"};
        request.setL1Code(strings);
        assertEquals(strings, request.getL1Code());
    }

    @Test
    void getL2Name() {
        String[] strings = {"Test"};
        request.setL2Name(strings);
        assertEquals(strings, request.getL2Name());
    }

    @Test
    void getL2Code() {
        String[] strings = {"Test"};
        request.setL2Code(strings);
        assertEquals(strings, request.getL2Code());
    }

    @Test
    void getL3Name() {
        String[] strings = {"Test"};
        request.setL3Name(strings);
        assertEquals(strings, request.getL3Name());
    }

    @Test
    void getL3Code() {
        String[] strings = {"Test"};
        request.setL3Code(strings);
        assertEquals(strings, request.getL3Code());
    }

    @Test
    void getPlanComLv1A() {
        String[] strings = {"Test"};
        request.setPlanComLv1(strings);
        assertEquals(strings, request.getPlanComLv1());
    }

    @Test
    void getPlanComLv2A() {
        String[] strings = {"Test"};
        request.setPlanComLv2(strings);
        assertEquals(strings, request.getPlanComLv2());
    }

    @Test
    void getPlanComLv3A() {
        String[] strings = {"Test"};
        request.setPlanComLv3(strings);
        assertEquals(strings, request.getPlanComLv3());
    }

    @Test
    void getBusiLv4A() {
        String[] strings = {"Test"};
        request.setBusiLv4(strings);
        assertEquals(strings, request.getBusiLv4());
    }

    @Test
    void getCoaCode() {
        String[] strings = {"Test"};
        request.setCoaCode(strings);
        assertEquals(strings, request.getCoaCode());
    }

    @Test
    void getArticulationFlag() {
        String[] strings = {"Test"};
        request.setArticulationFlag(strings);
        assertEquals(strings, request.getArticulationFlag());
    }

    @Test
    void getDataType() {
        request.setDataType("Test");
        Assert.assertEquals("Test", request.getDataType());
    }

    @Test
    void getStatus() {
        request.setStatus("Test");
        Assert.assertEquals("Test", request.getStatus());
    }

    @Test
    void getRoleId() {
        request.setRoleId(1250);
        Assert.assertEquals(new Integer(1250), request.getRoleId());
    }

    @Test
    void getLastUpdatedBy() {
        Long[] longs = {1250L};
        request.setLastUpdatedBy(longs);
        assertEquals(longs, request.getLastUpdatedBy());
    }

    @Test
    void getCreatedBy() {
        request.setCreatedBy(1250L);
        assertEquals(1250L, request.getCreatedBy());
    }

    @Test
    void getLv1Names() {
        LabelConfigQueryRequest request1 = new LabelConfigQueryRequest();
        request1.builder().busiLv4(new String[] {"Test"}).build();
        List<String> strings = Arrays.asList(new String[] {"Test"});
        request1.setLv1Names(strings);
        Assert.assertEquals(strings, request1.getLv1Names());
    }
}