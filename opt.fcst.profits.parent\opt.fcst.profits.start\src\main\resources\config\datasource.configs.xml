﻿<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
  http://www.springframework.org/schema/beans/spring-beans.xsd
  http://www.springframework.org/schema/aop 
  http://www.springframework.org/schema/aop/spring-aop.xsd
  http://www.springframework.org/schema/tx 
  http://www.springframework.org/schema/tx/spring-tx.xsd"
       default-lazy-init="true">

    <!--Gauss 数据源 -->
    <beans profile="dev,dev1,sit,uat,production">
        <bean id="wasDataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close" primary="true">
            <property name="driverClassName" value="${datasource.jdbcDriverClass.1}" />
            <property name="url" value="${datasource.url.1}" />
            <property name="username" value="${datasource.user.1}" />
            <property name="password" value="${datasource.password.1}" />

            <!-- 同一时刻可以从池分配的最多有效连接数量。设置为负数时表示无限制 -->
            <property name="maxTotal" value="${datasource.maxTotal.1}" />
            <!--池里保持空闲状态的最多连接数量(超出该值的空闲连接在归还到连接池时将被释放)。设置为负数时表示无限制 -->
            <property name="maxIdle" value="${datasource.maxIdle.1}" />
            <!-- 池里保持空闲状态的最少连接数量(低于该值时空闲连接将被创建)。设置为0时则不新建空闲连接 -->
            <property name="minIdle" value="${datasource.minIdle.1}" />
            <!--从池里获取连接时最大的等待时间(毫秒)。设置为-1时，若没有可用连接，池会无限期等待连接被回收。
                若设置为N，则连接池会等待N毫秒，获取不到连接时抛出异常 -->
            <property name="maxWaitMillis" value="${datasource.maxWaitMillis.1}" />
            <!--标记是否删除超过removeAbandonedTimout所指定时间的被遗弃的连接 -->
            <property name="removeAbandonedOnBorrow" value="${datasource.removeAbandonedOnBorrow.1}" />
            <!--一个被抛弃连接可以被移除的超时时间，单位为秒 -->
            <property name="removeAbandonedTimeout" value="${datasource.removeAbandonedTimeout.1}" />
            <property name="testWhileIdle" value="true" />
            <property name="validationQuery" value="select 1 from dual" />
            <property name="validationQueryTimeout" value="20" />
            <property name="timeBetweenEvictionRunsMillis" value="${datasource.timeBetweenEvictionRunsMillis.1}" />
            <property name="connectionProperties">
                <value>clientEncoding=utf-8</value>
            </property>
        </bean>
        <!-- Gauss Spring事务管理 -->
        <bean id="txManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
            <property name="dataSource" ref="wasDataSource"/>
        </bean>
    </beans>

    <beans>
        <tx:annotation-driven transaction-manager="txManager"
                              proxy-target-class="false" />
        <alias name="txManager" alias="transactionManager" />
        <tx:advice id="txAdvice" transaction-manager="txManager">
            <tx:attributes>
                <tx:method name="insert*" propagation="SUPPORTS" />
                <tx:method name="update*" propagation="SUPPORTS" />
                <tx:method name="delete*" propagation="SUPPORTS" />
                <tx:method name="select*" read-only="true" propagation="SUPPORTS" />
                <tx:method name="check*" read-only="true" propagation="SUPPORTS" />
                <tx:method name="setSelf" read-only="true" propagation="SUPPORTS" />
                <!-- 定义所有异常均回滚事务 -->
                <tx:method name="*" propagation="REQUIRED" read-only="false"
                           timeout="300" rollback-for="java.lang.Exception" />
            </tx:attributes>
        </tx:advice>
        <aop:config>
            <aop:pointcut id="transationMethod"
                          expression="execution(* com.huawei.it..*Service.*(..)) and !@annotation(com.huawei.it.jalor5.core.annotation.NoJalorTransation)" />
            <aop:advisor order="100" advice-ref="txAdvice"
                         pointcut-ref="transationMethod" />
        </aop:config>
        <!-- 配置拦截器切面，在包com.huawei下的，以Controller结尾的类的任意方法 -->
        <aop:config>
            <aop:advisor advice-ref="auditInterceptor"
                         pointcut="execution(@com.huawei.it.jalor5.core.log.Audit * com.huawei..*Controller.*(..))"/>
        </aop:config>
        <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
            <property name="configuration">
                <bean class="org.apache.ibatis.session.Configuration">
                    <property name="useActualParamName" value="false" />
                    <property name="callSettersOnNulls" value="true" />
                </bean>
            </property>
            <property name="dataSource" ref="wasDataSource" />
            <property name="plugins">
                <list>
                    <bean id="pagePlugin" class="com.huawei.it.jalor5.core.orm.PageInterceptor" />
                    <bean id="jalorResultSetPlugin"
                          class="com.huawei.it.jalor5.core.orm.JalorResultSetInterceptor" />
                    <bean id="programPlugin" class="com.huawei.it.jalor5.core.orm.ProgramInterceptor" />
                </list>
            </property>
            <property name="mapperLocations" value="classpath*:com/huawei/it/**/*Dao.xml" />
        </bean>
        <!-- 连数据库Dao自动mapping, 从jalor5.core.beans配置移到这里 -->
        <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
            <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory" />
            <property name="basePackage" value="com.huawei.it.**.dao" />
        </bean>
    </beans>
</beans>