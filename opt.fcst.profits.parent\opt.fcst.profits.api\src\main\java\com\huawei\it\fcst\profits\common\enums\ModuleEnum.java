/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * ModuleEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum ModuleEnum {
    MODULE_AUDIT("audit", "产品盈利-ICT-对象维表-标签审视"),
    MODULE_GROUP_ANALYS("groupAnalys", "配置管理-ICT-集团分析师预测"),
    MODULE_FORECASTS("forecasts", "产品盈利-ICT-价格成本预测");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    ModuleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * getByCode
     *
     * @param code code
     * @return StepTypeEnum
     */
    public static ModuleEnum getByCode(String code) {
        for (ModuleEnum value : ModuleEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

