/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * The Entity of DmPfDimensionAssociat
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-09-21 16:35:00
 */
@Getter
@Setter
public class DmPfDimensionAssociatVO {
    /**
     * LV1
     **/
    private String lv1Name;

    /**
     * LV1销售目录CODE
     **/
    private String lv1Code;

    /**
     * LV2
     **/
    private String lv2Name;

    /**
     * LV2销售目录CODE
     **/
    private String lv2Code;

    /**
     * bg名称
     **/
    private String bgName;

    /**
     * bg id
     **/
    private String bgCode;
}
