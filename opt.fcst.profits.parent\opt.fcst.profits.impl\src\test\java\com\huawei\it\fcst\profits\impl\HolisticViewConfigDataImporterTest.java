package com.huawei.it.fcst.profits.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

import com.huawei.it.fcst.profits.comm.AbstractConfigDataImporter;
import com.huawei.it.fcst.profits.common.poi.ExcelUtil;
import com.huawei.it.fcst.profits.common.utils.FileProcessUtis;
import com.huawei.it.fcst.profits.common.vo.ExcelVO;
import com.huawei.it.fcst.profits.common.vo.UploadInfoVO;
import com.huawei.it.fcst.profits.dao.IDmFopRecordDao;
import com.huawei.it.fcst.profits.dao.ILabelConfigDao;
import com.huawei.it.fcst.profits.service.config.impl.HolisticViewConfigDataImporter;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import cn.hutool.core.bean.BeanUtil;

import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * HolisticViewConfigDataImporter Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>3月 6, 2023</pre>
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {FileProcessUtis.class, CommUtils.class})
public class HolisticViewConfigDataImporterTest {
    @InjectMocks
    HolisticViewConfigDataImporter holisticViewConfigDataImporter;

    @Mock
    protected ExcelUtil excelUtil;

    @Mock
    IDmFopRecordDao iDmFopRecordDao;

    @Mock
    ILabelConfigDao iLabelConfigDao;

    @Mock
    AbstractConfigDataImporter importer;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    /**
     * Method: getHeadList()
     */
    @Test
    public void testGetHeadList() throws Exception {
        List<ExcelVO> headList = holisticViewConfigDataImporter.getHeadList();
        Assertions.assertEquals(false, headList.isEmpty());
    }

    @Test
    public void testLogErrorRecord() throws Exception {
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName("test.xlsx");
        uploadInfoVO.setFileKey("XXXXXXXXXXXXXXXXXXXXXX");
        uploadInfoVO.setUserId(1250L);
        uploadInfoVO.setFileSize(1250L);
        uploadInfoVO.setRowNumber(1);
        List dataList = new ArrayList<>();
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        holisticViewConfigDataVO.setArticulationFlag("SCENO1");
        HolisticViewConfigDataVO holisticViewConfigDataVO1 = new HolisticViewConfigDataVO();
        holisticViewConfigDataVO1.setArticulationFlag("SCENO2");
        HolisticViewConfigDataVO holisticViewConfigDataVO2 = new HolisticViewConfigDataVO();
        holisticViewConfigDataVO2.setArticulationFlag("SCENO3");
        HolisticViewConfigDataVO holisticViewConfigDataVO3 = new HolisticViewConfigDataVO();
        holisticViewConfigDataVO3.setArticulationFlag("NULL");
        dataList.add(holisticViewConfigDataVO);
        dataList.add(holisticViewConfigDataVO1);
        dataList.add(holisticViewConfigDataVO2);
        dataList.add(holisticViewConfigDataVO3);
        dataList.add(null);
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(), anyString(), anyString()).thenReturn("XXXXXXXXXXXXXXXX");
        holisticViewConfigDataImporter.logErrorRecord(uploadInfoVO, dataList);

        Assertions.assertNotNull(dataList);
    }
    @Test
    public void testLogErrorRecordA() throws Exception {
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName("test.xlsx");
        uploadInfoVO.setFileKey("XXXXXXXXXXXXXXXXXXXXXX");
        uploadInfoVO.setUserId(1250L);
        uploadInfoVO.setFileSize(1250L);
        uploadInfoVO.setRowNumber(1);
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(), anyString(), anyString()).thenReturn("XXXXXXXXXXXXXXXX");
        holisticViewConfigDataImporter.logErrorRecord(uploadInfoVO, null);
        Assertions.assertNotNull(uploadInfoVO);
    }
    @Test(expected = NullPointerException.class)
    public void testLogErrorRecordB() throws Exception {
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(), anyString(), anyString()).thenReturn("XXXXXXXXXXXXXXXX");
        holisticViewConfigDataImporter.logErrorRecord(null, null);

        Assertions.assertNotNull(new Object());
    }
    /**
     * Method: setVoDataInfoAndCheck(List<Object> dataList, String title, String value, AtomicInteger atomicInteger, Map<String, Object> keys, Map<String, Object> infoMap)
     */
    @Test
    public void testSetVoDataInfoAndCheck() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String[] titles = {"重量级团队LV1中文名", "重量级团队LV2中文名", "重量级团队LV3中文名", "L1名称", "预测场景","null"};
        String value = "Test";
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName", "TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);

        for (String title : titles) {
            holisticViewConfigDataImporter.setVoDataInfoAndCheck(dataList, title, value, new AtomicInteger(), key,
                new HashMap<>());
        }

        Assertions.assertNotNull(key);
    }

    @Test
    public void testSetVoDataInfoAndCheckA() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String[] titles = {"重量级团队LV1中文名", "重量级团队LV2中文名", "重量级团队LV3中文名", "L1名称", "预测场景","null"};
        String value = null;
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName", "TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);
        for (int i = 0; i < titles.length; i++) {
            holisticViewConfigDataImporter.setVoDataInfoAndCheck(dataList, titles[i], value, new AtomicInteger(), key,
                new HashMap<>());
        }
        Assertions.assertNotNull(key);
    }

    @Test
    public void testSetVoDataInfoAndCheckB() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String titles = "重量级团队LV1中文名";
        String value = "TEst";
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName", "TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);
        Map<String, Object> infpMap = new HashMap<>();
        infpMap.put("errorMsg", "Test");
        holisticViewConfigDataImporter.setVoDataInfoAndCheck(dataList, titles, value, new AtomicInteger(), key,
            infpMap);
        Assertions.assertNotNull(key);
    }

    @Test
    public void testSetVoDataInfoAndCheckC() throws Exception {
        List<Object> dataList = new ArrayList<>();
        String titles = "预测场景";
        String value = "场景一";
        Map<String, Object> key = new HashMap<>();
        ArrayList<String> strings = new ArrayList<>();
        strings.add("Test");
        key.put("lv1Name", strings);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("TestName", "TestCode");
        key.put("hierarchySortInfoLv1", stringStringHashMap);
        Map<String, Object> infpMap = new HashMap<>();
        infpMap.put("errorMsg", "Test");
        holisticViewConfigDataImporter.setVoDataInfoAndCheck(dataList, titles, value, new AtomicInteger(), key,
            infpMap);
        Assertions.assertNotNull(key);
    }
    /**
     * Method: setNormalInfoAndCheckRepeated(List<Object> dataList, Map<String, Object> dataMap, AtomicInteger counterRef, Map<String, Object> preparedInfo)
     */
    @Test
    public void testSetNormalInfoAndCheckRepeated() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三", "NULL"};
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            dataMap.put("lv1Name","Test");
            dataMap.put("lv2Name","");
            dataMap.put("lv3Name","");
            dataMap.put("l1Name","Test");
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap,
                new AtomicInteger(), new HashMap<>());
        }
        Assertions.assertNotNull(strings);
    }

    @Test
    public void testSetNormalInfoAndCheckRepeatedB() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());

        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("3");
            list.add(holisticViewConfigDataVO);
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("hierarchySortLv2",list);
            objectObjectHashMap.put("hierarchySortLv3",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }

    @Test
    public void testSetNormalInfoAndCheckRepeatedC() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("");
            list.add(holisticViewConfigDataVO);
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("hierarchySortLv2",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedD() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv2",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }

    @Test
    public void testSetNormalInfoAndCheckRepeatedE() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "Test");
            objectObjectHashMap.put("hierarchySortLv2",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }

    @Test
    public void testSetNormalInfoAndCheckRepeatedF() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());

        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("");
            holisticViewConfigDataVO.setLv3Name("3");
            list.add(holisticViewConfigDataVO);
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            dataMap.put("lv1Name","Test");
            dataMap.put("lv3Name","Test");
            dataMap.put("l1Name","Test");
            dataMap.put("errorMsg","Test");
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("hierarchySortLv2",list);
            objectObjectHashMap.put("hierarchySortLv3",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedG() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());

        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("3");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("hierarchySortLv2",list);
            objectObjectHashMap.put("hierarchySortLv3",list);
            dataMap.put("errorMsg","Test");
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedH() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());

        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("3");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("hierarchySortLv2",list);
            objectObjectHashMap.put("hierarchySortLv3",list);
            dataMap.put("errorMsg","");
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedZ() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            holisticViewConfigDataVO.setLv2Name("3");
            list.add(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv2",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedI() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            holisticViewConfigDataVO.setLv1Name("3");
            list.add(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv2",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedJ() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            holisticViewConfigDataVO.setLv1Name("3");
            holisticViewConfigDataVO.setLv2Name("4");
            list.add(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv2",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedK() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("3");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            holisticViewConfigDataVO.setLv1Name("3");
            list.add(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv3",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedL() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("3");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            holisticViewConfigDataVO.setLv2Name("3");
            list.add(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv3",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedM() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("3");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            holisticViewConfigDataVO.setLv3Name("4");
            list.add(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv3",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedN() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("3");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            holisticViewConfigDataVO.setLv1Name("3");
            holisticViewConfigDataVO.setLv2Name("4");
            list.add(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv3",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedO() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("3");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            holisticViewConfigDataVO.setLv1Name("4");
            holisticViewConfigDataVO.setLv3Name("5");
            list.add(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv3",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedP() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("3");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            holisticViewConfigDataVO.setLv2Name("4");
            holisticViewConfigDataVO.setLv3Name("5");
            list.add(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv3",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testSetNormalInfoAndCheckRepeatedQ() throws Exception {
        mockStatic(CommUtils.class);
        PowerMockito.doNothing().when(CommUtils.class, "setTimeCreatorInfoAndCheckRepeated", any(), any(), any(),anyString());
        HolisticViewConfigDataVO holisticViewConfigDataVO = new HolisticViewConfigDataVO();
        String[] strings = new String[] {"场景一", "场景二", "场景三"};
        List<Object> list = new ArrayList<>();
        for (String string : strings) {
            holisticViewConfigDataVO.setArticulationFlag(string);
            holisticViewConfigDataVO.setLv1Name("1");
            holisticViewConfigDataVO.setLv2Name("2");
            holisticViewConfigDataVO.setLv3Name("3");
            Map<String, Object> dataMap = BeanUtil.beanToMap(holisticViewConfigDataVO);
            holisticViewConfigDataVO.setLv1Name("4");
            holisticViewConfigDataVO.setLv2Name("5");
            holisticViewConfigDataVO.setLv3Name("6");
            list.add(holisticViewConfigDataVO);
            HashMap<String, Object> objectObjectHashMap = new HashMap<>();
            dataMap.put("errorMsg", "");
            objectObjectHashMap.put("hierarchySortLv3",list);
            holisticViewConfigDataImporter.setNormalInfoAndCheckRepeated(new ArrayList<>(), dataMap, new AtomicInteger(), objectObjectHashMap);
        }
        Assertions.assertNotNull(strings);
    }
    @Test
    public void testGetLegalTitleMap() throws Exception {
        holisticViewConfigDataImporter.getLegalTitleMap();
        Assertions.assertNotNull(new Object());
    }
}
