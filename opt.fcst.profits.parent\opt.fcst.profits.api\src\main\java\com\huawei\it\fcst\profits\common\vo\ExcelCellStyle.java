/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.vo;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.awt.Color;

/**
 * ExcelCellStyle
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public class ExcelCellStyle {
    /**
     * 边框样式
     */
    private BorderStyle borderBottom;
    private BorderStyle borderLeft;
    private BorderStyle borderTop;
    private BorderStyle borderRight;

    /**
     * 水平方位
     */
    private HorizontalAlignment horizontalAlignment ;

    // 垂直方位
    private VerticalAlignment verticalAlignment;

    // 字体是否粗体
    private Boolean fontBold ;

    // 字体名称
    private String fontName;

    public Boolean getFontBold() {
        return fontBold;
    }

    // 字体大小
    private Integer fontHeightInPoints;

    // 字体颜色
    private Integer fontColor;

    // 单元格填充风格
    private FillPatternType fillPatternType;

    // 单元格填充颜色
    private Color fillColor;

    public BorderStyle getBorderBottom() {
        return borderBottom;
    }

    public void setBorderBottom(BorderStyle borderBottom) {
        this.borderBottom = borderBottom;
    }

    public BorderStyle getBorderLeft() {
        return borderLeft;
    }

    public void setBorderLeft(BorderStyle borderLeft) {
        this.borderLeft = borderLeft;
    }

    public BorderStyle getBorderTop() {
        return borderTop;
    }

    public void setBorderTop(BorderStyle borderTop) {
        this.borderTop = borderTop;
    }

    public BorderStyle getBorderRight() {
        return borderRight;
    }

    public void setBorderRight(BorderStyle borderRight) {
        this.borderRight = borderRight;
    }

    public HorizontalAlignment getHorizontalAlignment() {
        return horizontalAlignment;
    }

    public void setHorizontalAlignment(HorizontalAlignment horizontalAlignment) {
        this.horizontalAlignment = horizontalAlignment;
    }

    public VerticalAlignment getVerticalAlignment() {
        return verticalAlignment;
    }

    public void setVerticalAlignment(VerticalAlignment verticalAlignment) {
        this.verticalAlignment = verticalAlignment;
    }

    public Boolean isFontBold() {
        return fontBold;
    }

    public void setFontBold(Boolean fontBold) {
        this.fontBold = fontBold;
    }

    public String getFontName() {
        return fontName;
    }

    public void setFontName(String fontName) {
        this.fontName = fontName;
    }

    public Integer getFontHeightInPoints() {
        return fontHeightInPoints;
    }

    public void setFontHeightInPoints(Integer fontHeightInPoints) {
        this.fontHeightInPoints = fontHeightInPoints;
    }

    public Integer getFontColor() {
        return fontColor;
    }

    public void setFontColor(Integer fontColor) {
        this.fontColor = fontColor;
    }

    public FillPatternType getFillPatternType() {
        return fillPatternType;
    }

    public void setFillPatternType(FillPatternType fillPatternType) {
        this.fillPatternType = fillPatternType;
    }

    public Color getFillColor() {
        return fillColor;
    }

    public void setFillColor(Color fillColor) {
        this.fillColor = fillColor;
    }

    @Override
    public String toString() {
        return "ExcelCellStyle{" +
                "borderBottom=" + borderBottom +
                ", borderLeft=" + borderLeft +
                ", borderTop=" + borderTop +
                ", borderRight=" + borderRight +
                ", horizontalAlignment=" + horizontalAlignment +
                ", verticalAlignment=" + verticalAlignment +
                ", fontBold=" + fontBold +
                ", fontName='" + fontName + '\'' +
                ", fontHeightInPoints=" + fontHeightInPoints +
                ", fontColor=" + fontColor +
                ", fillPatternType=" + fillPatternType +
                ", fillColor=" + fillColor +
                '}';
    }
}
