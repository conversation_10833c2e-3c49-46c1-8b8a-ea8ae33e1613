/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.poi;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import freemarker.template.utility.StringUtil;

import java.util.Date;

/**
 * The class JxlsUtils.java
 *
 * <AUTHOR>
 * @since 2021年7月12日
 */
public class JxlsUtils {
    /**
     * [服务名称]dateFmt
     *
     * @param date 入参
     * @param fmt 入参
     * @return String
     * <AUTHOR>
     */
    public String dateFmt(Date date, String fmt) {
        return DateUtil.format(date, fmt);
    }

    /**
     * [服务名称]ifelse
     *
     * @param b0 入参
     * @param o1 入参
     * @param o2 入参
     * @return Object
     * <AUTHOR>
     */
    public Object ifelse(boolean b0, Object o1, Object o2) {
        return b0 ? o1 : o2;
    }

    /**
     * [服务名称]formatNumber
     *
     * @param value 入参
     * @param fmt 入参
     * @return Object
     * <AUTHOR>
     */
    public Object formatNumber(Object value, String fmt) {
        return StrUtil.isBlankIfStr(value) ? value : parseBFB(StrUtil.toString(value), fmt);
    }

    /**
     * [服务名称]formatNumber
     *
     * @param value 入参
     * @param fmt 入参
     * @param flag 入参
     * @return Object
     * <AUTHOR>
     */
    public Object formatNumber(Object value, String fmt, boolean flag) {
        Object obj = formatNumber(value, fmt);
        return flag ? (StrUtil.equals(StrUtil.toString(obj), "0%") ? "-" : obj) : obj;
    }

    private Object parseBFB(String obj, String fmt) {
        if (StrUtil.containsAny(obj, BFH)) {
            return (NumberUtil.parseNumber(StringUtil.replace(obj, BFH, "")) + BFH);
        }
        if (NumberUtil.isNumber(obj)) {
            return NumberUtil.parseNumber(obj);
        }
        return StrUtil.replace(obj, fmt, "");
    }

    static final String BFH = "%";
}
