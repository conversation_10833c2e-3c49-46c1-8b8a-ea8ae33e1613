/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * The Entity of HolisticViewConfigDataVO
 * @since 2022-10-17 19:38:39
 */

@Getter
@Setter
public class HolisticViewConfigDataVO extends LabelConfigVo {
    private static final long serialVersionUID = 1L;

    /**
     * lv1代码
     **/
    private String lv1Code;

    /**
     * lv2代码
     **/
    private String lv2Code;

    /**
     * lv2名称
     **/
    private String lv2Name;

    /**
     * lv3代码
     **/
    private String lv3Code;

    /**
     * lv3名称
     **/
    private String lv3Name;

    /**
     * l1名称
     **/
    private String l1Name;

    /**
     * 人工标识
     */
    private String articulationFlag;

    /**
     * 工业类型
     */
    private String industryType;

    // 分析场景
    private String analysisFlag;

    @Override
    public boolean equals(Object other) {
        //	先判断自反性
        if(other == this) {
            return true;
        }

        //  判断同类型，同时也满足了第五条（null）
        if(!(other instanceof HolisticViewConfigDataVO)) {
            return false;
        }

        //  最后再进行类型转换，比较值
        HolisticViewConfigDataVO that = (HolisticViewConfigDataVO) other;
        return that.getLv1Name().equals(this.getLv1Name()) && that.lv2Name.equals(this.lv2Name) && that.lv3Name.equals(
            this.lv3Name) ;
    }

    @Override
    public int hashCode() {
        return Objects.hash(lv1Code,lv2Name,lv3Name,l1Name);
    }
}
