/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.request;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.PeriodTypeEnum;
import com.huawei.it.fcst.profits.common.enums.PredictionEnum;
import com.huawei.it.fcst.profits.common.utils.CalcUtils;
import com.huawei.it.fcst.profits.common.utils.DateUtil;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.vo.response.SpartInfoVo;
import com.huawei.it.jalor5.core.util.StringUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Arrays;
import java.util.Calendar;
import java.util.LinkedList;
import java.util.List;

/**
 * 预算预测 ForecastsRequest
 *
 * <AUTHOR>
 * @since 2022-10-06
 */
@Getter
@Setter
public class ForecastsRequest extends PageRequest {
    /**
     * 会计期
     **/
    @Max(value = 999912 ,message = "不在有效的会计期范围内")
    @Min(value = 201801 ,message = "不在有效的会计期范围内")
    private long periodId;

    /**
     * 预测步长-年度、半年度
     */
    private String fcstStep;

    /**
     * 年度     Y
     * 半年度   H
     * 月度     M
     * 季度     Q
     */
    private String tabGraphType;

    /**
     * 预测方法 分月法、年度法、年度平均法、时序法
     */
    private String fcstType;

    /**
     * SOP期次
     */
    private String phaseDate;

    /**
     * BG编码
     **/
    private String[] bgCode;

    /**
     * 产品lv1
     **/
    private String[] lv1Codes;

    private String lv1Code;

    /**
     * 产品lv1
     **/
    private String lv2Code;

    /**
     * L1
     */
    private String l1Name;

    /**
     * L2
     */
    private String[] l2Name;

    private List<String> lv1s;

    private List<String> lv2s;

    private List<String> l2Names;

    private List<String> l1Names;

    private List<String> bgCodes;

    private List<String> targetPeriods;

    private List<String> lv2Codes;

    private Integer topNum;

    private String predictionType;

    private String targetPeriod;

    private String fcstStepPeriod;

    private String actStepPeriod;

    private List<String> lv1Names;

    private String targetFlag;

    private String targetType;

    private String level;

    private List<String> actPeriods;

    private List<String> fcstPeriods;

    private List<String> maxPeriods;

    private List<String> specPeriods;

    private List<String> monthFcstPeriods;

    private String specYear;

    private int curYear;

    private int curMonth;

    private String includeFlag;
    @Max(value = Integer.MAX_VALUE ,message = "角色ID值不正确")
    @Min(value = 0 ,message = "角色ID值不正确")
    private int roleId;

    private DataPermissionsVO rightVO;

    private String orderColum;

    private String orderField;

    private String orderType;

    private String orderTargetPeriodId;

    private String optType;

    private String specFlag;

    private String auto2Year;

    private String queryType;

    private String specAutoTarget;

    // Spart成本相关code信息
    private List<SpartInfoVo> costItems;

    // Spart收入相关code信息
    private List<SpartInfoVo> revItems;

    // 国内外标志
    private String overseaDesc;

    // 展示名称拼接逻辑
    private List<String> displayName;

    private List<String> l3Names;

    // 预测，预算 类型区分（FCST，BUDGET）
    private String dataType;

    // 随机数赋值
    private int random;

    /**
     * ai 融合数据类型（集团分析师，产业分析师）
     */
    private String combinedExpertType;

    /**
     * 设置年度预算，区域默认值
     * @return 请求对象
     */
    public ForecastsRequest withOverseaDescValue(){
        if (StringUtils.equals(this.getPredictionType(), PredictionEnum.YEAR.getCode())) {
            if(StringUtil.isNullOrEmpty(this.overseaDesc)){
                this.setOverseaDesc("全球");
            }
        }
        return this;
    }

    /**
     * 设置 步长，方法对字段赋值
     * @return 当前对象
     */
    public ForecastsRequest buildFcstStepQueryParam() {
        // 转换前段传值预测步长方法映射值
        CalcUtils.setFcstTypeValue(this);
        Calendar calendar = DateUtil.getPeriodCalendar(String.valueOf(this.getPeriodId()));
        this.setCurYear(calendar.get(Calendar.YEAR));
        this.setCurMonth(calendar.get(Calendar.MONTH)+1);
        this.setMaxPeriods(Arrays.asList(String.valueOf(this.getCurYear())));
        this.setIncludeFlag("1");
        LinkedList<String> actTargets = new LinkedList<>();
        LinkedList<String> fcstTargets = new LinkedList<>();
        // 设置年度预测页签查询条件
        setAnnualForecastsParam(calendar, actTargets, fcstTargets);
        // 设置月度预测页签查询条件
        setMonthlyForecastsParam(calendar, actTargets, fcstTargets);
        return this;
    }

    private void setMonthlyForecastsParam(Calendar calendar, LinkedList<String> actTargets, LinkedList<String> fcstTargets) {
        if (!StringUtils.equals(this.getPredictionType(), PredictionEnum.MONTH.getCode())) {
            return;
        }
        // 设置固定值，封装视图，有重复数据，查询设置固定值去对应的数据
        this.dataType = "FCST";
        actTargets.add(String.valueOf(PeriodTypeEnum.CUR_YEAR_SUM.getDesc()));
        actTargets.add(String.valueOf(PeriodTypeEnum.LAST_YEAR_PERIOD.getDesc()));
        // 年度
        if (StringUtils.equals(CommonConstant.FCST_YEAR_STEP, this.getFcstStep())) {
            actTargets.add(String.valueOf(calendar.get(Calendar.YEAR) - 1));
            fcstTargets.add(String.valueOf(this.curYear));
            this.setActPeriods(actTargets);
            this.setFcstPeriods(fcstTargets);
        }
        // 半年度处理
        if (StringUtils.equals(CommonConstant.FCST_HALF_YEAR_STEP, this.getFcstStep())) {
            this.setActStepPeriod(DateUtil.getLastYearSemiAnnual(String.valueOf(this.getPeriodId())));
            this.setFcstStepPeriod(DateUtil.getYearSemiAnnual(String.valueOf(this.getPeriodId())));
            this.setMaxPeriods(Arrays.asList(this.getFcstStepPeriod()));
            actTargets.add(this.getActStepPeriod());
            fcstTargets.add(this.getFcstStepPeriod());
            this.setActPeriods(actTargets);
            this.setFcstPeriods(fcstTargets);
        }
        // 季度处理
        if (StringUtils.equals(CommonConstant.FCST_QUARTER_STEP, this.getFcstStep())) {
            this.setActStepPeriod(DateUtil.getLastQuarterTime(String.valueOf(this.getPeriodId())));
            this.setFcstStepPeriod(DateUtil.getQuarterTime(String.valueOf(this.getPeriodId())));
            this.setMaxPeriods(Arrays.asList(this.getFcstStepPeriod()));
            actTargets.add(this.getActStepPeriod());
            fcstTargets.add(this.getFcstStepPeriod());
            this.setActPeriods(actTargets);
            this.setFcstPeriods(fcstTargets);
        }
    }

    private void setAnnualForecastsParam(Calendar calendar, LinkedList<String> actTargets, LinkedList<String> fcstTargets) {
        if (!StringUtils.equals(this.getPredictionType(), PredictionEnum.YEAR.getCode())) {
            return;
        }
        Calendar systemCruYear = DateUtil.getPeriodCalendar(null);
        int periodYear = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH)+1;
        this.setCurYear(periodYear);
        this.setCurMonth(month);
        if(periodYear == systemCruYear.get(Calendar.YEAR) && month < 10){
            actTargets.add(String.valueOf(calendar.get(Calendar.YEAR) - 1));
            fcstTargets.add(String.valueOf(periodYear));
            fcstTargets.add(String.valueOf(periodYear));
            this.auto2Year = String.valueOf(periodYear);
            this.maxPeriods = Arrays.asList(String.valueOf(periodYear));
        }else {
            actTargets.add(String.valueOf(calendar.get(Calendar.YEAR) - 1));
            fcstTargets.add(String.valueOf(calendar.get(Calendar.YEAR)));
            fcstTargets.add(String.valueOf(calendar.get(Calendar.YEAR) + 1));
            this.auto2Year = String.valueOf(periodYear+1);
            this.maxPeriods = Arrays.asList(String.valueOf(periodYear+1));
        }
        this.setActPeriods(actTargets);
        this.setFcstPeriods(fcstTargets);
        this.setSpecAutoTarget(String.valueOf(periodYear));
    }
}
