/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.vo;

import com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * TreeNodeVo Class
 *
 * <AUTHOR>
 * @since 2022-09-24
 */
@Getter
@Setter
@NoArgsConstructor
public class TreeNodeVo {
    private static final long serialVersionUID = 1L;

    /**
     * id
     **/
    private Long id;

    /**
     * 父id
     **/
    private Long pid;

    /**
     * listId树结点对应的list中的id
     */
    private Long listId;

    /**
     * 树结点的显示名称
     */
    private String label;

    /**
     * 树结点的code
     */
    private String code;

    /**
     * 是否在树结果上显示一个复选框，说明这个结果可以选择
     */
    private Boolean isCanSelect;

    /**
     * 层级，指产品的维度
     */
    private String dimensionLevel;

    /**
     * 子结点集合
     */
    private List<TreeNodeVo> children;

    /**
     * nodeValue
     */
    private List<KrCpfLv1AggrResponse> nodeValues;

}
