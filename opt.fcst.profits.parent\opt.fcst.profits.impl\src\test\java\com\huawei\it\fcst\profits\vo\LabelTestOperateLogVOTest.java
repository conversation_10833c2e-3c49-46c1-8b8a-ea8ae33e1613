/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.vo;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class LabelTestOperateLogVOTest extends BaseVOCoverUtilsTest<LabelOperateLogVO> {
	 
	    @Override
	    protected Class<LabelOperateLogVO> getTClass() {
	        return LabelOperateLogVO.class;
	    }

		@Test
		void getLabelOperateLogVOTest(){
			//<init>(String, String, Boolean, Long, String, int, int, String)
			LabelOperateLogVO labelOperateLogVO = new LabelOperateLogVO("test","",Boolean.TRUE,999999L,"test-1",1,1,"SUCCESS");
			Assertions.assertEquals("test-1",labelOperateLogVO.getObjectId());
		}


	@Test
	void getLabelOperateLogVObuild(){
		//<init>(String, String, Bo<PERSON>an, Long, String, int, int, String)
		final LabelOperateLogVO builder = LabelOperateLogVO.builder().status("START").objectId("test").module("import").userId(999999L).countNum(1).upsertNum(1).isSkip(Boolean.TRUE).methodName("test").build();
		Assertions.assertEquals("START",builder.getStatus());
	}
	}