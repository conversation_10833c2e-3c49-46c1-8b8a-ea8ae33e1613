/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.config;

import com.huawei.his.jalor.store.service.s3.S3Source;
import com.huawei.his.jalor.store.service.s3.S3StoreService;
import com.huawei.it.his.jalor.store.IStoreService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * S3CustomConfig
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
@Configuration
public class S3CustomConfig {
    @Value("jalor.store.s3.ak")
    private String ak;

    @Value("jalor.store.s3.sk")
    private String sk;

    @Value("jalor.store.s3.bucketname")
    private String bucketName;

    @Value("jalor.store.s3.endpoint")
    private String endpoint;

    /**
     * storeService
     *
     * @return storeService
     */
    @Bean("s3-store-service")
    public IStoreService storeService() {
        S3Source s3Source = new S3Source();
        s3Source.setAk(ak);
        s3Source.setSk(sk);
        s3Source.setEndpoint(endpoint);
        s3Source.setBucketName(bucketName);
        return new S3StoreService(s3Source);
    }
}
