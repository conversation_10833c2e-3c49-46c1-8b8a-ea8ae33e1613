/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.thread;

import com.huawei.it.fcst.profits.common.constants.Constants;
import com.huawei.it.fcst.profits.common.constants.RelationConstants;
import com.huawei.it.fcst.profits.common.utils.CalcUtils;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.dao.ILabelOperateLogDao;
import com.huawei.it.fcst.profits.dao.ISpartProfitingRelationDao;
import com.huawei.it.fcst.profits.vo.LabelOperateLogVO;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.jalor5.core.ioc.Jalor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 标签功能
 *
 * <AUTHOR>
 * @since 2022/10/02.
 */
public class SpartRelationMainSubmitThread implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(SpartRelationMainSubmitThread.class);

    private Long userId;

    private ExecutorService executorService;

    private LabelOperateLogVO labelOperateLogVO;

    private ILabelOperateLogDao operateLogDao;

    public SpartRelationMainSubmitThread(LabelOperateLogVO labelOperateLogVO, Long userId, ExecutorService executorService) {
        this.userId = userId;
        this.executorService = executorService;
        this.labelOperateLogVO = labelOperateLogVO;
        this.operateLogDao = Jalor.getContext().getBean(ILabelOperateLogDao.class);
    }

    @Override
    public void run() {
        synchronized (SpartRelationMainSubmitThread.class) {
            Object relationDao = Jalor.getContext().getBean("ISpartProfitingRelationDao");
            String status = Constants.SUCCESS.getValue();
            try {
                Timestamp curTime = TimeUtils.getCurTime();
                ((ISpartProfitingRelationDao) relationDao).copyList(userId);
                CopyOnWriteArrayList<SpartProfitingRelationVO> totalModifyList = new CopyOnWriteArrayList<>();
                while (((ISpartProfitingRelationDao) relationDao).findCopyDataCount(null,userId) > 0) {
                    List<SpartProfitingRelationVO> copyDataList = ((ISpartProfitingRelationDao) relationDao).findCopyDataByParam("0",userId);
                    if (CollectionUtils.isEmpty(copyDataList)) {
                        break;
                    }
                    int batchNum = copyDataList.size() / RelationConstants.BATCH_COUNT;
                    updateCopyStatus(batchNum, copyDataList, relationDao);
                    AtomicReference<CopyOnWriteArrayList<SpartProfitingRelationVO>> atomicReference = new AtomicReference<>();
                    CopyOnWriteArrayList<SpartProfitingRelationVO> modifyList = new CopyOnWriteArrayList<>();
                    atomicReference.set(modifyList);
                    // 加门栓
                    final CountDownLatch countDownLatch = new CountDownLatch(CalcUtils.getLatchNum(copyDataList.size(), batchNum));
                    List<SpartProfitingRelationVO> subList = null;
                    for (int i = 0; i < batchNum; i++) {
                        subList = copyDataList.subList(i * RelationConstants.BATCH_COUNT, (i + 1) * RelationConstants.BATCH_COUNT);
                        executorService.execute(new SpartRelationSubmitThread(labelOperateLogVO,userId, subList, atomicReference, countDownLatch, curTime));
                    }
                    if (copyDataList.size() % RelationConstants.BATCH_COUNT > 0) {
                        subList = copyDataList.subList(batchNum * RelationConstants.BATCH_COUNT, copyDataList.size());
                        executorService.execute(new SpartRelationSubmitThread(labelOperateLogVO,userId, subList, atomicReference, countDownLatch, curTime));
                    }
                    countDownLatch.await();
                    totalModifyList.addAll(atomicReference.get());
                }
                executorService.execute(new SpartRelationModifyThread(userId, curTime, totalModifyList));
            } catch (Exception ex) {
                status = Constants.FAIL.getValue();
                logger.error("SpartRelationMainSubmitThread occurs error: {}", ex);
            }finally {
                // 执行最后删除本次copy 数据防止线程异常，数据存放，下次提交数据重复
                ((ISpartProfitingRelationDao) relationDao).delCopyListByUser(userId);
                upsertOperateStatus(status);
            }
        }
    }

    /**
     * 批量更新临时处理数据
     *
     * @param batchNum
     * @param copyDataList
     * @param relationDao
     */
    public void updateCopyStatus(int batchNum, List<SpartProfitingRelationVO> copyDataList, Object relationDao) {
        List<SpartProfitingRelationVO> tempList = null;
        for (int i = 0; i < batchNum; i++) {
            tempList = copyDataList.subList(i * RelationConstants.BATCH_COUNT, (i + 1) * RelationConstants.BATCH_COUNT);
            ((ISpartProfitingRelationDao) relationDao).updateCopyStatus(tempList);
        }
        if (copyDataList.size() % RelationConstants.BATCH_COUNT > 0) {
            tempList = copyDataList.subList(batchNum * RelationConstants.BATCH_COUNT, copyDataList.size());
            ((ISpartProfitingRelationDao) relationDao).updateCopyStatus(tempList);
        }
    }

    private void upsertOperateStatus(String status) {
        operateLogDao.updateUpsertNum(LabelOperateLogVO.builder().objectId(labelOperateLogVO.getObjectId()).status(status).build());
    }

}
