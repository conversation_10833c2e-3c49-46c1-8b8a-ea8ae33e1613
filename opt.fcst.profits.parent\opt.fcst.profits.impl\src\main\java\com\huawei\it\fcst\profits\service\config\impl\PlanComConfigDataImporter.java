/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.config.impl;

import com.huawei.it.fcst.profits.comm.AbstractConfigDataImporter;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.LabelConfigEnum;
import com.huawei.it.fcst.profits.common.enums.OptTypeEnum;
import com.huawei.it.fcst.profits.common.enums.RecStsEnum;
import com.huawei.it.fcst.profits.common.utils.CalcUtils;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.ExcelVO;
import com.huawei.it.fcst.profits.common.vo.UploadInfoVO;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.utils.HeaderTitleUtils;
import com.huawei.it.fcst.profits.utils.HeaderUtils;
import com.huawei.it.fcst.profits.vo.PlanComConfigDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import cn.hutool.core.bean.BeanUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
public class PlanComConfigDataImporter extends AbstractConfigDataImporter {

    @Override
    public List<ExcelVO> getHeadList() {
        return HeaderUtils.buildExcelVO(HeaderTitleUtils.creatImportHeaderList(LabelConfigEnum.PLAN_COM.getCode()));
    }

    @Override
    public void logErrorRecord(UploadInfoVO uploadInfoVO, List verifyDataList) throws CommonApplicationException {
        String errorFileKey = uploadInfoVO.getFileKey();
        Map params = new ConcurrentHashMap<>();
        params.put("fileName", CommonConstant.LABEL_CONFIG_EXCEPTION1 + uploadInfoVO.getFileName());
        params.put("dataType", "PLAN_COM");
        params.put("userId", String.valueOf(uploadInfoVO.getUserId()));
        if (CollectionUtils.isNotEmpty(verifyDataList)) {
            buildExportStreamAndUpload(params, String.valueOf(uploadInfoVO.getUserId()), verifyDataList,
                HeaderTitleUtils.creatErrorHeaderList("PLAN_COM"));
            if (Objects.nonNull(params.get("fileKey"))) {
                errorFileKey = String.valueOf(params.get("fileKey"));
            }
        }
        creatRecord(String.valueOf(params.get("fileName")), uploadInfoVO.getFileSize(), "Save", CommonConstant.ERROR_INPUT_DATA,
            uploadInfoVO.getRowNumber(), TimeUtils.getCurPeriod(), "", errorFileKey, OptTypeEnum.IMPORT.getCode(),
            RecStsEnum.FAIL.getCode(), CommonConstant.LABEL_CONFIG_MODULE_NAME3);
    }

    @Override
    public void setNormalInfoAndCheckRepeated(List<Object> dataList, Map<String, Object> dataMap,
        AtomicInteger counterRef, Map<String, Object> preparedInfo) {
        PlanComConfigDataVO dataVO = BeanUtil.fillBeanWithMap(dataMap, new PlanComConfigDataVO(), false);
        CommUtils.setTimeCreatorInfoAndCheckRepeated(dataList, counterRef, dataVO,
            CommonConstant.LABEL_CONFIG_PLAN_COM_REPEATED_MESSAGE);
    }

    @Override
    public void setVoDataInfoAndCheck(List<Object> dataList, String title, String value, AtomicInteger atomicInteger,
        Map<String, Object> keys, Map<String, Object> objectMap) {
        StringBuilder builder = new StringBuilder();
        keys.put("dataType","PLAN_COM");
        checkField(title, value, atomicInteger, objectMap, builder, keys);
    }
    private void checkField(String title, String value, AtomicInteger atomicInteger, Map<String, Object> objectMap,
        StringBuilder builder, Map<String, Object> keys) {
        List lv1Name = (List) keys.get("lv1Name");
        List<String> l1Name = (List<String>)keys.get("l1NameList");
        Map<String,String> hierarchySortInfoLv1 = (Map<String,String>)keys.get("hierarchySortInfoLv1");
        switch (title) {
            case "产业":
                objectMap.put("lv1Name", value);
                if (ObjectUtils.isEmpty(objectMap.get("lv1Name"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING2);
                } else if (!lv1Name.contains(objectMap.get("lv1Name"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING1);
                } else {
                    objectMap.put("lv1Code", hierarchySortInfoLv1.get(value));
                }break;
            case "一级计委包":
                objectMap.put("planComLv1", value);
                if (ObjectUtils.isEmpty(objectMap.get("planComLv1"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING6);
                }break;
            case "二级计委包":
                objectMap.put("planComLv2", value);
                if (ObjectUtils.isEmpty(objectMap.get("planComLv2"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING7);
                }break;
            case "三级计委包":
                objectMap.put("planComLv3", value); break;
            case "四级业务包":
                objectMap.put("busiLv4", value); break;
            case "L1名称":
                objectMap.put("l1Name", value);
                if (ObjectUtils.isEmpty(objectMap.get("l1Name"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING3);
                } else if (!l1Name.contains(value)) { // 增加对l1Name的校验，必须在全景图维护的l1Name中
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING13);
                }break;
            case "L2名称":
                objectMap.put("l2Name", value);
                if (ObjectUtils.isEmpty(objectMap.get("l2Name"))) {
                    builder.append(CommonConstant.LABEL_CONFIG_WARNING4);
                }break;
            case "L2系数":
                objectMap.put("l2Coefficient", value);
                if (!(Objects.nonNull(value) && CalcUtils.validateDataFormat(value))) {
                    builder.append("L2系数错误:正数或小数位最多保留六位小数;");
                }break;
            default: return;
        }
        checkErrorValue(atomicInteger, objectMap, builder);
    }

    @Override
    public Map<String, ExcelVO> getLegalTitleMap() {
        List<ExcelVO> planImportHeaderList = this.getHeadList();
        return planImportHeaderList.stream().collect(Collectors.toMap(k -> k.getHeadName(), v -> v));
    }
}
