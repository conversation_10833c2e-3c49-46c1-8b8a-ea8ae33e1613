/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.huawei.it.fcst.profits.common.utils.FcstJsonFormatVisitorWrapper;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * The Entity of KrCpfLv1AggrResponse
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:26:58
 */
@Getter
@Setter
public class KrCpfLv1AggrResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SOP期次
     */
    private String phaseDate;

    /**
     * 重量级团队LV1编码
     **/
    private String lv1Code;

    /**
     * 对价后设备收入额
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal equipRevAfter;

    /**
     * 对价后制毛率
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal mgpRateAfter;

    /**
     * BG编码
     **/
    private String bgCode;

    /**
     * L1名称
     **/
    private String l1Name;

    /**
     * 重量级团队LV1描述
     **/
    private String lv1Name;

    /**
     * BG名称
     **/
    private String bgName;

    /**
     * 场景
     **/
    private String targetPeriod;

    /**
     * 币种
     **/
    private String currency;

    /**
     * 会计期
     **/
    private Long periodId;

    /**
     * 重量级团队LV2名称
     **/
    private String lv2Name;

    /**
     * 重量级团队LV2编码
     **/
    private String lv2Code;

    /**
     * 分组key
     **/
    private String groupKey;

    /**
     * 场景标记
     **/
    private String articulationFlag;

    private KrCpfLv1AggrResponse aiAuto;

    private KrCpfLv1AggrResponse lastYearActure;

    private KrCpfLv1AggrResponse curYearSum;

    private KrCpfLv1AggrResponse lastYearPeriod;

    private KrCpfLv1AggrResponse cnbgLastYearActure;

    private KrCpfLv1AggrResponse cnbgCurYearSum;

    private KrCpfLv1AggrResponse cnbgLastYearPeriod;

    private KrCpfLv1AggrResponse ebgLastYearActure;

    private KrCpfLv1AggrResponse cnbgAiAuto;

    private KrCpfLv1AggrResponse ebgCurYearSum;

    private KrCpfLv1AggrResponse ebgLastYearPeriod;

    private KrCpfLv1AggrResponse ebgAiAuto;

    private KrCpfLv1AggrResponse ai2Auto;

    private KrCpfLv1AggrResponse cnbgAi2Auto;

    private KrCpfLv1AggrResponse ebgAi2Auto;

    private List<KrCpfLv1AggrResponse> children;

    // 区分预测，预算
    private String dataType;

    // 融合分析师 ,(集团分析师，产业分析师)
    private String combinedExpertType;

    // 集团分析师 ("groupAnalyst", "PROD0002"),
    private KrCpfLv1AggrResponse groupAnalyst;

    // 融合分析师-集团("combinedExpertGroup", "PROD0002"),
    private KrCpfLv1AggrResponse combinedExpertGroup;

    // 融合分析师 ("combinedExpert", "PROD0002"),
    private KrCpfLv1AggrResponse combinedExpert;

    // 融合分析师ebg("ebgCombinedExpert", "PDCG901160");
    private KrCpfLv1AggrResponse ebgCombinedExpert;

    // 融合分析师 cnbf("cnbgCombinedExpert", "PDCG901159"),
    private KrCpfLv1AggrResponse cnbgCombinedExpert;
}
