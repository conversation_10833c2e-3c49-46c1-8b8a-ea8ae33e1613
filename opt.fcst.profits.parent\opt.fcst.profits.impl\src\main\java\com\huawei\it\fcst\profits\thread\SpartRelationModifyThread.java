/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.thread;

import com.huawei.it.fcst.profits.comm.ExportService;
import com.huawei.it.fcst.profits.common.enums.ModuleEnum;
import com.huawei.it.fcst.profits.common.enums.SaveTypeEnum;
import com.huawei.it.fcst.profits.dao.ILabelModifyDao;
import com.huawei.it.fcst.profits.service.IExportService;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.vo.LabelModifyVO;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class SpartRelationModifyThread implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(SpartRelationModifyThread.class);

    private Long userId;

    private Timestamp curTime;

    private CopyOnWriteArrayList<SpartProfitingRelationVO> modifyList;

    public SpartRelationModifyThread(Long userId, Timestamp curTime, CopyOnWriteArrayList<SpartProfitingRelationVO> modifyList) {
        this.userId = userId;
        this.curTime = curTime;
        this.modifyList = modifyList;
    }

    @Override
    public void run() {
        try {
            buildModifyRecord(userId, curTime, modifyList);
        } catch (Exception ex) {
            logger.error("SpartRelationModifyThread occurs error: {}", ex);
        }
    }

    /**
     * 新增修改记录
     *
     * @param userId    用户id
     * @param spartList 变更信息列表
     */
    public void buildModifyRecord(Long userId, Timestamp curTime, List<SpartProfitingRelationVO> spartList) throws CommonApplicationException {
        if (CollectionUtils.isEmpty(spartList)) {
            return;
        }
        LabelModifyVO labelModifyVO = new LabelModifyVO();
        labelModifyVO.setId(UUID.randomUUID().toString());
        labelModifyVO.setPageModule(ModuleEnum.MODULE_AUDIT.getDesc());
        labelModifyVO.setCreatedBy(String.valueOf(userId));
        labelModifyVO.setLastUpdatedBy(String.valueOf(userId));
        labelModifyVO.setCreationDate(curTime);
        labelModifyVO.setLastUpdateDate(curTime);
        labelModifyVO.setStatus(SaveTypeEnum.SUBMIT.getCode());
        Map<String, Object> params = new ConcurrentHashMap<>();
        params.put("userId", userId);
        Object iExportService = Jalor.getContext().getBean("exportService");
        if (Objects.isNull(iExportService)) {
            return;
        }
        Map<Long, String> userCnName = ((ExportService) iExportService).getUserCnName(new HashSet<>(Arrays.asList(userId)));
        spartList.stream().forEach(item -> item.setLastUpdateCn(userCnName.get(userId)));
        Object iLabelModifyDao = Jalor.getContext().getBean("ILabelModifyDao");
        if (Objects.isNull(iLabelModifyDao)) {
            return;
        }
        try {
            params.put("fileName", "对象维表-标签审视-修改记录-");
            ((IExportService) iExportService).exportModifyData(params, CommUtils.buildModifyData(spartList));
        } catch (CommonApplicationException ex) {
            logger.error("生成修改记录文件失败: {}", ex);
        }
        if (Objects.nonNull(params.get("fileName"))) {
            labelModifyVO.setFileName(String.valueOf(params.get("fileName")));
        }
        if (Objects.nonNull(params.get("fileKey"))) {
            labelModifyVO.setFileSourceKey(String.valueOf(params.get("fileKey")));
        }
        ((ILabelModifyDao) iLabelModifyDao).createList(Arrays.asList(labelModifyVO));
    }
}
