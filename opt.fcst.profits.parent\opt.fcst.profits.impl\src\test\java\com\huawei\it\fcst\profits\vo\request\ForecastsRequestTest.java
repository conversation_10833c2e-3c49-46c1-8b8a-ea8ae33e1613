/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.vo.request;

import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import com.alibaba.fastjson.JSONObject;
import com.huawei.it.fcst.profits.vo.BaseVOCoverUtilsTest;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年02月28日
 */
class ForecastsRequestTest extends BaseVOCoverUtilsTest<ForecastsRequest> {

    @Override
    protected Class getTClass() {
        return ForecastsRequest.class;
    }

    @Test
    public void setMonthlyForecastsParamTest(){
        String str = "{\"periodId\":202303,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"季度\",\"fcstType\":\"分月法\",\"phaseDate\":\"20230121\",\"predictionType\":\"MONTH\",\"orderField\":\"\",\"orderType\":\"desc\",\"roleId\":13119,\"pageIndex\":1,\"pageSize\":10,\"overseaDesc\":\"全球\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        request.buildFcstStepQueryParam();
        Assertions.assertEquals("2023", request.getCurYear());
    }
    @Test
    public void setMonthlyForecastsParamTestA(){
        String str = "{\"periodId\":202303,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"半年度\",\"fcstType\":\"分月法\",\"phaseDate\":\"20230121\",\"predictionType\":\"MONTH\",\"orderField\":\"\",\"orderType\":\"desc\",\"roleId\":13119,\"pageIndex\":1,\"pageSize\":10,\"overseaDesc\":\"全球\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        request.buildFcstStepQueryParam();
        Assertions.assertEquals("2023", request.getCurYear());
    }
    @Test
    public void setMonthlyForecastsParamTestB(){
        String str = "{\"periodId\":202303,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"分月法\",\"phaseDate\":\"20230121\",\"predictionType\":\"MONTH\",\"orderField\":\"\",\"orderType\":\"desc\",\"roleId\":13119,\"pageIndex\":1,\"pageSize\":10,\"overseaDesc\":\"全球\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        request.buildFcstStepQueryParam();
        Assertions.assertEquals("2023", request.getCurYear());
    }
    @Test
    public void setMonthlyForecastsParamTestC(){
        String str = "{\"periodId\":202303,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"年度法\",\"phaseDate\":\"20230121\",\"predictionType\":\"YEAR\",\"orderField\":\"\",\"orderType\":\"desc\",\"roleId\":13119,\"pageIndex\":1,\"pageSize\":10,\"overseaDesc\":\"全球\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        request.buildFcstStepQueryParam();
        Assertions.assertEquals("2023", request.getCurYear());
    }



}