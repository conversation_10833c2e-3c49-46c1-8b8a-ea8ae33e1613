/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.profits.utils;

import cn.hutool.core.date.DateUtil;
import com.huawei.it.fcst.profits.common.annotations.ExportAttribute;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.ReflectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * ExcelExportUtil Class
 *
 * <AUTHOR>
 * @since 2023/11/14
 */
public class ExcelExportUtil<T> {

    // 写入样式的起始行
    private int styleIndex;

    // 写入数据的起始行
    private int rowIndex;

    // 对象中的所有属性
    private Field[] fields;

    // 对象字节码
    private Class clazz;

    public int getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(int rowIndex) {
        this.rowIndex = rowIndex;
    }

    public int getStyleIndex() {
        return styleIndex;
    }

    public void setStyleIndex(int styleIndex) {
        this.styleIndex = styleIndex;
    }

    public Class getClazz() {
        return clazz;
    }

    public void setClazz(Class clazz) {
        this.clazz = clazz;
    }

    public Field[] getFields() {
        return fields;
    }

    public void setFields(Field[] fields) {
        this.fields = fields;
    }

    public ExcelExportUtil() {
    }

    public ExcelExportUtil(int rowIndex, int styleIndex, Class clazz) {
        this.rowIndex = rowIndex;
        this.styleIndex = styleIndex;
        this.clazz = clazz;
        this.fields = clazz.getDeclaredFields();
    }

    private void setCellStyleAndValues(List<T> dataList, Sheet sheet) throws IllegalAccessException {
        // 3、提取公共的样式
        CellStyle[] templateStyles = getTemplateStyles(sheet.getRow(styleIndex));
        // 4、创建每一行每一列的数据
        AtomicInteger atomicInteger = new AtomicInteger(rowIndex);
        for (T data : dataList) {
            // 5、创建要写入的行数据
            Row row = sheet.createRow(atomicInteger.getAndIncrement());
            for (int idx = 0; idx < templateStyles.length; idx++) {
                // 设置单元格样式
                Cell cell = row.createCell(idx);
                cell.setCellStyle(templateStyles[idx]);
                // 设置单元格值
                setCellValue(data, idx, cell);
            }
        }
    }

    /**
     * 填充Sheet页数据
     *
     * @param sheet 要导出的工作工作表
     * @param dataList 数据集
     * @throws Exception
     */
    public void fillSheetData(Sheet sheet, List<T> dataList) throws Exception {
        // 2、提取工作表中的公共的样式
        setCellStyleAndValues(dataList, sheet);
    }

    /**
     * 获取模板样式
     *
     * @param row 行数据
     * @return CellStyle[] 单元格饲养数组
     */
    public static CellStyle[] getTemplateStyles(Row row) {
        short lastCellNum = row.getLastCellNum();
        CellStyle[] cellStyles = new CellStyle[lastCellNum];
        for (int i = 0; i < lastCellNum; i++) {
            cellStyles[i] = row.getCell(i).getCellStyle();
        }
        return cellStyles;
    }

    /**
     * 下载Excel到浏览器
     *
     * @param workbook 工作簿
     * @param fileName 导出文件名
     * @param response 响应
     * @throws Exception
     */
    public static void downloadExcel(Workbook workbook, String fileName, HttpServletResponse response)
        throws Exception {
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name());
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setHeader("content-disposition",
            "attachment;filename=" + new String(fileName.concat( ".xlsx").getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8));
        response.setHeader("filename", fileName);
        workbook.write(response.getOutputStream());
    }

    private void setCellValue(T data, int idx, Cell cell) throws IllegalAccessException {
        for (Field field : fields) {
            if (!field.isAnnotationPresent(ExportAttribute.class)) {
                continue;
            }
            ReflectionUtils.makeAccessible(field);
            setCellValueByDataType(data, idx, cell, field);
        }
    }

    private void setCellValueByDataType(T data, int idx, Cell cell, Field field) throws IllegalAccessException {
        ExportAttribute exportAttribute = field.getAnnotation(ExportAttribute.class);
        if (idx == exportAttribute.sort()) {
            Object value = field.get(data);
            if (value != null) {
                if ("Number".equals(exportAttribute.dataType())) {
                    cell.setCellValue(Double.valueOf(value.toString()));
                } else if ("DATE".equals(exportAttribute.dataType())) {
                    cell.setCellValue(DateUtil.format((Date) value, "yyyy-MM-dd HH:mm:ss"));
                } else {
                    cell.setCellValue(value.toString());
                }
            }
        }
    }

}