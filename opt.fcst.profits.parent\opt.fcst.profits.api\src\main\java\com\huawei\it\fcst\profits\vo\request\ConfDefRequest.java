/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.request;

import lombok.Data;

/**
 * ConfDefRequest
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
@Data
public class ConfDefRequest implements Comparable<ConfDefRequest>{
    /**
     * 会计期
     **/
    private long periodId;

    /**
     * BG编码
     **/
    private String bgCode;

    /**
     * BG编码
     **/
    private String bgName;

    private String lv1Code;

    private String lv1Name;

    /**
     * 产品lv1
     **/
    private String lv2Code;

    private String lv2Name;

    @Override
    public int compareTo(ConfDefRequest conf) {
        return this.bgCode.compareTo(conf.getBgCode());
    }
}
