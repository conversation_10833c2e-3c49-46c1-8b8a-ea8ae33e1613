/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The Entity of SpartResponse
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-15 09:18:41
 */
@Getter
@Setter
public class SpartResponse {

    /**
     * ITEM编
     **/
    @JsonProperty("item_code")
    private String itemCode;

    /**
     * LV1编码
     **/
    @JsonProperty("lv1_code")
    private String lv1Code;

    /**
     * LV1名称
     **/
    @JsonProperty("lv1_name")
    private String lv1Name;

    /**
     * L1名称
     **/
    @JsonProperty("l1_name")
    private String l1Name;

    /**
     * L2名称
     **/
    @JsonProperty("l2_name")
    private String l2Name;

    /**
     * L3名称
     **/
    @JsonProperty("l3_name")
    private String l3Name;

    /**
     * L3系数
     **/
    @JsonProperty("l3_coefficient")
    private BigDecimal l3Coefficient;

    /**
     * 会计期
     **/
    @JsonProperty("period_id")
    private Long periodId;

    /**
     * L2系数
     **/
    @JsonProperty("l2_coefficient")
    private BigDecimal l2Coefficient;

    /**
     * L1系数
     **/
    @JsonProperty("l1_coefficient")
    private BigDecimal l1Coefficient;

    /**
     * 修改标识（Y 是、N 否）
     **/
    @JsonProperty("update_flag")
    private String updateFlag;

    /**
     * 数据类型（Manual 历史、AI 新增）
     **/
    @JsonProperty("data_type")
    private String dataType;

}
