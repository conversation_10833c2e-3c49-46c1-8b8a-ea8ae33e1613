package com.huawei.it.fcst.profits.impl;

import com.huawei.it.fcst.profits.service.config.impl.CoaConfigDataExporter;
import com.huawei.it.fcst.profits.vo.CoaConfigDataVO;

import cn.hutool.core.bean.BeanUtil;

import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class CoaConfigDataExporterTest {
    @InjectMocks
    private CoaConfigDataExporter coaConfigDataExporter;
    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void resetDataList() {
        List<Map<String, Object>> infoList = new ArrayList<>();
        CoaConfigDataVO dataVO = new CoaConfigDataVO();
        dataVO.setStatus("Import");
        Map<String,Object> infoMap = BeanUtil.beanToMap(dataVO);
        infoList.add(infoMap);
        coaConfigDataExporter.resetDataList(new ArrayList() ,infoList);
        Assertions.assertEquals("待提交", infoList.get(0).get("status"));
    }
    @Test
    public void resetDataListA() {
        List<Map<String, Object>> infoList = new ArrayList<>();
        CoaConfigDataVO dataVO = new CoaConfigDataVO();
        dataVO.setStatus("Submit");
        Map<String,Object> infoMap = BeanUtil.beanToMap(dataVO);
        infoList.add(infoMap);
        coaConfigDataExporter.resetDataList(new ArrayList() ,infoList);
        Assertions.assertEquals("提交", infoList.get(0).get("status"));
    }
    @Test
    public void resetDataListB() {
        List<Map<String, Object>> infoList = new ArrayList<>();
        CoaConfigDataVO dataVO = new CoaConfigDataVO();
        Map<String,Object> infoMap = new HashMap<>();
        infoList.add(infoMap);
        coaConfigDataExporter.resetDataList(new ArrayList() ,infoList);
        Assertions.assertNotNull(infoMap);
    }
}