/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.common.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.registry.RegistryVO;

import static org.assertj.core.api.Assertions.assertThat;

class FcstGlobalParameterUtilTest {

    @Test
    void testPutRegistryValue() {
        // Setup
        final RegistryVO value = new RegistryVO();
        value.setStatus(0);
        value.setPropertyId(0);
        value.setLanguage("zh_CN");
        value.setDescription("description");
        value.setDisplayName("displayName");
        value.setName("name");
        value.setValue("def");
        value.setListIndex(0);
        value.setParentPath("parentPath");
        value.setParentId(0);
        // Run the test
        FcstGlobalParameterUtil.putRegistryValue(value.getParentPath()+value.getName(), value);
        Assertions.assertNotNull(FcstGlobalParameterUtil.getRegistryValue(value.getParentPath(),value.getName()));
        // Verify the results
    }
    @Test
    void testCleanRegistryValue() {
        // Setup
        // Run the test
        FcstGlobalParameterUtil.cleanRegistryValue();
        Assertions.assertEquals(FcstGlobalParameterUtil.getRegistryValue("test","test"),"");
        // Verify the results
    }

    @Test
    void testPutAllLookupValue() {
        // Setup
        final Map<String, List<LookupItemVO>> value = new HashMap<>();
        List<LookupItemVO> lookupItemVOList =new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("TEST");
        lookupItemVOList.add(lookupItemVO);
        value.put("FCST_METHOD_TYPE",lookupItemVOList);
        // Run the test
        FcstGlobalParameterUtil.putAllLookupValue(value);
        // Verify the results
        Assertions.assertNotNull(FcstGlobalParameterUtil.findMonthTypeItemByItemCode("TEST"));
    }

    @Test
    void testCleanLookupValue() {
        // Setup
        // Run the test
        FcstGlobalParameterUtil.cleanLookupValue();
        Assertions.assertFalse(FcstGlobalParameterUtil.findMonthTypeItemByItemCode("TEST").isPresent());
        // Verify the results
    }



    @Test
    void testGetRegistryEspaceValue() {
        assertThat(FcstGlobalParameterUtil.getRegistryEspaceValue("path", "key", "def")).isEqualTo("def");
    }

}
