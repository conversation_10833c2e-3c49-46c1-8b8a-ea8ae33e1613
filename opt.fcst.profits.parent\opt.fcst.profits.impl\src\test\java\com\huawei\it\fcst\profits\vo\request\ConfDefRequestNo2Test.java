/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.vo.request;

import static org.mockito.MockitoAnnotations.openMocks;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.rule.PowerMockRule;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年02月28日
 */
public class ConfDefRequestNo2Test {

    @InjectMocks
    private ConfDefRequest confDefRequest;

    @Rule
    public PowerMockRule rule = new PowerMockRule();
    private AutoCloseable mockitoCloseable;

    @Before
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @Test
    public void testEquals(){
        confDefRequest = new ConfDefRequest();
        ConfDefRequest request = new ConfDefRequest();
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }

    @Test
    public void testEqualsA(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1999L);
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }

    @Test
    public void testEqualsB(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsC(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("2");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsD(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Code("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("2");
        request.setLv1Code("1");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsE(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Code("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("2");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsF(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Code("1");
        confDefRequest.setLv1Name("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("1");
        request.setLv1Name("2");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsG(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Code("1");
        confDefRequest.setLv1Name("1");
        confDefRequest.setLv2Code("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("1");
        request.setLv1Name("1");
        request.setLv2Code("2");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsH(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Code("1");
        confDefRequest.setLv1Name("1");
        confDefRequest.setLv2Code("1");
        confDefRequest.setLv2Name("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("1");
        request.setLv1Name("1");
        request.setLv2Code("1");
        request.setLv2Name("2");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsI(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Code("1");
        confDefRequest.setLv1Name("1");
        confDefRequest.setLv2Code("1");
        confDefRequest.setLv2Name("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("1");
        request.setLv1Name("1");
        request.setLv2Code("1");
        request.setLv2Name("1");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsJ(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Code("1");
        confDefRequest.setLv1Name("1");
        confDefRequest.setLv2Code("1");
        confDefRequest.setLv2Name("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("1");
        request.setLv1Name("1");
        request.setLv2Code("1");
        request.setLv2Name("1");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsK(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setLv1Code("1");
        confDefRequest.setLv1Name("1");
        confDefRequest.setLv2Code("1");
        confDefRequest.setLv2Name("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("1");
        request.setLv1Name("1");
        request.setLv2Code("1");
        request.setLv2Name("1");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsL(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Name("1");
        confDefRequest.setLv2Code("1");
        confDefRequest.setLv2Name("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("1");
        request.setLv1Name("1");
        request.setLv2Code("1");
        request.setLv2Name("1");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsM(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Code("1");
        confDefRequest.setLv2Code("1");
        confDefRequest.setLv2Name("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("1");
        request.setLv1Name("1");
        request.setLv2Code("1");
        request.setLv2Name("1");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsO(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Code("1");
        confDefRequest.setLv1Name("1");
        confDefRequest.setLv2Name("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("1");
        request.setLv1Name("1");
        request.setLv2Code("1");
        request.setLv2Name("1");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
    @Test
    public void testEqualsP(){
        confDefRequest = new ConfDefRequest();
        confDefRequest.setPeriodId(1250L);
        confDefRequest.setBgCode("1");
        confDefRequest.setBgName("1");
        confDefRequest.setLv1Code("1");
        confDefRequest.setLv1Name("1");
        confDefRequest.setLv2Code("1");
        ConfDefRequest request = new ConfDefRequest();
        request.setPeriodId(1250L);
        request.setBgCode("1");
        request.setBgName("1");
        request.setLv1Code("1");
        request.setLv1Name("1");
        request.setLv2Code("1");
        request.setLv2Name("1");
        confDefRequest.equals(request);
        Assertions.assertNotNull(request);
    }
}
