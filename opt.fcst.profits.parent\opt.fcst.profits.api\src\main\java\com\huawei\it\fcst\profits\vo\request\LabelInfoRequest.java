/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * The Entity of LabelInfoRequest
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-12 11:07:53
 */
@Getter
@Setter
public class LabelInfoRequest extends PageRequest {

    /**
     * 导入是否有效
     **/
    @JsonProperty("is_vaild")
    private String isVaild;

    /**
     * 会计期
     **/
    @JsonProperty("period_id")
    private Long periodId;

    /**
     * ITEM编
     **/
    @JsonProperty("item_code")
    private String itemCode;

    /**
     * ITEM编
     **/
    @JsonProperty("item_desc")
    private String itemDesc;

    /**
     * LV1名称
     **/
    @JsonProperty("lv1_name")
    private String lv1Name;

    /**
     * LV1名称
     **/
    @JsonProperty("lv1_code")
    private String lv1Code;

    /**
     * L1名称
     **/
    @JsonProperty("l1_name")
    private String l1Name;

    /**
     * L2名称
     **/
    @JsonProperty("l2_name")
    private String l2Name;

    /**
     * L3名称
     **/
    @JsonProperty("l3_name")
    private String l3Name;

    /**
     * L3系数
     **/
    @JsonProperty("l3_coefficient")
    private BigDecimal l3Coefficient;

    /**
     * L2系数
     **/
    @JsonProperty("l2_coefficient")
    private BigDecimal l2Coefficient;

    /**
     * L1系数
     **/
    @JsonProperty("l1_coefficient")
    private BigDecimal l1Coefficient;

    public String getLv1Code() {
        return lv1Code;
    }

    public void setLv1Code(String lv1Code) {
        this.lv1Code = lv1Code;
    }

    /**
     * 数据类型（Manual 历史、AI 新增）
     **/
    private String dataType;

    private String status;

    private Long lastUpdateBy;

    private Timestamp lastUpdateTime;

    private List<Long> periods;

    private List<String> itemCodes;

    private List<String> lv1Names;

    private List<String> l1Names;

    private List<String> l2Names;

    private List<String> l3Names;

    private List<SpartProfitingRelationVO> submitList;

    private String errorMsg;

    private AtomicInteger atomicInteger;

    private String optType;

    private Integer startIndex;

    private Integer offset;

}
