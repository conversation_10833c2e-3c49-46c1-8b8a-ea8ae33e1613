/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.vo.response;

import com.huawei.it.fcst.profits.vo.BaseVOCoverUtilsTest;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年02月28日
 */
class GroupTestLv1TableVO extends BaseVOCoverUtilsTest<GroupLv1TableVO> {

    @Override
    protected Class getTClass() {
        return GroupLv1TableVO.class;
    }
}