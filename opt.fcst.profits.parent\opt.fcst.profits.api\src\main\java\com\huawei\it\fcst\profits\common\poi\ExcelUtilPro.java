/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.poi;

import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.huawei.it.fcst.profits.common.constants.Constants;
import com.huawei.it.fcst.profits.common.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.BranchExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.ExcelVO;
import com.huawei.it.fcst.profits.common.vo.LeafExcelTitleVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * The class ExcelUtilPro.java
 *
 * <AUTHOR>
 * @since 2022/2/22
 */
@Component
public class ExcelUtilPro {

    /**
     * [服务名称]implReadHead
     *
     * @param heads 入参
     * @return List<ExcelVO>
     * @throws CommonApplicationException
     * <AUTHOR>
     */
    public List<ExcelVO> implReadHead(String[] heads) {
        List<ExcelVO> list = new ArrayList<>();
        Arrays.asList(heads).stream().forEach(head -> {
            ExcelVO vo = new ExcelVO();
            if (head.contains(Constants.XHX.getValue())) {
                String[] titleHead = head.split(Constants.XHX.getValue());
                vo.setHeadName(titleHead[0]);
                vo.setHeadType(titleHead[1]);
            } else {
                vo.setHeadName(Constants.SNULL.getValue());
                vo.setHeadType(Constants.SNULL.getValue());
            }
            list.add(vo);
        });
        return list;
    }

    private void setExceptionHeader(int columnIdx, Workbook workbook, Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        Cell headerCell = headerRow.getCell(columnIdx);
        if (headerCell == null) {
            Cell exceptionCell = headerRow.createCell(columnIdx);
            exceptionCell.setCellValue("异常提示");
            CellStyle style = workbook.createCellStyle();
            style.setFillForegroundColor(IndexedColors.RED.index);
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            Font font = workbook.createFont();
            font.setFontName("微软雅黑");
            style.setFont(font);
            exceptionCell.setCellStyle(style);
        }
    }

    /**
     * [服务名称]adjustTitleVoList
     *
     * @param excelTitleVOS            入参
     * @param selectedTitles           入参
     * @param selectedLeafExcelTitleVO 入参
     * @return int
     * @throws CommonApplicationException
     * <AUTHOR>
     */
    public int adjustTitleVoList(List<AbstractExcelTitleVO> excelTitleVOS, Set<String> selectedTitles,
                                 List<AbstractExcelTitleVO> selectedLeafExcelTitleVO) {
        ExcelUtilPro excelUtilPro = new ExcelUtilPro();
        excelUtilPro.initTitleVoList(excelTitleVOS, selectedTitles, selectedLeafExcelTitleVO, 0, 0);
        // 叶子结点的高度调整，
        int maxX2 = 0;
        for (AbstractExcelTitleVO leafExcelTitleVO : selectedLeafExcelTitleVO) {
            if (leafExcelTitleVO.getX2() > maxX2) {
                maxX2 = leafExcelTitleVO.getX2();
            }
        }
        for (AbstractExcelTitleVO leafExcelTitleVO : selectedLeafExcelTitleVO) {
            if (leafExcelTitleVO.getX2() < maxX2) {
                leafExcelTitleVO.setX2(maxX2);
            }
        }
        return maxX2 + 1;
    }

    private void adjustChildExcelTitleVoWidth(List<AbstractExcelTitleVO> childTitleVoList, int gap) {
        if (CollectionUtil.isNullOrEmpty(childTitleVoList)) {
            return;
        }
        int index = childTitleVoList.size() - 1;
        while (index >= 0) {
            if (childTitleVoList.get(index).getSelected() != null && childTitleVoList.get(index).getSelected()) {
                break;
            } else {
                index--;
            }
        }
        if (index < 0) {
            return;
        }
        AbstractExcelTitleVO tailExcelTitleVo = childTitleVoList.get(index);
        int adjustWidth = tailExcelTitleVo.getWidth() + gap;
        tailExcelTitleVo.setWidth(adjustWidth);
        if (tailExcelTitleVo instanceof BranchExcelTitleVO) {
            adjustChildExcelTitleVoWidth(((BranchExcelTitleVO) tailExcelTitleVo).getChildExcelTitleList(), gap);
        }
    }

    private Map initTitleVoList(List<AbstractExcelTitleVO> titleVoList, Set<String> selectedTitles,
                                List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, int xx, int yy) {
        Map map = new HashMap();
        int allLeafCount = 0;
        boolean isHasChildSelected = false;
        int allChildWidthSum = 0;
        int tempYy = yy;
        int tempXx = xx;
        for (AbstractExcelTitleVO titleVO : titleVoList) {
            if ((titleVO instanceof LeafExcelTitleVO) && (titleVO.getSelected() || (selectedTitles.contains(
                    ((LeafExcelTitleVO) titleVO).getColumnCode())))) {
                titleVO.setX1(tempXx);
                titleVO.setY1(tempYy);
                titleVO.setX2(tempXx);
                titleVO.setY2(tempYy);
                tempYy++;
                allLeafCount++;
                titleVO.setSelected(true);
                isHasChildSelected = true;
                allChildWidthSum += titleVO.getWidth();
                selectedLeafExcelTitleVO.add(titleVO);
            } else if (titleVO instanceof BranchExcelTitleVO) {
                Map childMap = initTitleVoList(((BranchExcelTitleVO) titleVO).getChildExcelTitleList(), selectedTitles,
                        selectedLeafExcelTitleVO, tempXx + 1, tempYy);
                boolean isCurrentHasChildSelected = Boolean.valueOf(childMap.get("isHasChildSelected").toString());
                if (isCurrentHasChildSelected) {
                    int currentLeafCount = getCurrentLeafCount(tempYy, tempXx, titleVO, childMap);
                    allLeafCount += currentLeafCount;
                    tempYy += currentLeafCount;
                    int currentChildWidthSum = Integer.valueOf(childMap.get("allChildWidthSum").toString());
                    if (currentChildWidthSum > titleVO.getWidth()) {
                        titleVO.setWidth(currentChildWidthSum);
                        allChildWidthSum += currentChildWidthSum;
                    }
                    if (currentChildWidthSum < titleVO.getWidth()) {
                        int gap = titleVO.getWidth() - currentChildWidthSum;
                        adjustChildExcelTitleVoWidth(((BranchExcelTitleVO) titleVO).getChildExcelTitleList(), gap);
                        allChildWidthSum += titleVO.getWidth();
                    }
                }
                isHasChildSelected = isHasChildSelected || isCurrentHasChildSelected;
            } else {
                continue;
            }
        }
        map.put("allLeafCount", allLeafCount);
        map.put("isHasChildSelected", isHasChildSelected);
        map.put("allChildWidthSum", allChildWidthSum);
        return map;
    }

    private int getCurrentLeafCount(int tempYy, int tempXx, AbstractExcelTitleVO titleVO, Map childMap) {
        titleVO.setX1(tempXx);
        titleVO.setY1(tempYy);
        titleVO.setX2(tempXx);
        titleVO.setSelected(true);
        int currentLeafCount = Integer.valueOf(childMap.get("allLeafCount").toString());
        titleVO.setY2(tempYy + currentLeafCount - 1);
        return currentLeafCount;
    }

    /**
     * [服务名称]getStringCellValue
     *
     * @param cell     入参
     * @param headType 入参
     * @return Object
     * @throws CommonApplicationException
     * <AUTHOR>
     */
    public String getStringCellValue(Cell cell, String headType) throws CommonApplicationException {
        Optional<String> ele = Optional.ofNullable(null);
        if (headType.equals("VARCHAR") && cell == null) {
            return Constants.DEFAULT.getValue();
        } else if (headType.equals("NUMERIC") && cell == null) {
            return ele.orElse(null);
        } else if (headType.equals("NUMERIC") && cell != null) {
            try {
                String value = PoiUtils.getStringCellValue(cell);
                return StrUtil.isBlank(value) ? ele.orElse(null) : NumberUtil.parseNumber(value).toString();
            } catch (UtilException e1) {
                throw new CommonApplicationException(
                        "第" + (cell.getRowIndex() + 2) + "行,第" + (cell.getColumnIndex() + 1) + "列不是数字类型");
            }
        } else if (("VARCHAR").equals(headType) && cell != null) {
            return PoiUtils.getStringCellValue(cell);
        } else {
            return ele.orElse(null);
        }
    }


}
