/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.fcst.profits.utils;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.BgEnum;
import com.huawei.it.fcst.profits.common.enums.PredictionEnum;
import com.huawei.it.fcst.profits.common.utils.DateUtil;
import com.huawei.it.fcst.profits.common.utils.FcstGlobalParameterUtil;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.BranchExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.ExcelVO;
import com.huawei.it.fcst.profits.common.vo.HeaderVo;
import com.huawei.it.fcst.profits.common.vo.LeafExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.TableHeaderVo;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.CellType;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public class HeaderUtils {

    private static final Map<String, String> LV1_CHILD_TOP_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD1_HEADER = new LinkedHashMap<>();

    private static final Map<String, String> LV1_CHILD_MONTH_EBG1_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD_MONTH_EBG2_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD_MONTH_EBG3_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD_MONTH_EBG4_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD_MONTH_EBG5_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD_MONTH_EBG6_HEADER = new LinkedHashMap(){
        {
            put("ebgCombinedExpertEquipRevAfter", "设备收入");put("ebgCombinedExpertMgpRateAfter", "制毛率");
        }
    };
    private static final Map<String, String> LV1_CHILD_MONTH_CNBG1_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD_MONTH_CNBG2_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD_MONTH_CNBG3_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD_MONTH_CNBG4_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD_MONTH_CNBG5_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD_MONTH_CNBG6_HEADER = new LinkedHashMap(){
        {
            put("cnbgCombinedExpertEquipRevAfter", "设备收入");put("cnbgCombinedExpertMgpRateAfter", "制毛率");
        }
    };

    private static final Map<String, String> LV1_CHILD2_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD3_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD4_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD5_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV1_CHILD6_HEADER = new LinkedHashMap(){
        {
            put("combinedExpertEquipRevAfter", "设备收入");put("combinedExpertMgpRateAfter", "制毛率");
        }
    };
    private static final Map<String, String> LV1_CHILD7_HEADER = new LinkedHashMap(){
        {
            put("groupAnalystEquipRevAfter", "设备收入");put("groupAnalystMgpRateAfter", "制毛率");
        }
    };
    private static final Map<String, String> LV1_CHILD8_HEADER = new LinkedHashMap(){
        {
            put("combinedExpertGroupEquipRevAfter", "设备收入");put("combinedExpertGroupMgpRateAfter", "制毛率");
        }
    };
    private static final Map<String, String> L2_CHILD1_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> L2_CHILD2_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> L2_CHILD3_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> L2_CHILD4_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> L2_CHILD5_HEADER = new LinkedHashMap<>();
    public static final Map<String, String> AUDIT_CHILD_IMP_HEADER = new LinkedHashMap<>();
    public static final Map<String, String> AUDIT_CHILD_IMP_ERROR_HEADER = new LinkedHashMap<>();
    public static final Map<String, String> AUDIT_CHILD_EXP_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> AUDIT_CHILD_EXP_ERROR_HEADER = new LinkedHashMap<>();
    public static final Map<String, String> AUDIT_CHILD_MODIFY_HEADER = new LinkedHashMap<>();

    private static final Map<String, String> LV2_CHILD_EBG1_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV2_CHILD_EBG2_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV2_CHILD_EBG3_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV2_CHILD_EBG4_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV2_CHILD_EBG5_HEADER = new LinkedHashMap<>();

    private static final Map<String, String> LV2_CHILD_CNBG1_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV2_CHILD_CNBG2_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV2_CHILD_CNBG3_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV2_CHILD_CNBG4_HEADER = new LinkedHashMap<>();
    private static final Map<String, String> LV2_CHILD_CNBG5_HEADER = new LinkedHashMap<>();

    public static final Map<String, String> SPART_INFO_L1_HEADER = new LinkedHashMap<>();

    public static final Map<String, String> SPART_INFO_L2_HEADER = new LinkedHashMap<>();

    public static final Map<String, String> SPART_INFO_L3_HEADER = new LinkedHashMap<>();



    static {
        AUDIT_CHILD_IMP_HEADER.put("isVaild", "是否有效");
        AUDIT_CHILD_IMP_HEADER.put("itemCode", "Spart编码");
        AUDIT_CHILD_IMP_HEADER.put("itemDesc", "Spart描述");
        AUDIT_CHILD_IMP_HEADER.put("lv1Name", "产业");
        AUDIT_CHILD_IMP_HEADER.put("l1Name", "L1名称");
        AUDIT_CHILD_IMP_HEADER.put("l2Name", "L2名称");
        AUDIT_CHILD_IMP_HEADER.put("l3Name", "L3名称");
        AUDIT_CHILD_IMP_HEADER.put("l1Coefficient", "L1系数");
        AUDIT_CHILD_IMP_HEADER.put("l2Coefficient", "L2系数");
        AUDIT_CHILD_IMP_HEADER.put("l3Coefficient", "L3系数");
        AUDIT_CHILD_IMP_ERROR_HEADER.put("errorMsg", "异常原因");
        AUDIT_CHILD_IMP_ERROR_HEADER.putAll(AUDIT_CHILD_IMP_HEADER);

        AUDIT_CHILD_MODIFY_HEADER.put("itemCode", "Spart编码");
        AUDIT_CHILD_MODIFY_HEADER.put("itemDesc", "Spart描述");
        AUDIT_CHILD_MODIFY_HEADER.put("lv1Name", "产业");
        AUDIT_CHILD_MODIFY_HEADER.put("l1Name", "L1名称");
        AUDIT_CHILD_MODIFY_HEADER.put("l2Name", "L2名称");
        AUDIT_CHILD_MODIFY_HEADER.put("l3Name", "L3名称");
        AUDIT_CHILD_MODIFY_HEADER.put("l1Coefficient", "L1系数");
        AUDIT_CHILD_MODIFY_HEADER.put("l2Coefficient", "L2系数");
        AUDIT_CHILD_MODIFY_HEADER.put("l3Coefficient", "L3系数");
        AUDIT_CHILD_MODIFY_HEADER.put("lastUpdatedBy", "最后更新人");
        AUDIT_CHILD_MODIFY_HEADER.put("lastUpdateDate", "最后更新时间");

        AUDIT_CHILD_EXP_HEADER.put("itemCode", "Spart编码");
        AUDIT_CHILD_EXP_HEADER.put("itemDesc", "Spart描述");
        AUDIT_CHILD_EXP_HEADER.put("lv1Name", "产业");
        AUDIT_CHILD_EXP_HEADER.put("l1Name", "L1名称");
        AUDIT_CHILD_EXP_HEADER.put("l2Name", "L2名称");
        AUDIT_CHILD_EXP_HEADER.put("l3Name", "L3名称");
        AUDIT_CHILD_EXP_HEADER.put("l2NameProb", "L2名称预测概率");
        AUDIT_CHILD_EXP_HEADER.put("l3NameProb", "L3名称预测概率");
        AUDIT_CHILD_EXP_HEADER.put("l1Coefficient", "L1系数");
        AUDIT_CHILD_EXP_HEADER.put("l2Coefficient", "L2系数");
        AUDIT_CHILD_EXP_HEADER.put("l3Coefficient", "L3系数");
        AUDIT_CHILD_EXP_HEADER.put("l1CoefficientProb", "L1系数预测概率");
        AUDIT_CHILD_EXP_HEADER.put("l2CoefficientProb", "L2系数预测概率");
        AUDIT_CHILD_EXP_HEADER.put("l3CoefficientProb", "L3系数预测概率");
        AUDIT_CHILD_EXP_HEADER.put("dataType", "数据类型");
        AUDIT_CHILD_EXP_HEADER.put("status", "状态");
        AUDIT_CHILD_EXP_HEADER.put("createdBy", "创建人");
        AUDIT_CHILD_EXP_HEADER.put("creationDate", "创建时间");
        AUDIT_CHILD_EXP_HEADER.put("lastUpdatedBy", "最后更新人");
        AUDIT_CHILD_EXP_HEADER.put("lastUpdateDate", "最后更新时间");
        AUDIT_CHILD_EXP_ERROR_HEADER.putAll(AUDIT_CHILD_EXP_HEADER);
        AUDIT_CHILD_EXP_ERROR_HEADER.put("errormsg", "备注");

        LV1_CHILD_TOP_HEADER.put("lv1Name", "产业");
        LV1_CHILD_TOP_HEADER.put("lv2Name", "SPDT");
        LV1_CHILD_TOP_HEADER.put("l1Name", "L1名称");

        LV1_CHILD1_HEADER.put("lastActureEquipRevAfter", "设备收入");
        LV1_CHILD1_HEADER.put("lastActureMgpRateAfter", "制毛率");
        LV1_CHILD2_HEADER.put("curSumEquipRevAfter", "设备收入");
        LV1_CHILD2_HEADER.put("curSumMgpRateAfter", "制毛率");
        LV1_CHILD3_HEADER.put("lastPeriodEquipRevAfter", "设备收入");
        LV1_CHILD3_HEADER.put("lastPeriodmgpRateAfter", "制毛率");
        LV1_CHILD4_HEADER.put("aiAutoEquipRevAfter", "设备收入");
        LV1_CHILD4_HEADER.put("aiAutoMgpRateAfter", "制毛率");
        LV1_CHILD5_HEADER.put("ai2AutoEquipRevAfter", "设备收入");
        LV1_CHILD5_HEADER.put("ai2AutoMgpRateAfter", "制毛率");

        LV1_CHILD_MONTH_EBG1_HEADER.put("ebgLastActureEquipRevAfter", "设备收入");
        LV1_CHILD_MONTH_EBG1_HEADER.put("ebgLastActureMgpRateAfter", "制毛率");
        LV1_CHILD_MONTH_EBG2_HEADER.put("ebgCurSumEquipRevAfter", "设备收入");
        LV1_CHILD_MONTH_EBG2_HEADER.put("ebgCurSumMgpRateAfter", "制毛率");
        LV1_CHILD_MONTH_EBG3_HEADER.put("ebgLastPeriodEquipRevAfter", "设备收入");
        LV1_CHILD_MONTH_EBG3_HEADER.put("ebgLastPeriodmgpRateAfter", "制毛率");
        LV1_CHILD_MONTH_EBG4_HEADER.put("ebgAiAutoEquipRevAfter", "设备收入");
        LV1_CHILD_MONTH_EBG4_HEADER.put("ebgAiAutoMgpRateAfter", "制毛率");
        LV1_CHILD_MONTH_EBG5_HEADER.put("ebgAi2AutoEquipRevAfter", "设备收入");
        LV1_CHILD_MONTH_EBG5_HEADER.put("ebgAi2AutoMgpRateAfter", "制毛率");

        LV1_CHILD_MONTH_CNBG1_HEADER.put("cnbgLastActureEquipRevAfter", "设备收入");
        LV1_CHILD_MONTH_CNBG1_HEADER.put("cnbgLastActureMgpRateAfter", "制毛率");
        LV1_CHILD_MONTH_CNBG2_HEADER.put("cnbgCurSumEquipRevAfter", "设备收入");
        LV1_CHILD_MONTH_CNBG2_HEADER.put("cnbgCurSumMgpRateAfter", "制毛率");
        LV1_CHILD_MONTH_CNBG3_HEADER.put("cnbgLastPeriodEquipRevAfter", "设备收入");
        LV1_CHILD_MONTH_CNBG3_HEADER.put("cnbgLastPeriodmgpRateAfter", "制毛率");
        LV1_CHILD_MONTH_CNBG4_HEADER.put("cnbgAiAutoEquipRevAfter", "设备收入");
        LV1_CHILD_MONTH_CNBG4_HEADER.put("cnbgAiAutoMgpRateAfter", "制毛率");
        LV1_CHILD_MONTH_CNBG5_HEADER.put("cnbgAi2AutoEquipRevAfter", "设备收入");
        LV1_CHILD_MONTH_CNBG5_HEADER.put("cnbgAi2AutoMgpRateAfter", "制毛率");

        L2_CHILD1_HEADER.put("lastActureRevPercent", "设备收入结构");
        L2_CHILD1_HEADER.put("lastActureMgpRateBefore", "制毛率");
        L2_CHILD1_HEADER.put("lastActureMcaAdjustRatio", "对价等转换系数");
        L2_CHILD1_HEADER.put("lastActureMgpAdjustRatio", "量价到损益制毛调整率");

        L2_CHILD2_HEADER.put("curSumRevPercent", "设备收入结构");
        L2_CHILD2_HEADER.put("curSumMgpRateBefore", "制毛率");
        L2_CHILD2_HEADER.put("curSumMcaAdjustRatio", "对价等转换系数");
        L2_CHILD2_HEADER.put("curSumMgpAdjustRatio", "量价到损益制毛调整率");

        L2_CHILD3_HEADER.put("lastPeriodRevPercent", "设备收入结构");
        L2_CHILD3_HEADER.put("lastPeriodMgpRateBefore", "制毛率");
        L2_CHILD3_HEADER.put("lastPeriodMcaAdjustRatio", "对价等转换系数");
        L2_CHILD3_HEADER.put("lastPeriodMgpAdjustRatio", "量价到损益制毛调整率");

        L2_CHILD4_HEADER.put("aiAutoRevPercent", "设备收入结构");
        L2_CHILD4_HEADER.put("aiAutoMgpRateBefore", "制毛率");
        L2_CHILD4_HEADER.put("aiAutoMcaAdjustRatio", "对价等转换系数");
        L2_CHILD4_HEADER.put("aiAutoMgpAdjustRatio", "量价到损益制毛调整率");

        L2_CHILD5_HEADER.put("ai2AutoRevPercent", "设备收入结构");
        L2_CHILD5_HEADER.put("ai2AutoMgpRateBefore", "制毛率");
        L2_CHILD5_HEADER.put("ai2AutoMcaAdjustRatio", "对价等转换系数");
        L2_CHILD5_HEADER.put("ai2AutoMgpAdjustRatio", "量价到损益制毛调整率");

        // ebg去年实际
        LV2_CHILD_EBG1_HEADER.put("ebgLastActureRevPercent", "设备收入结构");
        LV2_CHILD_EBG1_HEADER.put("ebgLastActureMgpRateBefore", "制毛率");
        LV2_CHILD_EBG1_HEADER.put("ebgLastActureMcaAdjustRatio", "对价等转换系数");
        LV2_CHILD_EBG1_HEADER.put("ebgLastActureMgpAdjustRatio", "量价到损益制毛调整率");
        // ebg当年累计
        LV2_CHILD_EBG2_HEADER.put("ebgCurSumRevPercent", "设备收入结构");
        LV2_CHILD_EBG2_HEADER.put("ebgCurSumMgpRateBefore", "制毛率");
        LV2_CHILD_EBG2_HEADER.put("ebgCurSumMcaAdjustRatio", "对价等转换系数");
        LV2_CHILD_EBG2_HEADER.put("ebgCurSumMgpAdjustRatio", "量价到损益制毛调整率");
        // ebg去年同期
        LV2_CHILD_EBG3_HEADER.put("ebgLastPeriodRevPercent", "设备收入结构");
        LV2_CHILD_EBG3_HEADER.put("ebgLastPeriodMgpRateBefore", "制毛率");
        LV2_CHILD_EBG3_HEADER.put("ebgLastPeriodMcaAdjustRatio", "对价等转换系数");
        LV2_CHILD_EBG3_HEADER.put("ebgLastPeriodMgpAdjustRatio", "量价到损益制毛调整率");
        // ebgAiAuto
        LV2_CHILD_EBG4_HEADER.put("ebgAiAutoRevPercent", "设备收入结构");
        LV2_CHILD_EBG4_HEADER.put("ebgAiAutoMgpRateBefore", "制毛率");
        LV2_CHILD_EBG4_HEADER.put("ebgAiAutoMcaAdjustRatio", "对价等转换系数");
        LV2_CHILD_EBG4_HEADER.put("ebgAiAutoMgpAdjustRatio", "量价到损益制毛调整率");
        // ebgAiAuto2
        LV2_CHILD_EBG5_HEADER.put("ebgAi2AutoRevPercent", "设备收入结构");
        LV2_CHILD_EBG5_HEADER.put("ebgAi2AutoMgpRateBefore", "制毛率");
        LV2_CHILD_EBG5_HEADER.put("ebgAi2AutoMcaAdjustRatio", "对价等转换系数");
        LV2_CHILD_EBG5_HEADER.put("ebgAi2AutoMgpAdjustRatio", "量价到损益制毛调整率");

        // cnbg去年实际
        LV2_CHILD_CNBG1_HEADER.put("cnbgLastActureRevPercent", "设备收入结构");
        LV2_CHILD_CNBG1_HEADER.put("cnbgLastActureMgpRateBefore", "制毛率");
        LV2_CHILD_CNBG1_HEADER.put("cnbgLastActureMcaAdjustRatio", "对价等转换系数");
        LV2_CHILD_CNBG1_HEADER.put("cnbgLastActureMgpAdjustRatio", "量价到损益制毛调整率");
        // cnbg今年累计
        LV2_CHILD_CNBG2_HEADER.put("cnbgCurSumRevPercent", "设备收入结构");
        LV2_CHILD_CNBG2_HEADER.put("cnbgCurSumMgpRateBefore", "制毛率");
        LV2_CHILD_CNBG2_HEADER.put("cnbgCurSumMcaAdjustRatio", "对价等转换系数");
        LV2_CHILD_CNBG2_HEADER.put("cnbgCurSumMgpAdjustRatio", "量价到损益制毛调整率");
        // cnbg去年同期
        LV2_CHILD_CNBG3_HEADER.put("cnbgLastPeriodRevPercent", "设备收入结构");
        LV2_CHILD_CNBG3_HEADER.put("cnbgLastPeriodMgpRateBefore", "制毛率");
        LV2_CHILD_CNBG3_HEADER.put("cnbgLastPeriodMcaAdjustRatio", "对价等转换系数");
        LV2_CHILD_CNBG3_HEADER.put("cnbgLastPeriodMgpAdjustRatio", "量价到损益制毛调整率");
        // cnbgAIAuto
        LV2_CHILD_CNBG4_HEADER.put("cnbgAiAutoRevPercent", "设备收入结构");
        LV2_CHILD_CNBG4_HEADER.put("cnbgAiAutoMgpRateBefore", "制毛率");
        LV2_CHILD_CNBG4_HEADER.put("cnbgAiAutoMcaAdjustRatio", "对价等转换系数");
        LV2_CHILD_CNBG4_HEADER.put("cnbgAiAutoMgpAdjustRatio", "量价到损益制毛调整率");
        // cnbgAI2Auto
        LV2_CHILD_CNBG5_HEADER.put("cnbgAi2AutoRevPercent", "设备收入结构");
        LV2_CHILD_CNBG5_HEADER.put("cnbgAi2AutoMgpRateBefore", "制毛率");
        LV2_CHILD_CNBG5_HEADER.put("cnbgAi2AutoMcaAdjustRatio", "对价等转换系数");
        LV2_CHILD_CNBG5_HEADER.put("cnbgAi2AutoMgpAdjustRatio", "量价到损益制毛调整率");

        SPART_INFO_L2_HEADER.put("overseaDesc","区域（其他/中国区/海外/全球）");
        SPART_INFO_L2_HEADER.put("targetPeriod","时间颗粒度（月度+月度YTD+季度+半年度+年度）");
        SPART_INFO_L2_HEADER.put("periodId","会计期");
        SPART_INFO_L2_HEADER.put("lv1Name","产业");
        SPART_INFO_L2_HEADER.put("lv2Name","LV2");
        SPART_INFO_L2_HEADER.put("l1Name","L1");
        SPART_INFO_L2_HEADER.put("l2Name","L2");
        SPART_INFO_L2_HEADER.put("mgpRatio","量价制毛率");
        SPART_INFO_L2_HEADER.put("unitCost","平均成本");
        SPART_INFO_L2_HEADER.put("unitPrice","平均价格");
        SPART_INFO_L2_HEADER.put("shipQty","发货量");
        SPART_INFO_L2_HEADER.put("spartQty","结转量");
        SPART_INFO_L2_HEADER.put("planQty","发货量（S&OP）");
        SPART_INFO_L2_HEADER.put("equipRevConsBefore","量价设备收入额");
        SPART_INFO_L2_HEADER.put("equipCostConsBefore","量价设备成本额");
        SPART_INFO_L2_HEADER.put("revPercent","L2收入结构");
        SPART_INFO_L2_HEADER.put("bgName","BG（ICT/CNBG/EBG）");

        SPART_INFO_L3_HEADER.put("overseaDesc","区域（其他/中国区/海外/全球）");
        SPART_INFO_L3_HEADER.put("targetPeriod","时间颗粒度（月度+月度YTD+季度+半年度+年度）");
        SPART_INFO_L3_HEADER.put("periodId","会计期");
        SPART_INFO_L3_HEADER.put("lv1Name","产业");
        SPART_INFO_L3_HEADER.put("lv2Name","LV2");
        SPART_INFO_L3_HEADER.put("l1Name","L1");
        SPART_INFO_L3_HEADER.put("l2Name","L2");
        SPART_INFO_L3_HEADER.put("l3Name","L3");
        SPART_INFO_L3_HEADER.put("unitCost","平均成本");
        SPART_INFO_L3_HEADER.put("unitPrice","平均价格");
        SPART_INFO_L3_HEADER.put("shipQty","发货量");
        SPART_INFO_L3_HEADER.put("spartQty","结转量");
        SPART_INFO_L3_HEADER.put("equipRevConsBefore","量价设备收入额");
        SPART_INFO_L3_HEADER.put("equipCostConsBefore","量价设备成本额");
        SPART_INFO_L3_HEADER.put("revPercent","L3收入结构");
        SPART_INFO_L3_HEADER.put("bgName","BG（ICT/CNBG/EBG）");

        SPART_INFO_L1_HEADER.put("overseaDesc","区域（其他/中国区/海外/全球）");
        SPART_INFO_L1_HEADER.put("targetPeriod","时间颗粒度（月度+月度YTD+季度+半年度+年度）");
        SPART_INFO_L1_HEADER.put("periodId","会计期");
        SPART_INFO_L1_HEADER.put("lv1Name","产业");
        SPART_INFO_L1_HEADER.put("lv2Name","LV2");
        SPART_INFO_L1_HEADER.put("l1Name","L1");
        SPART_INFO_L1_HEADER.put("mgpRatio","损益制毛率");
        SPART_INFO_L1_HEADER.put("unitCost","平均成本");
        SPART_INFO_L1_HEADER.put("unitPrice","平均价格");
        SPART_INFO_L1_HEADER.put("shipQty","发货量");
        SPART_INFO_L1_HEADER.put("spartQty","结转量");
        SPART_INFO_L1_HEADER.put("equipCostConsBefore","量价设备成本额");
        SPART_INFO_L1_HEADER.put("equipCostConsAfter","损益设备成本额");
        SPART_INFO_L1_HEADER.put("equipRevConsBefore","量价设备收入额");
        SPART_INFO_L1_HEADER.put("equipRevConsAfter","损益设备收入额");
        SPART_INFO_L1_HEADER.put("mgpAdjustRatio","量价到损益制毛调整率");
        SPART_INFO_L1_HEADER.put("mcaAdjustRatio","对价等转换系数");
        SPART_INFO_L1_HEADER.put("carryoverRatio","发货量到收入转化率");
        SPART_INFO_L1_HEADER.put("bgName","BG（ICT/CNBG/EBG）");
    }

    public static TableHeaderVo buildTableHeaderVo(String title, String field) {
        TableHeaderVo tableHeaderVo = new TableHeaderVo();
        tableHeaderVo.setTitle(title);
        tableHeaderVo.setField(field);
        return tableHeaderVo;
    }

    /**
     * buildTableHeaderVo 表头构建
     *
     * @param header    header
     * @param title     title
     * @param field     field
     * @param childList childList
     */
    public static TableHeaderVo buildTableHeaderVo(List<TableHeaderVo> header, String title, String field,
                                                   Map<String, String> childList) {
        TableHeaderVo tableHeaderVo = new TableHeaderVo();
        tableHeaderVo.setTitle(title);
        tableHeaderVo.setField(field);
        if (!CollectionUtil.isNullOrEmpty(childList)) {
            List<TableHeaderVo> children = new ArrayList<>();
            childList.forEach((k, v) -> {
                children.add(buildTableHeaderVo(v, k));
            });
            tableHeaderVo.setChildren(children);
        }
        header.add(tableHeaderVo);
        return tableHeaderVo;
    }

    /**
     * buildTableHeaderVo 表头构建
     *
     * @param header header
     * @param title  title
     * @param field  field
     */
    public static void buildTableSubHeaderVo(List<TableHeaderVo> header, String title, String field,
                                             Map<String, String> child1, Map<String, String> child2, Map<String, String> child3,
                                             Map<String, String> child4,Map<String, String> child5, String stepType, long periodId, String predictType) {
        TableHeaderVo tableHeader2 = buildTableHeaderVo(title, field);
        List<TableHeaderVo> subHeader2 = new ArrayList<>();
        if (StringUtils.equals(PredictionEnum.MONTH.getCode(), predictType)) {
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_LAST_YEAR + "(" + stepType + ")", "", child1);
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_CUR_YEAR, "", child2);
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_LAST_PERIOD, "", child3);
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_AI_AUTO + "(" + stepType + ")", "", child4);
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_COMBINED_EXPERT + "(" + stepType + ")", "", child5);
        }
        tableHeader2.setChildren(subHeader2);
        header.add(tableHeader2);
    }

    /**
     * buildTableHeaderVo 表头构建
     *
     * @param header header
     * @param title  title
     * @param field  field
     */
    public static void buildTableSubHeaderVo(List<TableHeaderVo> header, String title, String field,
                                             Map<String, String> child1, Map<String, String> child2, Map<String, String> child3,
                                             Map<String, String> child4, String stepType, long periodId, String predictType) {
        TableHeaderVo tableHeader2 = buildTableHeaderVo(title, field);
        int curYear = TimeUtils.getCurYear(periodId);
        List<TableHeaderVo> subHeader2 = new ArrayList<>();
        if (StringUtils.equals(PredictionEnum.MONTH.getCode(), predictType)) {
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_LAST_YEAR + "(" + stepType + ")", "", child1);
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_CUR_YEAR, "", child2);
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_LAST_PERIOD, "", child3);
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_AI_AUTO + "(" + stepType + ")", "", child4);
        } else if (StringUtils.equals(PredictionEnum.YEAR.getCode(), predictType)) {
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_LAST_YEAR + "(" + (curYear - 1) + ")", "", child1);
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_AI_PREDICT + " (" + curYear + ")", "", child2);
            buildTableHeaderVo(subHeader2, CommonConstant.TITLE_LAST_CALC + " (" + getAIyear(periodId) + ")", "", child4);
        }
        tableHeader2.setChildren(subHeader2);
        header.add(tableHeader2);
    }


    private static int getAIyear(Long periodId){
        Calendar systemCruYear = DateUtil.getPeriodCalendar(null);
        Calendar periodCalendar = DateUtil.getPeriodCalendar(String.valueOf(periodId));
        if(periodCalendar.get(Calendar.YEAR) == systemCruYear.get(Calendar.YEAR)
                && (periodCalendar.get(Calendar.MONTH) + 1) < 10){
            return periodCalendar.get(Calendar.YEAR);
        }else {
            return periodCalendar.get(Calendar.YEAR) + 1 ;
        }
    }

    /**
     * buildTableHeaderVo 表头构建
     *
     * @param header header
     * @param title  title
     * @param field  field
     */
    public static void buildTableL2SubMonthHeaderVo(List<TableHeaderVo> header, String title, String field,
                                                    Map<String, String> child1, Map<String, String> child2, Map<String, String> child3,
                                                    Map<String, String> child4, String stepType) {
        TableHeaderVo tableHeader2 = buildTableHeaderVo(title, field);
        List<TableHeaderVo> subHeader2 = new ArrayList<>();
        buildTableHeaderVo(subHeader2, CommonConstant.TITLE_LAST_YEAR + "(" + stepType + ")", "", child1);
        buildTableHeaderVo(subHeader2, CommonConstant.TITLE_CUR_YEAR, "", child2);
        buildTableHeaderVo(subHeader2, CommonConstant.TITLE_LAST_PERIOD, "", child3);
        buildTableHeaderVo(subHeader2, CommonConstant.TITLE_AI_AUTO+ "(" + stepType + ")", "", child4);
        tableHeader2.setChildren(subHeader2);
        header.add(tableHeader2);
    }

    /**
     * buildAuditTableHeaderVo
     *
     * @param headers       headers
     * @param childerHeader childerHeader
     */
    public static void buildAuditTableHeaderVo(List<HeaderVo> headers, Map<String, String> childerHeader) {
        childerHeader.forEach((k, v) -> {
            HeaderVo headerVo = new HeaderVo();
            headerVo.setTitle(v);
            headerVo.setField(k);
            headers.add(headerVo);
        });
    }

    /**
     * buildExcelVO EXCEL基本构建
     *
     * @param list         list
     * @param headerVoList headerVoList
     */
    public static void buildExcelVO(List<ExcelVO> list, List<HeaderVo> headerVoList) {
        for (HeaderVo header : headerVoList) {
            ExcelVO vo = new ExcelVO();
            vo.setHeadName(header.getTitle());
            vo.setHeadType(CommonConstant.VARCHAR);
            list.add(vo);
        }
    }

    public static List<ExcelVO> buildExcelVO(List<TableHeaderVo> tableHeaderVos) {
        List<ExcelVO> list = new ArrayList<>();
        for (TableHeaderVo header : tableHeaderVos) {
            ExcelVO vo = new ExcelVO();
            vo.setHeadName(header.getTitle());
            vo.setHeadType(CommonConstant.VARCHAR);
            list.add(vo);
        }
        return list;
    }

    /**
     * buildHeader 标签表头构建
     *
     * @param header header
     * @return List<TableHeaderVo>
     */
    public static List<TableHeaderVo> buildHeader(Map<String, String> header) {
        List<TableHeaderVo> headers = new ArrayList<>();
        header.entrySet().forEach(entry -> {
            TableHeaderVo tableHeaderVo = new TableHeaderVo();
            tableHeaderVo.setTitle(entry.getValue());
            tableHeaderVo.setField(entry.getKey());
            headers.add(tableHeaderVo);
        });
        return headers;
    }

    /**
     * buildHeaders 表头构建
     *
     * @param excelList excelList
     * @param map       map
     * @return List<HeaderVo>
     */
    public static List<HeaderVo> buildHeaders(List<ExcelVO> excelList, Map<String, String> map) {
        List<HeaderVo> headers = new LinkedList<>();
        HeaderUtils.buildAuditTableHeaderVo(headers, map);
        HeaderUtils.buildExcelVO(excelList, headers);
        return headers;
    }

    /**
     * buildMonthHeadersByBg 表头构建
     *
     * @return List<TableHeaderVo>
     */
    public static List<TableHeaderVo> buildMonthHeadersByBg(String bgCode, String stepType, long periodId,String predictType) {
        List<TableHeaderVo> headers = new ArrayList<>();
        int curYear = TimeUtils.getCurYear(periodId);
        buildTableHeaderVo(headers, "指标(损益口径)", "", LV1_CHILD_TOP_HEADER);
        if (StringUtils.equals(PredictionEnum.MONTH.getCode(),predictType)){
            setMonthExportExcelHeader(bgCode, stepType, headers);
        }else if(StringUtils.equals(PredictionEnum.YEAR.getCode(),predictType)){
            if (StringUtils.equals(bgCode, BgEnum.GROUP.getCode())) {
                buildTableHeaderVo(headers, CommonConstant.TITLE_LAST_YEAR + "(" + (curYear - 1) + ")", "", LV1_CHILD1_HEADER);
                buildTableHeaderVo(headers, CommonConstant.TITLE_AI_PREDICT + "(" + (curYear) + ")", "", LV1_CHILD4_HEADER);
                buildTableHeaderVo(headers, CommonConstant.TITLE_LAST_CALC + "(" + getAIyear(periodId) + ")", "", LV1_CHILD5_HEADER);
            } else if (StringUtils.equals(bgCode, BgEnum.EBG.getCode())) {
                buildTableSubHeaderVo(headers, "EBG", "", LV1_CHILD_MONTH_EBG1_HEADER, LV1_CHILD_MONTH_EBG4_HEADER, null, LV1_CHILD_MONTH_EBG5_HEADER, null, periodId, PredictionEnum.YEAR.getCode());
            } else if (StringUtils.equals(bgCode, BgEnum.CNBG.getCode())) {
                buildTableSubHeaderVo(headers, "CNBG", "", LV1_CHILD_MONTH_CNBG1_HEADER, LV1_CHILD_MONTH_CNBG4_HEADER, null, LV1_CHILD_MONTH_CNBG5_HEADER, null, periodId, PredictionEnum.YEAR.getCode());
            } else {
                buildTableSubHeaderVo(headers, "CNBG", "", LV1_CHILD_MONTH_CNBG1_HEADER, LV1_CHILD_MONTH_CNBG4_HEADER, null, LV1_CHILD_MONTH_CNBG5_HEADER, null, periodId, PredictionEnum.YEAR.getCode());
                buildTableSubHeaderVo(headers, "EBG", "", LV1_CHILD_MONTH_EBG1_HEADER, LV1_CHILD_MONTH_EBG4_HEADER, null, LV1_CHILD_MONTH_EBG5_HEADER, null, periodId, PredictionEnum.YEAR.getCode());
            }
        }
        return headers;
    }

    private static void setMonthExportExcelHeader(String bgCode, String stepType, List<TableHeaderVo> headers) {
        if("open".equalsIgnoreCase(FcstGlobalParameterUtil.getRegistryValue("App.Config.Profits.MonthlyForecast","ExportHeaderSwitch"))){
            setSnapshotMonthExportExcelHeader(bgCode,stepType,headers);
        }else {
            setMonthExportExcelByBgCodeHeader(bgCode,stepType,headers);
        }
    }

    /**
     * 正式版本代码 20240613 修改
     * @param bgCode bgCode
     * @param stepType stepType
     * @param headers headers
     */
    private static void setMonthExportExcelByBgCodeHeader(String bgCode, String stepType, List<TableHeaderVo> headers){
        if (StringUtils.equals(bgCode, BgEnum.GROUP.getCode())) {
            buildTableHeaderVo(headers, CommonConstant.TITLE_LAST_YEAR + "(" + stepType + ")", "", LV1_CHILD1_HEADER);
            buildTableHeaderVo(headers, CommonConstant.TITLE_CUR_YEAR, "", LV1_CHILD2_HEADER);
            buildTableHeaderVo(headers, CommonConstant.TITLE_LAST_PERIOD, "", LV1_CHILD3_HEADER);
            buildTableHeaderVo(headers, CommonConstant.TITLE_AI_AUTO +"(" + stepType + ")", "", LV1_CHILD4_HEADER);
            buildTableHeaderVo(headers, CommonConstant.TITLE_COMBINED_EXPERT +"(" + stepType + ")", "", LV1_CHILD6_HEADER);
            buildTableHeaderVo(headers, CommonConstant.TITLE_GROUP_ANALYST +"(" + stepType + ")", "", LV1_CHILD7_HEADER);
            buildTableHeaderVo(headers, CommonConstant.TITLE_COMBINED_EXPERT_GROUP +"(" + stepType + ")", "", LV1_CHILD8_HEADER);
        } else if (StringUtils.equals(bgCode, BgEnum.EBG.getCode())) {
            buildTableSubHeaderVo(headers, "EBG", "", LV1_CHILD_MONTH_EBG1_HEADER, LV1_CHILD_MONTH_EBG2_HEADER,
                    LV1_CHILD_MONTH_EBG3_HEADER, LV1_CHILD_MONTH_EBG4_HEADER,LV1_CHILD_MONTH_EBG6_HEADER, stepType, 0, PredictionEnum.MONTH.getCode());
        } else if (StringUtils.equals(bgCode, BgEnum.CNBG.getCode())) {
            buildTableSubHeaderVo(headers, "CNBG", "", LV1_CHILD_MONTH_CNBG1_HEADER, LV1_CHILD_MONTH_CNBG2_HEADER,
                    LV1_CHILD_MONTH_CNBG3_HEADER, LV1_CHILD_MONTH_CNBG4_HEADER,LV1_CHILD_MONTH_CNBG6_HEADER, stepType, 0, PredictionEnum.MONTH.getCode());
        } else {
            buildTableSubHeaderVo(headers, "CNBG", "", LV1_CHILD_MONTH_CNBG1_HEADER, LV1_CHILD_MONTH_CNBG2_HEADER,
                    LV1_CHILD_MONTH_CNBG3_HEADER, LV1_CHILD_MONTH_CNBG4_HEADER, LV1_CHILD_MONTH_CNBG6_HEADER, stepType, 0, PredictionEnum.MONTH.getCode());
            buildTableSubHeaderVo(headers, "EBG", "", LV1_CHILD_MONTH_EBG1_HEADER, LV1_CHILD_MONTH_EBG2_HEADER,
                    LV1_CHILD_MONTH_EBG3_HEADER, LV1_CHILD_MONTH_EBG4_HEADER,LV1_CHILD_MONTH_EBG6_HEADER, stepType, 0, PredictionEnum.MONTH.getCode());
        }
    }

    /**
     * 临时版本代码 20240613 修改
     * @param bgCode bgCode
     * @param stepType stepType
     * @param headers headers
     */
    private static void setSnapshotMonthExportExcelHeader(String bgCode, String stepType, List<TableHeaderVo> headers) {
        if (StringUtils.equals(bgCode, BgEnum.GROUP.getCode())) {
            buildTableHeaderVo(headers, CommonConstant.TITLE_LAST_YEAR + "(" + stepType + ")", "", LV1_CHILD1_HEADER);
            buildTableHeaderVo(headers, CommonConstant.TITLE_CUR_YEAR, "", LV1_CHILD2_HEADER);
            buildTableHeaderVo(headers, CommonConstant.TITLE_LAST_PERIOD, "", LV1_CHILD3_HEADER);
            buildTableHeaderVo(headers, CommonConstant.TITLE_AI_AUTO +"(" + stepType + ")", "", LV1_CHILD4_HEADER);
        } else if (StringUtils.equals(bgCode, BgEnum.EBG.getCode())) {
            buildTableSubHeaderVo(headers, "EBG", "", LV1_CHILD_MONTH_EBG1_HEADER, LV1_CHILD_MONTH_EBG2_HEADER,
                    LV1_CHILD_MONTH_EBG3_HEADER, LV1_CHILD_MONTH_EBG4_HEADER, stepType, 0, PredictionEnum.MONTH.getCode());
        } else if (StringUtils.equals(bgCode, BgEnum.CNBG.getCode())) {
            buildTableSubHeaderVo(headers, "CNBG", "", LV1_CHILD_MONTH_CNBG1_HEADER, LV1_CHILD_MONTH_CNBG2_HEADER,
                    LV1_CHILD_MONTH_CNBG3_HEADER, LV1_CHILD_MONTH_CNBG4_HEADER, stepType, 0, PredictionEnum.MONTH.getCode());
        } else {
            buildTableSubHeaderVo(headers, "CNBG", "", LV1_CHILD_MONTH_CNBG1_HEADER, LV1_CHILD_MONTH_CNBG2_HEADER,
                    LV1_CHILD_MONTH_CNBG3_HEADER, LV1_CHILD_MONTH_CNBG4_HEADER, stepType, 0, PredictionEnum.MONTH.getCode());
            buildTableSubHeaderVo(headers, "EBG", "", LV1_CHILD_MONTH_EBG1_HEADER, LV1_CHILD_MONTH_EBG2_HEADER,
                    LV1_CHILD_MONTH_EBG3_HEADER, LV1_CHILD_MONTH_EBG4_HEADER, stepType, 0, PredictionEnum.MONTH.getCode());
        }
    }


    /**
     * buildL2Header L2表头内容填充
     *
     * @param l1Name l1Name
     * @return List<TableHeaderVo>
     */
    public static List<TableHeaderVo> buildL2Header(String l1Name, String bgCode, ForecastsRequest forecastsRequest) {
        List<TableHeaderVo> header = new ArrayList<>();
        Map<String, String> L2_CHILD_TOP_HEADER = new LinkedHashMap<>();
        L2_CHILD_TOP_HEADER.put("l2Name", "");
        buildTableHeaderVo(header, l1Name, "", L2_CHILD_TOP_HEADER);
        if (PredictionEnum.MONTH.getCode().equals(forecastsRequest.getPredictionType())) {
            String stepType = CommonConstant.FCST_YEAR_STEP;
            if (StringUtils.equals(forecastsRequest.getFcstStep(), CommonConstant.FCST_HALF_YEAR_STEP)) {
                stepType = DateUtil.getSemiAnnual(String.valueOf(forecastsRequest.getPeriodId()));
            }
            if (StringUtils.equals(forecastsRequest.getFcstStep(), CommonConstant.FCST_QUARTER_STEP)) {
                stepType =DateUtil.getQuarter(String.valueOf(forecastsRequest.getPeriodId()));
            }
            if (StringUtils.equals(bgCode, BgEnum.GROUP.getCode())) {
                buildTableHeaderVo(header, CommonConstant.TITLE_LAST_YEAR + "(" + stepType + ")", "", L2_CHILD1_HEADER);
                buildTableHeaderVo(header, CommonConstant.TITLE_CUR_YEAR, "", L2_CHILD2_HEADER);
                buildTableHeaderVo(header, CommonConstant.TITLE_LAST_PERIOD, "", L2_CHILD3_HEADER);
                buildTableHeaderVo(header, CommonConstant.TITLE_AI_AUTO + "(" + stepType + ")", "", L2_CHILD4_HEADER);
            } else if (StringUtils.equals(bgCode, BgEnum.EBG.getCode())) {
                buildTableL2SubMonthHeaderVo(header, "EBG", "", LV2_CHILD_EBG1_HEADER, LV2_CHILD_EBG2_HEADER,
                        LV2_CHILD_EBG3_HEADER, LV2_CHILD_EBG4_HEADER, stepType);
            } else if (StringUtils.equals(bgCode, BgEnum.CNBG.getCode())) {
                buildTableL2SubMonthHeaderVo(header, "CNBG", "", LV2_CHILD_CNBG1_HEADER, LV2_CHILD_CNBG2_HEADER,
                        LV2_CHILD_CNBG3_HEADER, LV2_CHILD_CNBG4_HEADER, stepType);
            } else {
                buildTableL2SubMonthHeaderVo(header, "CNBG", "", LV2_CHILD_CNBG1_HEADER, LV2_CHILD_CNBG2_HEADER,
                        LV2_CHILD_CNBG3_HEADER, LV2_CHILD_CNBG4_HEADER, stepType);
                buildTableL2SubMonthHeaderVo(header, "EBG", "", LV2_CHILD_EBG1_HEADER, LV2_CHILD_EBG2_HEADER,
                        LV2_CHILD_EBG3_HEADER, LV2_CHILD_EBG4_HEADER, stepType);
            }
        } else if(PredictionEnum.YEAR.getCode().equals(forecastsRequest.getPredictionType())){
            int curYear = TimeUtils.getCurYear(forecastsRequest.getPeriodId());
            if (StringUtils.equals(bgCode, BgEnum.GROUP.getCode())) {
                buildTableHeaderVo(header, CommonConstant.TITLE_LAST_YEAR + "(" + (curYear - 1) + ")", "", L2_CHILD1_HEADER);
                buildTableHeaderVo(header, CommonConstant.TITLE_AI_PREDICT + "(" + (curYear) + ")", "", L2_CHILD4_HEADER);
                buildTableHeaderVo(header, CommonConstant.TITLE_LAST_CALC + "(" + getAIyear(forecastsRequest.getPeriodId()) + ")", "", L2_CHILD5_HEADER);
            } else if (StringUtils.equals(bgCode, BgEnum.EBG.getCode())) {
                buildTableSubHeaderVo(header, "EBG", "", LV2_CHILD_EBG1_HEADER, LV2_CHILD_EBG4_HEADER, null,
                        LV2_CHILD_EBG5_HEADER, null, forecastsRequest.getPeriodId(), PredictionEnum.YEAR.getCode());
            } else if (StringUtils.equals(bgCode, BgEnum.CNBG.getCode())) {
                buildTableSubHeaderVo(header, "CNBG", "", LV2_CHILD_CNBG1_HEADER, LV2_CHILD_CNBG4_HEADER, null,
                        LV2_CHILD_CNBG5_HEADER, null, forecastsRequest.getPeriodId(), PredictionEnum.YEAR.getCode());
            } else {
                buildTableSubHeaderVo(header, "CNBG", "", LV2_CHILD_CNBG1_HEADER, LV2_CHILD_CNBG4_HEADER, null,
                        LV2_CHILD_CNBG5_HEADER, null, forecastsRequest.getPeriodId(), PredictionEnum.YEAR.getCode());
                buildTableSubHeaderVo(header, "EBG", "", LV2_CHILD_EBG1_HEADER, LV2_CHILD_EBG4_HEADER, null,
                        LV2_CHILD_EBG5_HEADER, null, forecastsRequest.getPeriodId(), PredictionEnum.YEAR.getCode());
            }
        }
        return header;
    }

    /**
     * buildHeader 表头项内容填充
     *
     * @param titleVoList titleVoList
     * @param titles      titles
     * @param headerList  headerList
     */
    public static void buildHeader(List<AbstractExcelTitleVO> titleVoList, Set<String> titles,
                                   List<TableHeaderVo> headerList) {
        int column = 0;
        for (TableHeaderVo tableHeaderVo : headerList) {
            column++;
            if (CollectionUtil.isNullOrEmpty(tableHeaderVo.getChildren())) {
                Integer tileWith = Optional.ofNullable(tableHeaderVo.getTitle()).orElse("").length() > 5 ? tableHeaderVo.getTitle().length() * 640 : 6 * 640 ;
                LeafExcelTitleVO chnRelOutColumn1 = new LeafExcelTitleVO(tableHeaderVo.getTitle(), tileWith, true,
                        tableHeaderVo.getField(), "column" + column, CellType.STRING, "@");
                titles.add(tableHeaderVo.getField());
                titleVoList.add(chnRelOutColumn1);
            } else {
                BranchExcelTitleVO chnRelOutColumn9 = new BranchExcelTitleVO(tableHeaderVo.getTitle(), 12 * 640);
                titleVoList.add(chnRelOutColumn9);
                List<TableHeaderVo> children = tableHeaderVo.getChildren();
                for (TableHeaderVo headers : children) {
                    Integer tileWith = Optional.ofNullable(headers.getTitle()).orElse("").length() > 5 ? headers.getTitle().length() * 640 : 6 * 640 ;
                    LeafExcelTitleVO chnRelOutColumn2 = new LeafExcelTitleVO(headers.getTitle(), tileWith, true,
                            headers.getField(), "column" + column, CellType.NUMERIC, "0.000000");
                    chnRelOutColumn9.addChild(chnRelOutColumn2);
                    titles.add(tableHeaderVo.getField());
                }
            }
        }
    }

}
