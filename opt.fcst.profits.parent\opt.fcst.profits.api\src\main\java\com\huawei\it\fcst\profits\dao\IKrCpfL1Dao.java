/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.dao;

import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.response.KrCpfL1Response;

import java.util.List;

/**
 * The DAO to access KrCpfL1ActVO entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:25:12
 */
public interface IKrCpfL1Dao {
    /**
     * 获取l2_act信息
     *
     * @param forecastsRequest forecastsRequest
     * @return List<String>
     */
    List<KrCpfL1Response> getL1TotalInfo(ForecastsRequest forecastsRequest);

    /**
     * 导出l1因子数据
     * @param forecastsRequest 参数
     * @return list
     */
    List<KrCpfL1Response> exportL1Factor(ForecastsRequest forecastsRequest);

    /**
     * 查询条数
     *
     * @param forecastsRequest 查询条件
     * @return 条数
     */
    int  exportL1FactorCount(ForecastsRequest forecastsRequest);
}
