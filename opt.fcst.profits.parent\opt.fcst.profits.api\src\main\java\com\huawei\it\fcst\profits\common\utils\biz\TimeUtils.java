/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.utils.biz;

import com.huawei.it.fcst.profits.common.utils.AesGcmUtil;

import cn.hutool.core.date.format.FastDateFormat;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * TimeUtils
 *
 * <AUTHOR>
 * @since 2020/06/02.
 */
public class TimeUtils {

    private static final Logger logger = LoggerFactory.getLogger(TimeUtils.class);

    private static final int LAST_MONTH = 1;

    private static final int SELECTED_YEAR_START = 0;

    private static final int SELECTED_YEAR_END = 4;

    private static final int SELECTED_MONTH_START = 4;

    private static final int SELECTED_MONTH_END = 6;

    private static final int RANDOM_NUM_LENGTH = 900;

    private static final int RANDOM_NUM_MAX_LIMIT = 100;

    /**
     * 获取结束时间
     *
     * @param yearMonth yearMonth
     * @return String
     */
    public static String getEndTime(String yearMonth) {
        Integer year = Integer.valueOf(yearMonth.trim().substring(SELECTED_YEAR_START, SELECTED_YEAR_END));
        Integer month = Integer.valueOf(yearMonth.trim().substring(SELECTED_MONTH_START, SELECTED_MONTH_END));
        String format = "yyyyMMdd";
        Calendar cale = Calendar.getInstance();
        cale.set(Calendar.YEAR, year);
        cale.set(Calendar.MONTH, month - LAST_MONTH);
        int endDay = cale.getActualMaximum(Calendar.DAY_OF_MONTH);
        cale.set(Calendar.DAY_OF_MONTH, endDay);
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String lastDayOfMonth = sdf.format(cale.getTime());
        return lastDayOfMonth + " 23:59:59";
    }

    /**
     * 获取开始时间
     *
     * @param yearMonth yearMonth
     * @return String
     */
    public static String getBeginTime(String yearMonth) {
        return yearMonth.trim().substring(SELECTED_YEAR_START, SELECTED_YEAR_END) + "" + yearMonth.trim()
                .substring(SELECTED_MONTH_START, SELECTED_MONTH_END) + "" + "01 00:00:00";
    }

    /**
     * 获取会计期
     *
     * @param curYear curYear
     * @param num     num
     * @return String
     */
    public static String getPeriod(int curYear, Integer num) {
        return String.valueOf(curYear + num);
    }

    /**
     * 获取时间错
     *
     * @return Timestamp
     */
    public static Timestamp getCurTime() {
        Date dt = new Date();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String nowTime = df.format(dt);
        return java.sql.Timestamp.valueOf(nowTime);
    }

    /**
     * 获取批次号
     *
     * @return String
     */
    public static String getBathNo() {
        String curNo = DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
        int num = AesGcmUtil.getSecureRandom();
        return curNo + num;
    }

    /**
     * 获取会计期
     *
     * @return String
     */
    public static String getCurPeriod() {
        Calendar calendar = Calendar.getInstance();
        String curYear = String.valueOf(calendar.get(Calendar.YEAR));
        String month = StringUtils.leftPad(String.valueOf(calendar.get(Calendar.MONTH) + 1), 2, "0");
        return curYear + month;
    }

    /**
     * 获取当月的那一天
     *
     * @return int
     */
    public static int getDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DATE);
    }

    /**
     * 获取年份
     *
     * @param periodId periodId
     * @return int
     */
    public static int getCurYear(long periodId) {
        if (String.valueOf(periodId).length() == 6) {
            return Integer.valueOf(String.valueOf(periodId).substring(0, 4));
        }
        return 0;
    }

    /**
     * 获取月份
     *
     * @param periodId periodId
     * @return int
     */
    public static int getCurMonth(long periodId) {
        if (String.valueOf(periodId).length() == 6) {
            return Integer.valueOf(String.valueOf(periodId).substring(4, 6));
        }
        return 0;
    }

    /**
     * 获取当前年月
     */
    public static String getCurYear() {
        return FastDateFormat.getInstance("yyyyMM").format(new Date());
    }

    /**
     * 获取当前时间戳
     *
     * @return String
     */
    public static String getNowTime() {
        return FastDateFormat.getInstance("yyyyMMddHHmmss").format(new Date());
    }

    /**
     * 获取当前时间戳
     *
     * @return String
     */
    public static boolean getNowHHTime(int day) {
        Long hHmmss = Long.valueOf(FastDateFormat.getInstance("HHmmss").format(new Date()));
        if (day >= 7 && day < 20) {
            return true;
        } else if (day == 20 && (hHmmss >= Long.valueOf(0) && hHmmss <= Long.valueOf(120000))) {
            return true;
        }
        return false;
    }

    public static String getNowTimeAtMin() {
        return FastDateFormat.getInstance("yyyyMMdd-HHmm").format(new Date());
    }
}
