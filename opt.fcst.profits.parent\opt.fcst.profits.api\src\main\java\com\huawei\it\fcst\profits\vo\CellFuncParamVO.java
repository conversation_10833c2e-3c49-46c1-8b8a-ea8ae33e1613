/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.util.Map;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年06月15日
 */
@Setter@Getter
@XmlAccessorType(XmlAccessType.FIELD)
@JsonIgnoreProperties(ignoreUnknown=true,value={"key"})
public class CellFuncParamVO {
    private String funcName;
    private String status;

    /**
     * 秘钥管理，不参与序列化
     */
    private String key;
    private Map<String,Object> params;

}
