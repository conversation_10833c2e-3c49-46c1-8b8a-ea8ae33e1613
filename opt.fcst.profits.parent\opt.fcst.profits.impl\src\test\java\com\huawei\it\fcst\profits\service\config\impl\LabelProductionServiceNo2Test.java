package com.huawei.it.fcst.profits.service.config.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

import com.huawei.it.fcst.profits.common.user.JalorUserTools;
import com.huawei.it.fcst.profits.common.utils.ConfigUtil;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.dao.ILabelProductionDao;
import com.huawei.it.fcst.profits.vo.PlanComProdInfoVo;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.request.LabelConfigQueryRequest;
import com.huawei.it.fcst.profits.vo.request.SpartInfoRequest;
import com.huawei.it.fcst.profits.vo.response.ProfitGranularitySpartInfoVo;
import com.huawei.it.fcst.profits.vo.response.SpartInfoVo;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.security.UserVO;
import com.huawei.it.jalor5.security.service.IUserQueryService;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.rule.PowerMockRule;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

@PrepareForTest( {ConfigUtil.class})
public class LabelProductionServiceNo2Test {

    @Rule
    public PowerMockRule rule = new PowerMockRule();

    @InjectMocks
    private LabelProductionService labelProductionServiceUnderTest;

    @Mock
    private ILabelProductionDao mockILabelProductionDao;

    @Mock
    private JalorUserTools mockJalorUserTools;

    @Mock
    private IUserQueryService mockIUserQueryService;

    @Before
    public void setup() {
        MockitoAnnotations.openMocks(this);
        PowerMockito.mockStatic(ConfigUtil.class);
        ConfigUtil configUtil = PowerMockito.mock(ConfigUtil.class);
        PowerMockito.when(ConfigUtil.getInstance()).thenReturn(configUtil);
    }

    @Test
    public void testGetProfitExaminingSpartMonthCostInfo() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingSpartMonthCostInfo(...).
        final ProfitGranularitySpartInfoVo profitGranularitySpartInfoVo = new ProfitGranularitySpartInfoVo();
        profitGranularitySpartInfoVo.setLv1Code("lv1Code");
        profitGranularitySpartInfoVo.setBgCode("bgCode");
        profitGranularitySpartInfoVo.setL1Name("l1Name");
        profitGranularitySpartInfoVo.setL2Name("l2Name");
        profitGranularitySpartInfoVo.setLv1Name("lv1Name");
        final List<ProfitGranularitySpartInfoVo> profitGranularitySpartInfoVos = Arrays.asList(
            profitGranularitySpartInfoVo);
        when(mockILabelProductionDao.getProfitExaminingSpartMonthCostInfo(any())).thenReturn(
            profitGranularitySpartInfoVos);

        // Run the test
        final Map<String, List<ProfitGranularitySpartInfoVo>> result
            = labelProductionServiceUnderTest.getProfitExaminingSpartMonthCostInfo(requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    public void testGetProfitExaminingSpartMonthRevtInfo() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingSpartMonthCostInfo(...).
        final ProfitGranularitySpartInfoVo profitGranularitySpartInfoVo = new ProfitGranularitySpartInfoVo();
        profitGranularitySpartInfoVo.setLv1Code("lv1Code");
        profitGranularitySpartInfoVo.setBgCode("bgCode");
        profitGranularitySpartInfoVo.setL1Name("l1Name");
        profitGranularitySpartInfoVo.setL2Name("l2Name");
        profitGranularitySpartInfoVo.setLv1Name("lv1Name");
        final List<ProfitGranularitySpartInfoVo> profitGranularitySpartInfoVos = Arrays.asList(
            profitGranularitySpartInfoVo);
        when(mockILabelProductionDao.getProfitExaminingSpartMonthRevInfo(any())).thenReturn(
            profitGranularitySpartInfoVos);

        // Run the test
        final Map<String, List<ProfitGranularitySpartInfoVo>> result
            = labelProductionServiceUnderTest.getProfitExaminingSpartMonthRevInfo(requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    public void testGetProfitExaminingSpartMonthInfo_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        when(mockILabelProductionDao.getProfitExaminingSpartMonthCostInfo(any(ForecastsRequest.class))).thenReturn(
            Collections.emptyList());

        // Run the test
        final Map<String, List<ProfitGranularitySpartInfoVo>> result
            = labelProductionServiceUnderTest.getProfitExaminingSpartMonthCostInfo(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyMap());
    }

    @Test
    public void testGetProfitExaminingSpartYtdCostInfo() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingSpartYtdCostInfo(...).
        final ProfitGranularitySpartInfoVo profitGranularitySpartInfoVo = new ProfitGranularitySpartInfoVo();
        profitGranularitySpartInfoVo.setLv1Code("lv1Code");
        profitGranularitySpartInfoVo.setBgCode("bgCode");
        profitGranularitySpartInfoVo.setL1Name("l1Name");
        profitGranularitySpartInfoVo.setL2Name("l2Name");
        profitGranularitySpartInfoVo.setLv1Name("lv1Name");
        final List<ProfitGranularitySpartInfoVo> profitGranularitySpartInfoVos = Arrays.asList(
            profitGranularitySpartInfoVo);
        when(mockILabelProductionDao.getProfitExaminingSpartYtdCostInfo(any(ForecastsRequest.class))).thenReturn(
            profitGranularitySpartInfoVos);

        // Run the test
        final Map<String, List<ProfitGranularitySpartInfoVo>> result
            = labelProductionServiceUnderTest.getProfitExaminingSpartYtdCostInfo(requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    public void testGetProfitExaminingSpartYtdRevInfo() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        // Configure ILabelProductionDao.getProfitExaminingSpartYtdRevInfo(...).
        final ProfitGranularitySpartInfoVo profitGranularitySpartInfoVo = new ProfitGranularitySpartInfoVo();
        profitGranularitySpartInfoVo.setLv1Code("lv1Code");
        profitGranularitySpartInfoVo.setBgCode("bgCode");
        profitGranularitySpartInfoVo.setL1Name("l1Name");
        profitGranularitySpartInfoVo.setL2Name("l2Name");
        profitGranularitySpartInfoVo.setLv1Name("lv1Name");
        final List<ProfitGranularitySpartInfoVo> profitGranularitySpartInfoVos = Arrays.asList(
            profitGranularitySpartInfoVo);
        when(mockILabelProductionDao.getProfitExaminingSpartYtdRevInfo(any(ForecastsRequest.class))).thenReturn(
            profitGranularitySpartInfoVos);

        // Run the test
        final Map<String, List<ProfitGranularitySpartInfoVo>> result
            = labelProductionServiceUnderTest.getProfitExaminingSpartYtdRevInfo(requestVO);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    public void testGetProfitExaminingSpartYtdInfo_ILabelProductionDaoReturnsNoItems() throws Exception {
        // Setup
        final ForecastsRequest requestVO = new ForecastsRequest();
        requestVO.setPageIndex(0);
        requestVO.setPageSize(0);
        requestVO.setFcstStep("fcstStep");
        requestVO.setBgCode(new String[] {"bgCode"});
        requestVO.setBgCodes(Arrays.asList("value"));
        requestVO.setTargetPeriods(Arrays.asList("value"));
        requestVO.setPredictionType("predictionType");
        requestVO.setFcstStepPeriod("fcstStepPeriod");
        requestVO.setActStepPeriod("actStepPeriod");
        requestVO.setLevel("level");
        requestVO.setCurYear(2020);
        requestVO.setCurMonth(1);
        requestVO.setRoleId(0);
        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));
        requestVO.setRightVO(rightVO);

        when(mockILabelProductionDao.getProfitExaminingSpartYtdCostInfo(any(ForecastsRequest.class))).thenReturn(
            Collections.emptyList());

        // Run the test
        final Map<String, List<ProfitGranularitySpartInfoVo>> result
            = labelProductionServiceUnderTest.getProfitExaminingSpartYtdCostInfo(requestVO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyMap());
    }

    @Test
    public void testGetPersonalSpartInfoPage() throws Exception {
        // Setup
        final SpartInfoRequest requestVO = new SpartInfoRequest();

        when(mockILabelProductionDao.getPersonalSpartInfoPage(any(SpartInfoRequest.class),any())).thenReturn(
            (new PagedResult<>()));

        // Run the test
        final PagedResult<SpartInfoVo> result = labelProductionServiceUnderTest.getPersonalSpartInfoPage(requestVO, new PageVO());

        // Verify the results
        Assertions.assertNotNull(result);
    }

    @Test
    public void testCheckUserPermissionAllProd() throws Exception {
        // Setup

        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(false);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));

        when(mockJalorUserTools.getRolePermission(anyInt())).thenReturn(rightVO);

        // Run the test
        final boolean result = labelProductionServiceUnderTest.checkUserPermissionAllProd(1000);

        // Verify the results
        assertThat(result).isEqualTo(false);
    }

    @Test
    public void testCheckUserPermissionAllProdA() throws Exception {
        // Setup

        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(true);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("lv1DataType");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));

        when(mockJalorUserTools.getRolePermission(anyInt())).thenReturn(rightVO);
        // Run the test
        final boolean result = labelProductionServiceUnderTest.checkUserPermissionAllProd(1000);

        // Verify the results
        assertThat(result).isEqualTo(false);
    }

    @Test
    public void testCheckUserPermissionAllProdB() throws Exception {
        // Setup

        final DataPermissionsVO rightVO = new DataPermissionsVO();
        rightVO.setOptLv1Right(true);
        rightVO.setOptBgRight(false);
        rightVO.setLv1DataType("ALL");
        rightVO.setBgDataType("bgDataType");
        rightVO.setLv1DimensionSet(new HashSet<>(Arrays.asList("value")));
        rightVO.setBgDimensionSet(new HashSet<>(Arrays.asList("value")));

        when(mockJalorUserTools.getRolePermission(anyInt())).thenReturn(rightVO);

        // Run the test
        final boolean result = labelProductionServiceUnderTest.checkUserPermissionAllProd(1000);

        // Verify the results
        assertThat(result).isEqualTo(true);
    }

    @Test
    public void testGetProfitExaminingSpartYtdEntries() throws Exception {
        // Setup
        SpartInfoVo infoVo = new SpartInfoVo();
        infoVo.setSpartCode("TestCOde");
        infoVo.setSpartDesc("TestDesc");
        infoVo.setCostPercent("0.1");
        infoVo.setRevPercent(new BigDecimal("0.01"));
        SpartInfoVo infoVo1 = new SpartInfoVo();
        infoVo1.setSpartCode("TestCOde");
        infoVo1.setSpartDesc("TestDesc");
        infoVo1.setCostPercent("0.1");
        infoVo1.setRevPercent(new BigDecimal("0.01"));
        List<SpartInfoVo> lists = new ArrayList<>();
        for (int i = 51; i > 0; i--) {
            lists.add(infoVo);
            lists.add(infoVo1);
        }
        when(mockILabelProductionDao.getProfitExaminingSpartYtdEntries(any())).thenReturn(lists);
        // Run the test
        final Map<String, List<SpartInfoVo>> result = labelProductionServiceUnderTest.getProfitExaminingSpartYtdEntries(
            new ForecastsRequest());
        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    public void testGetProfitExaminingSpartYtdEntries1() throws Exception {
        // Setup
        SpartInfoVo infoVo = new SpartInfoVo();
        infoVo.setSpartCode("TestCOde");
        infoVo.setSpartDesc("TestDesc");
        infoVo.setCostPercent("0.1");
        infoVo.setRevPercent(new BigDecimal("0.1"));
        SpartInfoVo infoVo1 = new SpartInfoVo();
        infoVo1.setSpartCode("TestCOde");
        infoVo1.setSpartDesc("TestDesc");
        infoVo1.setCostPercent("0.1");
        infoVo1.setRevPercent(new BigDecimal("0.01"));
        List<SpartInfoVo> lists = new ArrayList<>();
            lists.add(infoVo);
            lists.add(infoVo1);
        when(mockILabelProductionDao.getProfitExaminingSpartYtdEntries(any())).thenReturn(lists);
        // Run the test
        final Map<String, List<SpartInfoVo>> result
            = labelProductionServiceUnderTest.getProfitExaminingSpartYtdEntries(new ForecastsRequest());
        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    public void testGetCoaMonitorVersion() throws Exception {

        // Run the test
        final String result
            = String.valueOf(labelProductionServiceUnderTest.getCoaMonitorVersion(new LabelConfigQueryRequest()));
        // Verify the results
        Assertions.assertNotNull(Collections.emptyList());
    }
    @Test
    public void testGetPlanComSopInfo() throws Exception {

        // Run the test
        final List<PlanComProdInfoVo> result
            = labelProductionServiceUnderTest.getPlanComSopInfo(new LabelConfigQueryRequest());
        // Verify the results
        Assertions.assertNotNull(Collections.emptyList());
    }
    @Test
    public void testCheckSpartPermission() {
        // Setup

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");

        userAccount.setUserId(123456789L);
        DataPermissionsVO vo = new DataPermissionsVO();
        vo.setLv1DataType("Test");
        PowerMockito.doReturn(vo).when(mockJalorUserTools).getRolePermission(anyInt());

        // Run the test
        final boolean result = labelProductionServiceUnderTest.checkSpartPermission(0);

        // Verify the results
        Assertions.assertNotNull(result);
    }
    @Test
    public void testCheckSpartPermissionA() {
        // Setup

        UserVO userAccount = new UserVO("userAccount");
        userAccount.setUserCN("TestName");

        userAccount.setUserId(123456789L);
        DataPermissionsVO vo = new DataPermissionsVO();
        vo.setLv1DataType("ALL");
        PowerMockito.doReturn(vo).when(mockJalorUserTools).getRolePermission(anyInt());

        // Run the test
        final boolean result = labelProductionServiceUnderTest.checkSpartPermission(0);

        // Verify the results
        Assertions.assertNotNull(result);
    }
}