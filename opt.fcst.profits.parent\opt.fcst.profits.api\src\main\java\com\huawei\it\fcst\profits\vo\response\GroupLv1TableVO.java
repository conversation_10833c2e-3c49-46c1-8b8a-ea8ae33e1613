/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import lombok.Getter;
import lombok.Setter;

/**
 * The Entity of GroupLv1TableVO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-15 16:00:24
 */
@Getter
@Setter
public class GroupLv1TableVO {
    private String key;

    private String l1Name;

    private String lv1Name;

    private String lv2Name;

    private KrCpfLv1AggrResponse lastYearActure;

    private KrCpfLv1AggrResponse curYearSum;

    private KrCpfLv1AggrResponse lastYearPeriod;

    private KrCpfLv1AggrResponse aiAuto;

    private KrCpfLv1AggrResponse cnbgLastYearActure;

    private KrCpfLv1AggrResponse cnbgCurYearSum;

    private KrCpfLv1AggrResponse cnbgLastYearPeriod;

    private KrCpfLv1AggrResponse cnbgAiAuto;

    private KrCpfLv1AggrResponse ebgLastYearActure;

    private KrCpfLv1AggrResponse ebgCurYearSum;

    private KrCpfLv1AggrResponse ebgLastYearPeriod;

    private KrCpfLv1AggrResponse ebgAiAuto;

    private KrCpfLv1AggrResponse ai2Auto;

    private KrCpfLv1AggrResponse ebgAi2Auto;

    private KrCpfLv1AggrResponse cnbgAi2Auto;
}
