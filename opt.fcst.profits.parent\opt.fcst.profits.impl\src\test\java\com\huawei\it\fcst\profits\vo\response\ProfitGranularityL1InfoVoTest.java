/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.huawei.it.fcst.profits.vo.BaseVOCoverUtilsTest;

class ProfitGranularityL1InfoVoTest extends BaseVOCoverUtilsTest<ProfitGranularityL1InfoVo> {
    @Override
    protected Class<ProfitGranularityL1InfoVo> getTClass() {
        return ProfitGranularityL1InfoVo.class;
    }
}