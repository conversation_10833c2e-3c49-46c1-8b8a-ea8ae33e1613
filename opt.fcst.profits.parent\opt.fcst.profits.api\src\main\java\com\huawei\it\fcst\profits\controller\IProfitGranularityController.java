/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.controller;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;

/**
 * IProfitGranularityController
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
@Path("/profitGranularity")
@Consumes( {"application/json"})
@Produces( {"application/json"})
public interface IProfitGranularityController {

    @POST
    @Path("/getProfitExaminingQueryCondition")
    CommonResult getProfitExaminingLv1QueryCondition(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingL1QueryCondition")
    CommonResult getProfitExaminingL1QueryCondition(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingL2QueryCondition")
    CommonResult getProfitExaminingL2QueryCondition(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingLv1MonthInfo")
    CommonResult getProfitExaminingLv1MonthInfo(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingLv1YtdInfo")
    CommonResult getProfitExaminingLv1YtdInfo(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingL1MonthInfo")
    CommonResult getProfitExaminingL1MonthInfo(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingL1YtdInfo")
    CommonResult getProfitExaminingL1YtdInfo(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingL2MonthInfo")
    CommonResult getProfitExaminingL2MonthInfo(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingL2YtdInfo")
    CommonResult getProfitExaminingL2YtdInfo(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingSpartMonthInfo")
    CommonResult getProfitExaminingSpartMonthInfo(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingSpartYtdInfo")
    CommonResult getProfitExaminingSpartYtdInfo(ForecastsRequest requestVO);

    @GET
    @Path("/getPersonalSpartInfo/page/{pageSize}/{curPage}")
    PagedResult getPersonalSpartInfo(@PathParam("") PageVO pageVO, @QueryParam("spartCode") String spartCode,
        @QueryParam("spartDesc") String spartDesc);

    @POST
    @Path("/downloadProfitExaminingSpartInfo")
    CommonResult downloadProfitExaminingSpartInfo(@Context HttpServletResponse response, ForecastsRequest request);

    @POST
    @Path("/getProfitExaminingL3MonthInfo")
    CommonResult getProfitExaminingL3MonthInfo(ForecastsRequest requestVO);

    @POST
    @Path("/getProfitExaminingL3YtdInfo")
    CommonResult getProfitExaminingL3YtdInfo(ForecastsRequest requestVO);

}
