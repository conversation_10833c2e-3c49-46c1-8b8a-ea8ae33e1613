/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.config.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.huawei.it.fcst.profits.common.poi.ExcelUtil;
import com.huawei.it.fcst.profits.common.poi.ExcelUtilPro;
import com.huawei.it.fcst.profits.common.vo.AbstractExcelTitleVO;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

class SpartInfoExporterTest {

    @Mock
    private ExcelUtilPro mockExcelUtilPro;

    @Mock
    private ExcelUtil excelUtil;
    @InjectMocks
    private SpartInfoExporter spartInfoExporterUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeEach
    void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterEach
    void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    void testResetDataList() {
        // Setup
        final List resultResult = new ArrayList<>();
        final List<Map<String, Object>> infoList = new ArrayList<>();
        Map<String, Object> map =new HashMap<>();
        map.put("targetPeriod","2023Q1");
        infoList.add(map);
        Map<String, Object> map1 =new HashMap<>();
        map1.put("test","2023Q1");
        infoList.add(map1);
        // Run the test
        spartInfoExporterUnderTest.resetDataList(resultResult, infoList);

        // Verify the results
        Assertions.assertNotNull(map1);
    }

    @Test
    void testSetSpartInfoMap() {
        // Setup
        final Map<String, Object> params = new HashMap<>();
        final List<AbstractExcelTitleVO> excelTitleVOS = Arrays.asList();
        when(mockExcelUtilPro.adjustTitleVoList(Arrays.asList(), new HashSet<>(Arrays.asList("value")),
            Arrays.asList())).thenReturn(0);

        // Run the test
        spartInfoExporterUnderTest.setSpartInfoMap(params, new HashSet<>(Arrays.asList("value")), excelTitleVOS,
            Arrays.asList("value"), "sheetName");

        // Verify the results
        Assertions.assertNotNull(params);
    }

    @Test
    void testExportData() throws Exception {
        // Setup
        final Map<String, Object> paramsMap = new HashMap<>();
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        when(mockExcelUtilPro.adjustTitleVoList(new LinkedList<>(), new HashSet<>(Arrays.asList("value")),
            Arrays.asList())).thenReturn(0);

        final List<Map<String, Object>> infoList = new ArrayList<>();
        Map<String, Object> map =new HashMap<>();
        map.put("targetPeriod","2023Q1");
        infoList.add(map);
        Map<String, Object> map1 =new HashMap<>();
        map1.put("test","2023Q1");
        infoList.add(map1);
        paramsMap.put("l1Info",infoList);
        // Run the test
        spartInfoExporterUnderTest.exportData(paramsMap, infoList, mockResponse);
        // doNothing().when(excelUtil.downloadAndUploadS3Excel(any(),any(),any()));
        // Verify the results
        Assertions.assertNotNull(paramsMap);
    }
    @Test
    void testExportDataOne() throws Exception {
        // Setup
        final Map<String, Object> paramsMap = new HashMap<>();
        final HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        when(mockExcelUtilPro.adjustTitleVoList(new LinkedList<>(), new HashSet<>(Arrays.asList("value")),
            Arrays.asList())).thenReturn(0);
        doNothing().when(excelUtil).downloadAndUploadS3Excel(any(),any(),any());
        final List<Map<String, Object>> infoList = new ArrayList<>();
        Map<String, Object> map =new HashMap<>();
        map.put("targetPeriod","2023Q1");
        infoList.add(map);
        Map<String, Object> map1 =new HashMap<>();
        map1.put("test","2023Q1");
        infoList.add(map1);
        paramsMap.put("l1Info",infoList);
        paramsMap.put("l2Info",infoList);
        // Run the test
        spartInfoExporterUnderTest.exportData(paramsMap, infoList, mockResponse);
        // doNothing().when(excelUtil.downloadAndUploadS3Excel(any(),any(),any()));
        // Verify the results
        Assertions.assertNotNull(paramsMap);
    }
}
