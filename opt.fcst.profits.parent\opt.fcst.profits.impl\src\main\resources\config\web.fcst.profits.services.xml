<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jaxws="http://cxf.apache.org/jaxws" xmlns:jaxrs="http://cxf.apache.org/jaxrs" xsi:schemaLocation="   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd      http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd      http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd      http://cxf.apache.org/jaxrs http://cxf.apache.org/schemas/jaxrs.xsd">
  <jaxrs:server id="fcstProfitsServiceRest" address="/">
    <jaxrs:serviceBeans>
      <!-- 测试服务 -->
      <ref bean="userCenterController" />
      <ref bean="labelAuditController" />
      <ref bean="labelConfigController" />
      <ref bean="profitGranularityController" />
      <ref bean="forecastsController" />
      <ref bean="forecastDimensionController" />
      <ref bean="ltsTaskIntegrationController" />
      <ref bean="groupAnalystsController" />
    </jaxrs:serviceBeans>
    <jaxrs:providers>
      <ref bean="jsonProvider" />
      <ref bean="errorHandlerProvider" />
      <ref bean="localValidatorExceptionMapper" />
    </jaxrs:providers>
    <jaxrs:features>
      <ref bean="swagger2Feature" />
    </jaxrs:features>
  </jaxrs:server>
  <bean id="commonValidationFeature" class="org.apache.cxf.validation.BeanValidationFeature"/>
  <bean id="localValidatorExceptionMapper" class="com.huawei.it.fcst.profits.service.mapper.LocalValidatorExceptionMapper"/>
  <bean id="swagger2Feature" class="org.apache.cxf.jaxrs.swagger.Swagger2Feature">
    <property name="title" value="盈利预测" />
    <property name="version" value="1.0.0" />
    <property name="prettyPrint" value="true" />
  </bean>
</beans>

