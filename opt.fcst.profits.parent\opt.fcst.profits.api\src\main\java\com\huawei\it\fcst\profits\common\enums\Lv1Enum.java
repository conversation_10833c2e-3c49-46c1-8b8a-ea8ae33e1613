/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * BgEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum Lv1Enum {

    LV1_1("100001", "无线"),
    LV1_2("134557", "光"),
    LV1_3("合并", "合并"),
    LV1_4("121085", "华为云计算"),
    LV1_5("101764", "政企服务"),
    LV1_6("100011", "云核心网"),
    LV1_7("133277", "计算"),
    LV1_8("101775", "数据存储"),
    LV1_9("153930", "机器视觉"),
    LV1_10("100010", "数字能源"),
    LV1_11("137565", "数据通信"),
    LV1_12("100005", "运营商服务与软件");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    Lv1Enum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * getByCode
     *
     * @param code code
     * @return StepTypeEnum
     */
    public static Lv1Enum getByCode(String code) {
        for (Lv1Enum value : Lv1Enum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

