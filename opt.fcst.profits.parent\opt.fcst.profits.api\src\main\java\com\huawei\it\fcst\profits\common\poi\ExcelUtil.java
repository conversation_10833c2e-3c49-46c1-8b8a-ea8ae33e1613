/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.poi;

import cn.hutool.core.util.StrUtil;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.constants.Constants;
import com.huawei.it.fcst.profits.common.constants.RelationConstants;
import com.huawei.it.fcst.profits.common.enums.ModuleEnum;
import com.huawei.it.fcst.profits.common.utils.CalcUtils;
import com.huawei.it.fcst.profits.common.utils.FileProcessUtis;
import com.huawei.it.fcst.profits.common.utils.UserHandle;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.BranchExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.ExcelVO;
import com.huawei.it.fcst.profits.common.vo.LeafExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.UploadInfoVO;
import com.huawei.it.fcst.profits.dao.IDmFopRecordDao;
import com.huawei.it.fcst.profits.vo.DmFopRecordVO;
import com.huawei.it.fcst.profits.vo.request.LabelInfoRequest;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.core.util.StreamUtil;
import com.huawei.it.jalor5.core.util.StringUtil;
import com.huawei.it.jalor5.excel.exception.ExcelExportException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * This class ExcelFactory.java
 *
 * <AUTHOR>
 * @since 2021年2月9日
 */
@Component
public class ExcelUtil {

    private static final String SUPPLEMENT_METHOD = "implSpDataSupplement";

    private static final String ROS_SUPPLEMENT_METHOD = "implApdSpRosBmT";

    private static final String COMPANY_STOCK_METHOD = "implApdSpCompanyStockT";

    private static final String SUPP_TEMPLATE_SUFFIX = "apdExcelModule";

    private static final String ROS_TEMPLATE_SUFFIX = "rosExcelModule";

    private static final String STOCK_TEMPLATE_SUFFIX = "stockExcelModule";

    private static final String EXCEPTION_HEADER_NAME = "异常提示";

    private static final Logger logger = LoggerFactory.getLogger(ExcelUtil.class);

    private static final String FILE = "file";

    private static final String METHOD = "method";

    @Autowired
    private ExcelUtilPro excelUtilPro;

    @Inject
    private IDmFopRecordDao iDmFopRecordDao;

    /**
     * 存储输入流，以便后面使用
     *
     * @param inputStream
     * @return
     */
    public ByteArrayOutputStream putInputStreamCacher(InputStream inputStream) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        try {
            while ((len = inputStream.read(buffer)) > -1) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            byteArrayOutputStream.flush();
        } catch (IOException e) {
            logger.error(CommonConstant.STREAM_WRITE_ERROR_TIPS);
        }
        return byteArrayOutputStream;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void insertErrorRecord(UploadInfoVO uploadInfoVO, String errorMsg) {
        // 统计导入错误信息
        uploadInfoVO.setFileName("对象维表-导入异常反馈-" + TimeUtils.getNowTime());
        DmFopRecordVO dmFoiImpExpRecordVO = new DmFopRecordVO();
        dmFoiImpExpRecordVO.setExceptionFeedback(errorMsg);
        dmFoiImpExpRecordVO.setFileSourceKey(uploadInfoVO.getFileKey());
        dmFoiImpExpRecordVO.setFileErrorKey(uploadInfoVO.getFileKey());
        insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, uploadInfoVO.getRowNumber());
    }

    private static void setImportExcelParams(String fileName, DmFopRecordVO dmFoiImpExpRecordVO, long size, Integer recordNum) {
        dmFoiImpExpRecordVO.setFileName(fileName);
        dmFoiImpExpRecordVO.setFileSize(String.valueOf(size));
        dmFoiImpExpRecordVO.setRecordNum(recordNum);
        dmFoiImpExpRecordVO.setPageModule(ModuleEnum.MODULE_AUDIT.getDesc());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void insertImportExcel(DmFopRecordVO dmFoiImpExpRecordVO, UploadInfoVO uploadInfoVO, boolean flag, Integer recordNum) {
        String fileName = uploadInfoVO.getFileName();
        long fileSize = uploadInfoVO.getFileSize();
        setImportExcelParams(fileName, dmFoiImpExpRecordVO, fileSize, recordNum);
        String userId = String.valueOf(uploadInfoVO.getUserId());
        if (flag) {
            dmFoiImpExpRecordVO.setRecSts("OK");
        } else {
            dmFoiImpExpRecordVO.setRecSts("FAIL");
        }
        statisticsImportRecord(dmFoiImpExpRecordVO, userId);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void statisticsImportRecord(DmFopRecordVO dmFoiImpExpRecordVO, String userId) {
        dmFoiImpExpRecordVO.setCreatedBy(userId);
        dmFoiImpExpRecordVO.setLastUpdatedBy(userId);
        dmFoiImpExpRecordVO.setOptType("IMP");
        dmFoiImpExpRecordVO.setStatus("Save");
        dmFoiImpExpRecordVO.setCreationDate(TimeUtils.getCurTime());
        dmFoiImpExpRecordVO.setLastUpdateDate(TimeUtils.getCurTime());
        dmFoiImpExpRecordVO.setEndDate(TimeUtils.getCurTime());
        dmFoiImpExpRecordVO.setId(Long.valueOf(DateFormatUtils.format(new Date(), "yyyyMMddHHmmss")));
        dmFoiImpExpRecordVO.setDelFlag("N");
        iDmFopRecordDao.create(dmFoiImpExpRecordVO);
    }

    /**
     * expSelectColumnExcel导出可选择列的excel
     *
     * @param params         params
     * @param selectedTitles 配置的列头结构
     * @param excelTitleVOS  excelTitleVOS
     * @param list           数据
     * @param response       response
     * @throws CommonApplicationException 异常处理
     */
    public void expSelectColumnExcel(Map<String, Object> params, Set<String> selectedTitles,
                                     List<AbstractExcelTitleVO> excelTitleVOS, List<?> list, HttpServletResponse response)
            throws CommonApplicationException {
        params.put("objs", list);
        params.put("selTitles", selectedTitles);
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = new ArrayList<>();
        int titleRowCount = excelUtilPro.adjustTitleVoList(excelTitleVOS, selectedTitles, selectedLeafExcelTitleVO);
        params.put("titleRowCount", titleRowCount);
        params.put("excelTitleVOS", excelTitleVOS);
        params.put("sheetName", "sheet1");
        params.put("selectedLeafExcelTitleVO", selectedLeafExcelTitleVO);
        this.expSelTitle(response, params);
    }

    /**
     * expSelectColumnExcel导出可选择列的excel
     *
     * @param outputStream   outputStream
     * @param params         params
     * @param userId         userId
     * @param selectedTitles selectedTitles
     * @param excelTitleVOS  excelTitleVOS
     * @param list           list
     * @throws CommonApplicationException
     */
    public void expSelectColumnExcel(OutputStream outputStream, Map<String, Object> params,
                                     String userId, Set<String> selectedTitles, List<AbstractExcelTitleVO> excelTitleVOS, List<?> list)
            throws CommonApplicationException {
        params.put("objs", list);
        params.put("selTitles", selectedTitles);
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = new ArrayList<>();
        int titleRowCount = excelUtilPro.adjustTitleVoList(excelTitleVOS, selectedTitles, selectedLeafExcelTitleVO);
        params.put("titleRowCount", titleRowCount);
        params.put("excelTitleVOS", excelTitleVOS);
        params.put("selectedLeafExcelTitleVO", selectedLeafExcelTitleVO);
        params.put("sheetName", "sheet1");
        params.put("userId", userId);
        PoiEnum.exportExcel(outputStream, params);
    }

    /**
     * 为前台页面展示需要为每个AbstractExcelTitleVO ,添加 id, 并且为子类添加 parentId
     * 用于将结构返回给前台，用于选择列
     *
     * @param titleVoList List<AbstractExcelTitleVO>
     * @return list List<AbstractExcelTitleVO>
     */
    public List<AbstractExcelTitleVO> adjStrucForPage(List<AbstractExcelTitleVO> titleVoList) {
        if (!CollectionUtil.isNullOrEmpty(titleVoList)) {
            addIdAndParentId(titleVoList, 1, null);
        }
        return titleVoList;
    }

    private Integer addIdAndParentId(List<AbstractExcelTitleVO> titleVoList, Integer id, Integer parentId) {
        Integer localId = id;
        for (AbstractExcelTitleVO excelTitleVO : titleVoList) {
            if (excelTitleVO instanceof BranchExcelTitleVO) {
                // 安全整改
                Integer currentId = localId;
                localId += 1;
                excelTitleVO.setId(currentId);
                excelTitleVO.setParentId(parentId);
                localId = addIdAndParentId(((BranchExcelTitleVO) excelTitleVO).getChildExcelTitleList(), localId,
                        currentId);
            }
            if (excelTitleVO instanceof LeafExcelTitleVO) {
                // 安全整改
                excelTitleVO.setId(localId);
                localId += 1;
                excelTitleVO.setParentId(parentId);
            }
        }
        return localId;
    }

    public void readExcel(Attachment attachment, List<ExcelVO> heads, UploadInfoVO uploadInfoVO) {
        ByteArrayOutputStream byteArrayOutputStream = null;
        InputStream inputStream = null;
        XSSFWorkbook workbook = null;
        try {
            ZipSecureFile.setMinInflateRatio(-1.0d);
            byteArrayOutputStream = putInputStreamCacher(attachment.getDataHandler().getInputStream());
            inputStream = getInputStream(byteArrayOutputStream);
            if (!((Constants.XLSX.getValue().equalsIgnoreCase(uploadInfoVO.getSuffix()) || Constants.XLS.getValue().equalsIgnoreCase(uploadInfoVO.getSuffix())) && inputStream.available() <= 6144000)) {
                throw new CommonApplicationException(CommonConstant.LABEL_CONFIG_EXCEPTION2);
            }
            DmFopRecordVO dmFoiImpExpRecordVO = new DmFopRecordVO();
            FileProcessUtis.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1, getInputStream(byteArrayOutputStream));
            uploadInfoVO.setFileKey(dmFoiImpExpRecordVO.getFileSourceKey());
            List<LabelInfoRequest> dataList = new ArrayList<>();
            AtomicInteger counterRef = new AtomicInteger();
            workbook = new XSSFWorkbook(inputStream);
            XSSFSheet sheet = workbook.getSheetAt(0);
            if (sheet.getLastRowNum() > 100001) {
                throw new CommonApplicationException("请导入Excel数据不要超过100000条,当前数量为:" + sheet.getLastRowNum() + ",超出了最大导入条数");
            }
            uploadInfoVO.setSheetName(sheet.getSheetName());
            uploadInfoVO.setRowNumber(-1);
            Map<String, Integer> headMap = new LinkedHashMap<>();
            pullData(heads, uploadInfoVO, dataList, counterRef, sheet, headMap);
            verifyDataResult(uploadInfoVO, dataList, counterRef);
            if (uploadInfoVO.getRowNumber() != 0) {
                uploadInfoVO.setRowNumber(dataList.size());
            }
            uploadInfoVO.setDataList(dataList);
            uploadInfoVO.setCount(counterRef.get());
        } catch (Exception ex) {
            logger.error("importExcel occurs error：{}", ex);
            uploadInfoVO.setErrorTips(ex.getMessage());
        } finally {
            StreamUtil.closeStreams(inputStream, byteArrayOutputStream, workbook);
        }
    }

    private void pullData(List<ExcelVO> heads, UploadInfoVO uploadInfoVO, List<LabelInfoRequest> dataList, AtomicInteger counterRef, XSSFSheet sheet, Map<String, Integer> headMap) throws CommonApplicationException {
        XSSFRow headerRow = sheet.getRow(0);
        if (null == headerRow) {
            return;
        }
        headerRow.forEach(cell -> {
            RelationConstants.AUDIT_CHILD_IMP_HEADER.entrySet().stream().forEach(entry -> {
                if (StringUtils.equals(entry.getKey(), cell.getStringCellValue())) {
                    headMap.put(entry.getKey(), cell.getColumnIndex());
                }
            });
        });
        StringBuilder builder = null;
        for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
            builder = new StringBuilder();
            XSSFRow row = sheet.getRow(rowNum);
            if (row == null) {
                uploadInfoVO.setRowNumber(rowNum - 1);
                break;
            }
            LabelInfoRequest labelInfo = new LabelInfoRequest();
            for (int cellNum = 0; cellNum < heads.size(); cellNum++) {
                String title = heads.get(cellNum).getHeadName();
                XSSFCell cell = row.getCell(headMap.get(title));
                if (StrUtil.equals(Constants.SNULL.getValue(), title)) {
                    continue;
                }
                setRequestData(labelInfo, title, excelUtilPro.getStringCellValue(cell, heads.get(cellNum).getHeadType()), builder);
            }
            if (!StringUtil.isNullOrEmpty(builder.toString())) {
                counterRef.getAndIncrement();
                labelInfo.setErrorMsg(builder.toString());
            }
            dataList.add(labelInfo);
        }
    }

    public void setRequestData(LabelInfoRequest labelInfo, String title, String val, StringBuilder builder) {
        switch (title) {
            case "是否有效":
                labelInfo.setIsVaild(val);
                break;
            case "Spart编码":
                labelInfo.setItemCode(val);
                if (StringUtils.isBlank(labelInfo.getItemCode())) {
                    builder.append("Spart编码为空;");
                    labelInfo.setItemCode("");
                } else if (Objects.nonNull(labelInfo.getItemCode()) && labelInfo.getItemCode().length() < 8) {
                    builder.append("Spart编码不足8位;");
                }
                break;
            case "Spart描述":
                labelInfo.setItemDesc(val);
                break;
            case "产业":
                labelInfo.setLv1Name(val);
                if (Objects.isNull(labelInfo.getLv1Name())) {
                    builder.append("产业不能为空;");
                }
                break;
            case "L1名称":
                labelInfo.setL1Name(val);
                if (Objects.isNull(labelInfo.getL1Name())) {
                    builder.append("L1名称不能为空;");
                }
                break;
            case "L2名称":
                labelInfo.setL2Name(val);
                if (Objects.isNull(labelInfo.getL2Name())) {
                    builder.append("L2名称不能为空;");
                }
                break;
            case "L3名称":
                if (StringUtils.equals("无", val) || StringUtils.isBlank(val)) {
                    val = null;
                }
                labelInfo.setL3Name(val);
                break;
            case "L2系数":
                if (Objects.nonNull(val) && CalcUtils.validateDataFormat(val)) {
                    labelInfo.setL2Coefficient(new BigDecimal(val));
                } else {
                    builder.append("L2系数错误:正数或小数位最多保留六位小数;");
                }
                break;
            default:
                CalcUtils.validateDataFormat(labelInfo, builder, val, title);
                break;
        }
    }


    /**
     * 校验数据
     *
     * @param verifyDataList 数据列表
     * @return List
     */
    public void verifyDataResult(UploadInfoVO uploadInfoVO, List<LabelInfoRequest> verifyDataList, AtomicInteger counterRef) {
        if (CollectionUtils.isEmpty(verifyDataList)) {
            return;
        }
        Map<String, List<LabelInfoRequest>> resultMap = verifyDataList.stream().collect(Collectors.groupingBy(result ->
                (result.getItemCode() + result.getL1Name())));
        verifyDataList.stream().forEach(verify -> {
            String key = verify.getItemCode() + verify.getL1Name();
            if (resultMap.containsKey(key) && resultMap.get(key).size() > 1) {
                setErrMsg(verify, counterRef, ";Spart编码和L1名称存在重复记录，请检查!");
            }
            if (!uploadInfoVO.getCheckList().contains(CalcUtils.reBuildKey(verify.getLv1Name(), verify.getL1Name(), verify.getL2Name()))) {
                setErrMsg(verify, counterRef, ";产业、L1名称、L2名称层级关系不存在;");
            }
        });
    }

    public void setErrMsg(LabelInfoRequest verify, AtomicInteger counterRef, String tipMsg) {
        if (StringUtils.isEmpty(verify.getErrorMsg())) {
            verify.setErrorMsg(tipMsg);
        } else {
            verify.setErrorMsg(verify.getErrorMsg() + tipMsg);
        }
        counterRef.addAndGet(1);
    }

    /**
     * [服务名称]expByS3
     *
     * @param response 入参
     * @param params   入参
     * @throws CommonApplicationException void
     * <AUTHOR>
     */
    public void expSelTitle(HttpServletResponse response, Map<String, Object> params)
            throws CommonApplicationException {
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            response.reset();
            response.setHeader("Content-disposition",
                    "attachment; filename=" + new String(("template" + ".xlsx").getBytes("gbk"), "ISO8859-1"));
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            PoiEnum.exportExcel(os, params);
        } catch (IOException e) {
            logger.error("导出excel文件：{}", CommonConstant.STREAM_WRITE_ERROR_TIPS);
            throw new CommonApplicationException("excel导出失败");
        } finally {
            if (null != os) {
                try {
                    os.close();
                } catch (IOException e) {
                    logger.error("close Wb-Exception.{}", CommonConstant.STREAM_OUT_CLOSE_FAILLED);
                }
            }
        }
    }

    public InputStream getInputStream(ByteArrayOutputStream byteArrayOutputStream) {
        return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
    }


    public void downloadAndUploadS3Excel(HttpServletResponse response, Workbook workbook, Map<String, Object> paramsMap) {
        FileOutputStream outputStream = null;
        File targetFile = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            String fileName = String.valueOf(paramsMap.get("fileName"));
            logger.info("export data start" + fileName);
            targetFile = File.createTempFile(fileName, ".xlsx");
            outputStream = new FileOutputStream(targetFile);
            workbook.write(byteArrayOutputStream);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("content-disposition", "attachment;filename=" + new String(fileName.getBytes("ISO8859-1"), "UTF-8"));
            response.setHeader("filename", fileName);
            byteArrayOutputStream.writeTo(response.getOutputStream());
            byteArrayOutputStream.writeTo(outputStream);
            paramsMap.put("fileKey",
                    FileProcessUtis.uploadToS3(targetFile, fileName + ".xlsx", String.valueOf(paramsMap.get("userId"))));
            paramsMap.put("fileSize", targetFile.length() / 1024);
        } catch (IOException ex) {
            logger.error("流关闭异常");
        } finally {
            try {
                response.flushBuffer();
                StreamUtil.closeStreams(outputStream, byteArrayOutputStream);
            } catch (IOException ex) {
                logger.error("流关闭异常");
            }
            try {
                if (null != targetFile) {
                    if (!targetFile.delete()) {
                        logger.error("删除临时文件失败");
                    }
                }
                workbook.close();
            } catch (Exception ex) {
                logger.error("删除临时文件失败: {}", ex);
            }
        }
    }

    public void verifyExcelFile(Attachment attachment, String pageModule, File tempFlie) throws CommonApplicationException {
        if (verifyFileFormat(attachment)) {
            checkFile(attachment, pageModule, "请导入xlsx、xlsm格式的Excel文件!", tempFlie);
            logger.error("Excel文件格式错误");
            throw new CommonApplicationException("请导入xlsx、xlsm格式的Excel文件!");
        }
        if (verifyFileSize(attachment)) {
            String errorMsg = String.format(Locale.ROOT, "文件允许的大小是：%dM!", "6");
            checkFile(attachment, pageModule, errorMsg, tempFlie);
            throw new CommonApplicationException(errorMsg);
        }
    }

    public static boolean verifyFileFormat(Attachment attachment) {
        String fileName = "";
        fileName = new String(attachment.getDataHandler().getName().getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        return !(fileName).endsWith(Constants.XLSX.getValue()) && !(fileName).endsWith(Constants.XLSM.getValue());
    }

    public static void closeStreamAndWorkbook(InputStream inputStream, Workbook wb) {
        if (wb != null) {
            try {
                logger.info("close used workBook!");
                wb.close();
            } catch (IOException e) {
                logger.error("close Wb-Exception.{}", CommonConstant.STREAM_WORKBOOK_CLOSE_FAILLED);
            }
        }
        if (null != inputStream) {
            try {
                logger.info("close used inputStream!");
                inputStream.close();
            } catch (IOException ex) {
                logger.error("close Wb-Exception.{}", CommonConstant.STREAM_WRITE_ERROR_TIPS);
            }
        }
    }
    public void checkFile(Attachment attachment, String pageModule, String errorMsg, File tempFile) {
        InputStream inputStream = null;
        FileOutputStream out = null;
        try {
            // 个人中心导入所需文件
            String tempName = new String(attachment.getDataHandler().getName().getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
            File file = new File(attachment.getDataHandler().getName());
            String fileName = tempName.substring(0, tempName.indexOf(".")) + "_" + getTempName();
            out = new FileOutputStream(file);
            // 刷新流
            out.flush();
            logger.info("stream flush ok");
            // 上传文件至S3服务器
            Long userId = UserHandle.getUserId();
            // 设置个人中心上传信息
            DmFopRecordVO recordVO = new DmFopRecordVO();
            inputStream = Files.newInputStream(tempFile.toPath());
            recordVO.setExceptionFeedback(errorMsg);
            recordVO.setRecordNum(0);
            recordVO.setPageModule(pageModule);
            UploadInfoVO uploadInfoVO = new UploadInfoVO();
            uploadInfoVO.setFileName(fileName);
            uploadInfoVO.setUserId(userId);
            uploadInfoVO.setFileSize(inputStream.available() / 1024);
            FileProcessUtis.getImportUploadFileKey(recordVO, userId, 1, inputStream);
            insertImportExcel(recordVO, uploadInfoVO, false, null);
        } catch (Exception e) {
            logger.error("verifyExcelFile 上传文件失败");
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error("verifyExcelFile Stream 关闭失败");
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    logger.error("verifyExcelFile inputStream 关闭失败");
                }
            }
        }
    }

    /**
     * 校验导入的Excel文件大小，目前只支持 6144000 byts
     *
     * @param attachment attachment
     * @return true or false
     */
    private static boolean verifyFileSize(Attachment attachment) {
        try {
            return attachment.getDataHandler().getInputStream().available() >= 6144000;
        } catch (IOException ex) {
            logger.error(">>>Find verifyFileSize a error:", ex);
            return false;
        }
    }

    private static String getTempName() {
        return DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS");
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void insertImportExcelRecord(DmFopRecordVO dmFopExpRecordVO, UploadInfoVO uploadInfoVO, boolean flag,
        Integer recordNum) {
        dmFopExpRecordVO.setFileName(uploadInfoVO.getFileName());
        dmFopExpRecordVO.setFileSize(String.valueOf(uploadInfoVO.getFileSize()));
        dmFopExpRecordVO.setRecordNum(recordNum);
        String userId = String.valueOf(uploadInfoVO.getUserId());
        if (flag) {
            dmFopExpRecordVO.setRecSts("OK");
        } else {
            dmFopExpRecordVO.setRecSts("FAIL");
        }
        statisticsImportRecord(dmFopExpRecordVO, userId);
    }

    /**
     * 根据Excel导出模板创建工作簿
     *
     * @param templatePath 导出模板路径
     * @return Workbook 工作簿
     * @throws Exception
     */
    public XSSFWorkbook getWorkbookByTemplate(String templatePath) throws Exception {
        // 1、加载导出模板
        try (InputStream inputStream = new ClassPathResource(templatePath).getInputStream()) {
            return new XSSFWorkbook(inputStream);
        } catch (IOException ex) {
            throw new ExcelExportException("not found template.", ex);
        }
    }
    public DmFopRecordVO uploadExportExcel(Workbook workbook, int number, String fileName) throws IOException, CommonApplicationException {
        String name = fileName.concat(String.valueOf(System.currentTimeMillis())) + ".xlsx";
        File file = new File(name);
        if (!file.createNewFile()) {
            logger.error(">>> create new file failed. ");
        }
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(file);
            workbook.write(out);
            out.flush();
            // 上传
            String userId = String.valueOf(UserHandle.getUserId());
            String fileSourceKey = FileProcessUtis.uploadToS3(file,name, userId);

            DmFopRecordVO dmFoiImpExpRecordVO = new DmFopRecordVO();
            dmFoiImpExpRecordVO.setFileSourceKey(fileSourceKey);
            long fileSize = file.length() / 1024;
            dmFoiImpExpRecordVO.setFileSize(String.valueOf(fileSize));
            dmFoiImpExpRecordVO.setRecordNum(number);
            dmFoiImpExpRecordVO.setFileName(fileName);
            return dmFoiImpExpRecordVO;
        } catch (Exception e) {
            logger.error("file create fail");
        } finally {
            if (out != null) {
                out.close();
            }
            if (file != null) {
                if (!file.delete()) {
                    logger.error(">>> file delete failed.");
                }
            }
            if (workbook != null) {
                workbook.close();
            }
        }
        return null;
    }

    public void statisticsExportRecord(DmFopRecordVO dmFoiImpExpRecordVO) {
        String userId = String.valueOf(UserHandle.getUserId());
        dmFoiImpExpRecordVO.setCreatedBy(userId);
        dmFoiImpExpRecordVO.setCreationDate(TimeUtils.getCurTime());
        dmFoiImpExpRecordVO.setLastUpdatedBy(userId);
        dmFoiImpExpRecordVO.setLastUpdateDate(TimeUtils.getCurTime());
        dmFoiImpExpRecordVO.setEndDate(TimeUtils.getCurTime());
        dmFoiImpExpRecordVO.setId(Long.valueOf(TimeUtils.getBathNo()));
        dmFoiImpExpRecordVO.setDelFlag("N");
        dmFoiImpExpRecordVO.setOptType("EXP");
        dmFoiImpExpRecordVO.setStatus("Save");
        iDmFopRecordDao.create(dmFoiImpExpRecordVO);
    }
}
