/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.vo;

import com.alibaba.fastjson.JSONObject;
import com.huawei.it.jalor5.security.ProgramVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * DimensionVO Class
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DimensionVO implements Serializable {

    private static final long serialVersionUID = -1233210256025647471L;

    private int roleId;

    private String roleName;

    private List<ProgramVO> programVOList;

    private JSONObject dataPermission;
}