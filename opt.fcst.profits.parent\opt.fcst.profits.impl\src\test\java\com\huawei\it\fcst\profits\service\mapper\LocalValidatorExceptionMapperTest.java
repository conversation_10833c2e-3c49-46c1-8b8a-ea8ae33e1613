package com.huawei.it.fcst.profits.service.mapper;

import org.hibernate.validator.internal.engine.ConstraintViolationImpl;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.validation.ConstraintViolationException;
import javax.ws.rs.core.Response;
import java.util.Collections;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class LocalValidatorExceptionMapperTest {
    private LocalValidatorExceptionMapper localValidatorExceptionMapperUnderTest;
    @Before
    public void before() throws Exception {
        localValidatorExceptionMapperUnderTest = new LocalValidatorExceptionMapper();
    }
    @Test
    void testToResponse() {
        // Setup
        final ConstraintViolationException ex = new ConstraintViolationException("message", Collections.singleton(ConstraintViolationImpl.forBeanValidation(
                "",
                null,
                null,
                "error",
                null,
                null,
                null,
                "",
                PathImpl.createRootPath(),
                null,
                null
        )));
        // Run the test
        final Response result = localValidatorExceptionMapperUnderTest.toResponse(ex);
        // Verify the results
        Map<String,Object> entity = (Map<String, Object>) result.getEntity();
        Assert.assertNotNull(entity);
    }
}
