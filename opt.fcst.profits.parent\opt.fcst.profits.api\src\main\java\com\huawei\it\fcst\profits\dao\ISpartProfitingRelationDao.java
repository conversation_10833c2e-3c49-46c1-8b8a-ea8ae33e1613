/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.dao;

import com.huawei.it.fcst.profits.vo.LabelCountVO;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.request.LabelConfigRequest;
import com.huawei.it.fcst.profits.vo.request.LabelInfoRequest;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * The DAO to access ISpartProfitingRelationDao
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-15 09:18:41
 */
public interface ISpartProfitingRelationDao {
    /**
     * findByPage
     *
     * @param requestVO   request
     * @param paramPageVO paramPageVO
     * @return PagedResult<SpartProfitingRelationVO> result
     */
    PagedResult<SpartProfitingRelationVO> findByPage(LabelConfigRequest requestVO, PageVO paramPageVO);

    /**
     * findUserIdsByPage
     *
     * @param requestVO   request
     * @return List<LabelConfigRequest> result
     */
    List<LabelConfigRequest> findUserIdsByParam(LabelConfigRequest requestVO);


    /**
     * findConfigByPage
     *
     * @param vo          vo
     * @param paramPageVO paramPageVO
     * @return PagedResult<SpartProfitingRelationVO>
     */
    PagedResult<SpartProfitingRelationVO> findConfigByPage(LabelConfigRequest vo, PageVO paramPageVO);

    /**
     * findYearData
     *
     * @param list year
     * @return List<SpartProfitingRelationVO>
     */
    List<LabelCountVO> findLv1Data(@Param("list") List<String> list, @Param("lv1List") List<String> lv1s);

    List<LabelCountVO> getYearStatics(@Param("list") List<String> list, @Param("lv1List") List<String> lv1s);

    /**
     * Create batch DmDimFopSpartProfitingRelationVO record.
     *
     * @param items items
     * @return int
     */
    int createList(List<SpartProfitingRelationVO> items);

    /**
     * Update batch   DmDimFopSpartProfitingRelationVO record.
     *
     * @param list items
     * @return int
     */
    int updateList(List<SpartProfitingRelationVO> list) throws CommonApplicationException;

    /**
     * Update batch   DmDimFopSpartProfitingRelationVO record.
     *
     * @param list items
     * @return int
     */
    int updateOldList(List<SpartProfitingRelationVO> list) throws CommonApplicationException;

    int deleteInvalidList(List<LabelInfoRequest> list) throws CommonApplicationException;

    /**
     * Update batch   DmDimFopSpartProfitingRelationVO record.
     *
     * @param list items
     * @return int
     */
    int updateMoveList(@Param("lastUpdatedBy") Long userId, @Param("lastUpdateDate") Timestamp lastUpdateDate,
                       @Param("updateFlag") String updateFlag, @Param("list") List<SpartProfitingRelationVO> list) throws CommonApplicationException;

    int uptMoveList(List<SpartProfitingRelationVO> list) throws CommonApplicationException;

    /**
     * Update batch   DmDimFopSpartProfitingRelationVO record.
     *
     * @param items items
     * @return int
     */
    int uptTempList(List<SpartProfitingRelationVO> items);

    List<String> getSpartConfInfo();

    /**
     * getLv1List
     *
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> getLv1List(LabelConfigRequest labelConfigRequest);

    /**
     * getAuditLv1Names
     *
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> getAuditLv1Names(LabelConfigRequest labelConfigRequest);

    /**
     * getItemCodeL1Info
     *
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> getItemCodeL1Info(LabelConfigRequest request);

    /**
     * getL1NameList
     *
     * @param request request
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> getL1NameList(LabelConfigRequest request);

    /**
     * getAuditL1Names
     *
     * @param request request
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> getAuditL1Names(LabelConfigRequest request);

    /**
     * getL2NameList
     *
     * @param request request
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> getL2NameList(LabelConfigRequest request);

    /**
     * getAuditL2Names
     *
     * @param request request
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> getAuditL2Names(LabelConfigRequest request);

    /**
     * getL3NameList
     *
     * @param request request
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> getL3NameList(LabelConfigRequest request);

    /**
     * getAuditL3Names
     *
     * @param request request
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> getAuditL3Names(LabelConfigRequest request);

    /**
     * getVersions
     *
     * @return List<String> result
     */
    List<String> getVersions();

    /**
     * findDataByList
     *
     * @param list list
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> findDataByList(List<LabelInfoRequest> list);

    /**
     * findDataByParam
     *
     * @param labelInfoRequest request
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> findDataByParam(LabelInfoRequest labelInfoRequest) throws CommonApplicationException;

    /**
     * getLv1Code record.
     *
     * @return int
     */
    List<LabelInfoRequest> getLv1Code();

    int countRecord(LabelInfoRequest labelInfoRequest);

    /**
     * findDataByParam
     * @param lastUpdatedBy 最后更新人
     * @return 条数
     */
    Integer getWaitCount(@Param("lastUpdatedBy") Long lastUpdatedBy);

    /**
     * 提交数据到临时表
     *
     * @param lastUpdatedBy 操作人ID
     */
    void copyList(@Param("lastUpdatedBy") Long lastUpdatedBy);


    /**
     * updateCopyStatus
     *
     * @return void
     */
    void updateCopyStatus(List<SpartProfitingRelationVO> list);

    /**
     * Update batch   DmDimFopSpartProfitingRelationVO record.
     *
     * @param list items
     * @return int
     */
    int uptCoverSubmitList(List<SpartProfitingRelationVO> list);


    /**
     * 删除临时表数据
     *
     * @param handleType 类型
     * @param items code
     * @param lastUpdatedBy 操作人ID
     * @return int
     */
    int uptCopyList(@Param("handleType") String handleType, @Param("items") List<SpartProfitingRelationVO> items,@Param("lastUpdatedBy") Long lastUpdatedBy);

    /**
     * uptCoverList
     * @param items code
     *
     * @return int
     */
    int uptCoverList(@Param("items") List<SpartProfitingRelationVO> items);

    /**
     * 异常删除copy数据
     * @param lastUpdatedBy
     * @return
     */
    int delCopyListByUser(@Param("lastUpdatedBy") Long lastUpdatedBy);

    /**
     * findCopyDataByParam
     *
     * @param
     * @return List<SpartProfitingRelationVO>
     */
    List<SpartProfitingRelationVO> findCopyDataByParam(@Param("handleType") String handleType,@Param("lastUpdatedBy") Long lastUpdatedBy);

    /**
     * findCopyDataByParam
     *
     * @param
     * @return List<SpartProfitingRelationVO>
     */
    int findCopyDataCount(@Param("handleType") String handleType,@Param("lastUpdatedBy") Long lastUpdatedBy);

    /**
     * getUpdateDataTimeInfo
     *
     * @param forecastsRequest req
     * @return SpartProfitingRelationVO
     */
    SpartProfitingRelationVO getUpdateDataTimeInfo(ForecastsRequest forecastsRequest);

    /**
     * getCurrentDataTimeInfo
     *
     * @param forecastsRequest req
     * @return SpartProfitingRelationVO
     */
    SpartProfitingRelationVO getCurrentDataTimeInfo(ForecastsRequest forecastsRequest);

    /**
     * 查询用户UserId
     *
     * @param request 请求参数
     * @return set
     */
    Set<Long> getLastUpdatedBys(LabelConfigRequest request);

    /**
     * 查询数据状态
     *
     * @param request 请求参数
     * @return 数据状态集合
     */
    List<SpartProfitingRelationVO> getStatusList(LabelConfigRequest request);

    /**
     * 根据item_code 查询编码
     *
     * @param list itemCode
     * @return list
     */
    List<String> findItemCode(@Param("items") List<String> list);
}
