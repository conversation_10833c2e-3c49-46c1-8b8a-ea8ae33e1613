package com.huawei.it.fcst.profits.service.config.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;

import com.huawei.it.fcst.profits.common.user.JalorUserTools;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.dao.ILabelConfigDao;
import com.huawei.it.fcst.profits.service.config.impl.HolisticViewConfigFullDataImporter;
import com.huawei.it.fcst.profits.vo.CoaConfigDataVO;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.junit.Test;
import org.junit.Before; 
import org.junit.After;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;

import java.util.Arrays;
import java.util.HashSet;

/** 
* HolisticViewConfigFullDataImporter Tester. 
* 
* <AUTHOR> name> 
* @since <pre>6月 14, 2023</pre> 
* @version 1.0 
*/ 
public class HolisticViewConfigFullDataImporterTest {
    @InjectMocks
    HolisticViewConfigFullDataImporter holisticViewConfigFullDataImporter;
    @Mock
    private JalorUserTools jalorUserTools;

    @Mock
    private ILabelConfigDao iLabelConfigDao;
    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: importData(Attachment attachment, String roleId)
     */

    @Test
    public void testImportData() throws CommonApplicationException {
        // Setup
        final Attachment attachment = new Attachment("id", "mediaType", "object");
        DataPermissionsVO vo = new DataPermissionsVO();
        vo.setLv1DataType("Test");
        PowerMockito.doReturn(vo).when(jalorUserTools).getRolePermission(anyInt());
        // Run the test
        holisticViewConfigFullDataImporter.importData(attachment, "1250");

        Assertions.assertNotNull(attachment);
    }

    @Test
    public void testImportDataA() throws CommonApplicationException {
        // Setup
        final Attachment attachment = new Attachment("id", "mediaType", "object");
        DataPermissionsVO vo = new DataPermissionsVO();
        vo.setLv1DataType("Test");
        PowerMockito.doReturn(vo).when(jalorUserTools).getRolePermission(anyInt());
        // Run the test
        holisticViewConfigFullDataImporter.importData(attachment, "1250");

        Assertions.assertNotNull(attachment);
    }

    /**
     * Method: doCoaData(int batchNum, Set<String> lv1NameSet, List<Object> voList)
     */
    @Test
    public void testDoCoaData() {
        // Setup
        PowerMockito.doNothing().when(iLabelConfigDao).deleteImportIctData();
        PowerMockito.doReturn(1).when(iLabelConfigDao).createIctDataList(any());
        HolisticViewConfigDataVO vo= new HolisticViewConfigDataVO();
        // Run the test
        holisticViewConfigFullDataImporter.doIctData(0, new HashSet<>(Arrays.asList("value")), Arrays.asList(vo));

        Assertions.assertNotNull(vo);
        // Verify the results
    }


} 
