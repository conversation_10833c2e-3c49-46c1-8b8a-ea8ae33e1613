/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.dao;

import com.huawei.it.fcst.profits.vo.CoaConfigDataVO;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.fcst.profits.vo.LabelConfigSumitVO;
import com.huawei.it.fcst.profits.vo.LabelConfigVo;
import com.huawei.it.fcst.profits.vo.ObjectConfigDataVO;
import com.huawei.it.fcst.profits.vo.PlanComConfigDataVO;
import com.huawei.it.fcst.profits.vo.request.LabelConfigQueryRequest;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * The DAO to access KrCpfL1ActVO entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:25:12
 */
public interface ILabelConfigDao {

    /**
     * findConfigByPage
     *
     * @param vo vo
     * @param paramPageVO paramPageVO
     * @return PagedResult<SpartProfitingRelationVO>
     */
    PagedResult<CoaConfigDataVO> findCoaDataByPage(LabelConfigQueryRequest vo, PageVO paramPageVO);

    int createCoaDataList(List<?> dataList);

    PagedResult<PlanComConfigDataVO> findPlanComDataByPage(LabelConfigQueryRequest vo, PageVO paramPageVO,
        @Param("lv3Flag") boolean lv3Flag, @Param("lv4Flag") boolean lv4Flag);

    int createPlanComDataList(List<?> dataList);


    PagedResult<HolisticViewConfigDataVO> findIctDataByPage(LabelConfigQueryRequest vo, PageVO paramPageVO,
        @Param("lv2Flag") boolean lv2Flag, @Param("lv3Flag") boolean lv3Flag, @Param("l1Flag") boolean l1Flag);

    int createIctDataList(List<?> dataList);

    List<CoaConfigDataVO> getCoaProductionNames(LabelConfigQueryRequest requestVO);

    List<CoaConfigDataVO> getCoaL1Names(LabelConfigQueryRequest requestVO);

    List<CoaConfigDataVO> getCoaL2Names(LabelConfigQueryRequest requestVO);

    List<CoaConfigDataVO> getCoaCodes(LabelConfigQueryRequest requestVO);

    List<PlanComConfigDataVO> getPlanComLv1Names(LabelConfigQueryRequest requestVO);

    List<PlanComConfigDataVO> getPlanComLv2Names(LabelConfigQueryRequest requestVO);

    List<PlanComConfigDataVO> getPlanComLv3Names(LabelConfigQueryRequest requestVO);

    List<PlanComConfigDataVO> getBusiLv4Names(LabelConfigQueryRequest requestVO,
        @Param("lv3Flag") boolean planComLv3QueryNull);

    List<PlanComConfigDataVO> getPlanComL1Names(LabelConfigQueryRequest requestVO,
        @Param("lv3Flag") boolean planComLv3QueryNull, @Param("lv4Flag") boolean lv4QueryNull);

    List<PlanComConfigDataVO> getPlanComL2Names(LabelConfigQueryRequest requestVO,
        @Param("lv3Flag") boolean planComLv3QueryNull, @Param("lv4Flag") boolean lv4QueryNull);

    List<HolisticViewConfigDataVO> getIctLv1Names(LabelConfigQueryRequest requestVO);

    List<HolisticViewConfigDataVO> getIctLv2Names(LabelConfigQueryRequest requestVO);

    List<HolisticViewConfigDataVO> getIctLv3Names(LabelConfigQueryRequest requestVO,
        @Param("lv2Flag") boolean lv2QueryNull);

    List<HolisticViewConfigDataVO> getIctL1Names(LabelConfigQueryRequest requestVO,
        @Param("lv2Flag") boolean lv2QueryNull, @Param("lv3Flag") boolean l3QueryNull);

    List<HolisticViewConfigDataVO> getIctArticulationFlagNames(LabelConfigQueryRequest requestVO,
        @Param("lv2Flag") boolean lv2QueryNull, @Param("lv3Flag") boolean l3QueryNull,
        @Param("l1Flag") boolean l1QueryNull);

    int createObjectDataList(List<?> list);

    PagedResult<ObjectConfigDataVO> findObjectDataByPage(LabelConfigQueryRequest request, PageVO pageVO,
        @Param("flag") boolean l3Flag);

    List<ObjectConfigDataVO> getObjectLv1Names(LabelConfigQueryRequest request);

    List<ObjectConfigDataVO> getObjectL1Names(LabelConfigQueryRequest request);

    List<ObjectConfigDataVO> getObjectL2Names(LabelConfigQueryRequest request);

    List<ObjectConfigDataVO> getObjectL3Names(LabelConfigQueryRequest request);

    List<ObjectConfigDataVO> getObjectUpdatedNames(LabelConfigQueryRequest requestVO,@Param("l3Flag") boolean l3QueryNull);

    List<CoaConfigDataVO> getCoaUpdatedNames(LabelConfigQueryRequest requestVO);

    List<PlanComConfigDataVO> getPlanComUpdatedNames(LabelConfigQueryRequest requestVO,
        @Param("lv3Flag") boolean planComLv3QueryNull, @Param("lv4Flag") boolean lv4QueryNull);

    List<HolisticViewConfigDataVO> getIctUpdatedNames(LabelConfigQueryRequest requestVO,
        @Param("lv2Flag") boolean lv2QueryNull, @Param("lv3Flag") boolean l3QueryNull,
        @Param("l1Flag") boolean l1QueryNull);

    void deleteSelectedCoaDataList(List<String> lv1Name);

    void deleteSelectedPlanComDataList(List<String> lv1Name);

    void deleteSelectedIctDataList(List<String> lv1Name);

    void deleteSelectedObjectDataList(List<String> lv1Name);

    int getObjectImportDataNum(LabelConfigQueryRequest requestVO);

    int getIctImportDataNum(LabelConfigQueryRequest requestVO);

    int getPlanComImportDataNum(LabelConfigQueryRequest requestVO);

    int getCoaImportDataNum(LabelConfigQueryRequest requestVO);

    List<LabelConfigSumitVO> getCoaImportDataDetail(LabelConfigQueryRequest requestVO);

    List<LabelConfigSumitVO> getIctImportDataDetail(LabelConfigQueryRequest requestVO);

    List<LabelConfigSumitVO> getObjectImportDataDetail(LabelConfigQueryRequest requestVO);

    List<LabelConfigSumitVO> getPlanComImportDataDetail(LabelConfigQueryRequest requestVO);

    List<CoaConfigDataVO> getPreparedCoaList(LabelConfigQueryRequest requestVO);

    List<HolisticViewConfigDataVO> getPreparedIctList(LabelConfigQueryRequest requestVO);

    List<ObjectConfigDataVO> getPreparedObjectList(LabelConfigQueryRequest requestVO);

    List<PlanComConfigDataVO> getPreparedPlanComList(LabelConfigQueryRequest requestVO);

    void deleteOldSubmitObjectDataList(List<String> lv1Name);

    void deleteOldSubmitIctDataList(List<String> lv1Name);

    int deleteOldSubmitCoaDataList(List<String> lv1Name);

    void submitCoaData(@Param("requestMap")Map<String, Long> requestVO, @Param("lastUpdatedTime") Timestamp time);

    void submitObjectData(@Param("requestMap")Map<String, Long> requestVO, @Param("lastUpdatedTime") Timestamp time);

    void submitPlanComData(@Param("requestMap")Map<String, Long> requestVO, @Param("lastUpdatedTime") Timestamp time);

    void submitIctData(@Param("requestMap")Map<String, Long> requestVO, @Param("lastUpdatedTime") Timestamp time);

    List<PlanComConfigDataVO> getPlanComProductionNames(LabelConfigQueryRequest requestVO);

    void deleteOldSubmitPlanComDataList(List<String> lv1Name);

    List<HolisticViewConfigDataVO> getHierarchySortInfoLv2();

    List<HolisticViewConfigDataVO> getHierarchySortInfoLv3();

    @MapKey("lv1Code")
    Map<String, HolisticViewConfigDataVO> getHierarchySortInfoLv1();

    List<String> getIctAllL1Item();

    void deleteImportCoaData();
    void deleteImportObjectData();
    void deleteImportIctData();
    void deleteImportPlanComData();
    void deleteObjectFullData();
    void deleteCoaFullData();
    void deleteIctFullData();
    void deletePlanComFullData();
    List<LabelConfigVo> checkObjectImportDataType(LabelConfigQueryRequest requestVO);
    List<LabelConfigVo> checkIctImportDataType(LabelConfigQueryRequest requestVO);
    List<LabelConfigVo> checkCoaImportDataType(LabelConfigQueryRequest requestVO);
    List<LabelConfigVo> checkPlanComImportDataType(LabelConfigQueryRequest requestVO);
    List<HolisticViewConfigDataVO> findArticulationConfig();
}
