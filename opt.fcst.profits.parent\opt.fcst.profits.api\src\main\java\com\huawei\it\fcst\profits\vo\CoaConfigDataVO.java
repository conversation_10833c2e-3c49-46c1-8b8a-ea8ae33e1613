/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * The Entity of CoaConfigDataVO
 * @since 2022-10-17 19:38:39
 */
@Getter
@Setter
@NoArgsConstructor
public class CoaConfigDataVO extends LabelConfigVo {
    private static final long serialVersionUID = 1L;

    /**
     * lv1代码
     **/
    private String lv1Code;

    /**
     * l1名称
     **/
    private String l1Name;

    /**
     * l2名称
     */
    private String l2Name;

    /**
     * COA编码
     */
    private String coaCode;

    @Override
    public boolean equals(Object other) {
        //	先判断自反性
        if(other == this) {
            return true;
        }

        //  判断同类型，同时也满足了第五条（null）
        if(!(other instanceof CoaConfigDataVO)) {
            return false;
        }

        //  最后再进行类型转换，比较值
        CoaConfigDataVO that = (CoaConfigDataVO) other;
        return that.getLv1Name().equals(this.getLv1Name()) && that.l1Name.equals(this.l1Name) && that.coaCode.equals(this.coaCode) ;
    }

    @Override
    public int hashCode() {
        return Objects.hash(lv1Code,l1Name,l2Name,coaCode);
    }
}

