/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.comm;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.DiffColumnTyp;
import com.huawei.it.fcst.profits.common.enums.ModuleEnum;
import com.huawei.it.fcst.profits.common.enums.PeriodTypeEnum;
import com.huawei.it.fcst.profits.common.enums.PredictionEnum;
import com.huawei.it.fcst.profits.common.enums.SaveTypeEnum;
import com.huawei.it.fcst.profits.common.poi.ExcelUtil;
import com.huawei.it.fcst.profits.common.user.JalorUserTools;
import com.huawei.it.fcst.profits.common.utils.DateUtil;
import com.huawei.it.fcst.profits.common.utils.FcstGlobalParameterUtil;
import com.huawei.it.fcst.profits.common.utils.FileProcessUtis;
import com.huawei.it.fcst.profits.common.utils.GenerateUtils;
import com.huawei.it.fcst.profits.common.utils.ObjectUtils;
import com.huawei.it.fcst.profits.common.utils.UserHandle;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.common.vo.UploadInfoVO;
import com.huawei.it.fcst.profits.dao.IDmFopRecordDao;
import com.huawei.it.fcst.profits.dao.IKrCpfLv1AggrDao;
import com.huawei.it.fcst.profits.dao.ISpartProfitingRelationDao;
import com.huawei.it.fcst.profits.vo.DmFopRecordVO;
import com.huawei.it.fcst.profits.vo.LabelCountVO;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.request.LabelConfigRequest;
import com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse;
import com.huawei.it.jalor5.core.cache.IJalorShortLifeCache;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import com.huawei.it.jalor5.security.service.IUserQueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ReflectionUtils;

import javax.inject.Inject;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.huawei.it.fcst.profits.common.constants.CommonConstant.AI_COMBINED_EXPERT_TYPE_BG;
import static com.huawei.it.fcst.profits.common.constants.CommonConstant.AI_COMBINED_EXPERT_TYPE_GROUP;

public abstract class AbstractService {

    private static final Logger logger = LoggerFactory.getLogger(AbstractService.class);

    @Inject
    protected IDmFopRecordDao iDmFopRecordDao;

    @Inject
    protected IKrCpfLv1AggrDao iKrCpfLv1AggrDao;

    @Autowired
    protected ExcelUtil excelUtil;

    @Autowired
    private JalorUserTools jalorUserTools;

    @Inject
    protected IUserQueryService iUserQueryService;

    @Inject
    protected ISpartProfitingRelationDao iSpartProfitingRelationDao;

    @Autowired(required = false)
    protected IJalorShortLifeCache shortLifeCache;

    // 线程池，用于调度线程任务
    protected final static ExecutorService EXECUTOR_SERVICE;

    static {
        ThreadPoolExecutor pool = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 10, Runtime.getRuntime().availableProcessors() * 30, 60,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(Runtime.getRuntime().availableProcessors() * 200),
                new ThreadPoolExecutor.CallerRunsPolicy());
        // 如果设置为true,当任务执行完后，所有的线程在指定的空闲时间后，poolSize会为0
        // 如果不设置，或者设置为false，那么，poolSize会保留为核心线程的数量
        pool.allowCoreThreadTimeOut(true);
        EXECUTOR_SERVICE = TtlExecutors.getTtlExecutorService(pool);
    }

    /**
     * 根据角色Id获取用户权限信息
     *
     * @param roleId
     * @return DataPermissionsVO
     */
    public DataPermissionsVO getUserRoleOpt(int roleId) {
        return jalorUserTools.getRolePermission(roleId);
    }

    /**
     * 新建记录
     *
     * @param errorMsg  错误信息
     * @param periodId  周期id
     * @param sourceKey 源key
     * @param errorKey  错误key
     * @param optTyp    操作类型
     */

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void creatRecord(UploadInfoVO uploadInfoVO, String recStatus, String errorMsg, String periodId,
                            String sourceKey, String errorKey, String optTyp) {
        DmFopRecordVO dmFopRecordVO = new DmFopRecordVO();
        dmFopRecordVO.setFileName(uploadInfoVO.getFileName());
        dmFopRecordVO.setFileSize(String.valueOf(uploadInfoVO.getFileSize()));
        dmFopRecordVO.setStatus(SaveTypeEnum.SAVE.getCode());
        dmFopRecordVO.setExceptionFeedback(errorMsg);
        dmFopRecordVO.setRecordNum(uploadInfoVO.getRowNumber());
        dmFopRecordVO.setPeriodId(periodId);
        dmFopRecordVO.setFileSourceKey(sourceKey);
        dmFopRecordVO.setFileErrorKey(errorKey);
        dmFopRecordVO.setOptType(optTyp);
        dmFopRecordVO.setPageModule(ModuleEnum.MODULE_AUDIT.getDesc());
        dmFopRecordVO.setCreationDate(uploadInfoVO.getStartTime());
        dmFopRecordVO.setDelFlag("N");
        dmFopRecordVO.setRecSts(recStatus);
        dmFopRecordVO.setEndDate(TimeUtils.getCurTime());
        dmFopRecordVO.setId(Long.valueOf(TimeUtils.getBathNo()));
        dmFopRecordVO.setCreatedBy(String.valueOf(UserHandle.getUserId()));
        iDmFopRecordDao.create(dmFopRecordVO);
    }

    /**
     * 新建记录
     *
     * @param fileName  文件名称
     * @param fileSize  文件大小
     * @param status    状态
     * @param errorMsg  错误信息
     * @param num       数字编号
     * @param periodId  周期id
     * @param sourceKey 源key
     * @param errorKey  错误key
     * @param optTyp    操作类型
     * @param recSts    信息记录
     */
    public void creatRecord(String fileName, Long fileSize, String status, String errorMsg, int num, String periodId,
                            String sourceKey, String errorKey, String optTyp, String recSts, String moudule) {
        DmFopRecordVO dmFopRecordVO = new DmFopRecordVO();
        dmFopRecordVO.setFileName(fileName);
        dmFopRecordVO.setFileSize(String.valueOf(fileSize));
        dmFopRecordVO.setStatus(status);
        dmFopRecordVO.setExceptionFeedback(errorMsg);
        dmFopRecordVO.setRecordNum(num);
        dmFopRecordVO.setPeriodId(periodId);
        dmFopRecordVO.setFileSourceKey(sourceKey);
        dmFopRecordVO.setFileErrorKey(errorKey);
        dmFopRecordVO.setOptType(optTyp);
        dmFopRecordVO.setPageModule(moudule);
        dmFopRecordVO.setCreationDate(TimeUtils.getCurTime());
        dmFopRecordVO.setDelFlag("N");
        dmFopRecordVO.setRecSts(recSts);
        dmFopRecordVO.setEndDate(TimeUtils.getCurTime());
        dmFopRecordVO.setId(Long.valueOf(TimeUtils.getBathNo()));
        dmFopRecordVO.setCreatedBy(String.valueOf(UserHandle.getUserId()));
        iDmFopRecordDao.create(dmFopRecordVO);
    }

    /**
     * excel模板下载
     *
     * @param response
     * @throws CommonApplicationException
     */
    public void templateDownload(HttpServletResponse response) throws CommonApplicationException {
        try {
            FileProcessUtis.downloadFile(response);
        } catch (Exception ex) {
            logger.error("下载模板异常: {}", ex);
            throw new CommonApplicationException("下载模板异常");
        }
    }

    /**
     * 初始月数据
     *
     * @param month
     * @param lv1Name
     * @return
     */
    public LabelCountVO initLabelCount(String month, String lv1Name) {
        LabelCountVO tempVO = new LabelCountVO();
        tempVO.setPeriodId(month);
        tempVO.setLv1Name(lv1Name);
        tempVO.setTotalNumber(0);
        tempVO.setModifyNumber(0);
        tempVO.setModifyRate(BigDecimal.ZERO);
        return tempVO;
    }

    /**
     * 设置模块数据
     *
     * @param forecastsRequest 预测类型
     * @param param1           请求响应等级
     * @param param2           l1列信息
     */
    public <T> void setModuleData(ForecastsRequest forecastsRequest, T param1, T param2) {
        if (Objects.isNull(param1) || Objects.isNull(param2)) {
            return;
        }
        Field bgFiled = ReflectionUtils.findField(param1.getClass(), "bgCode");
        Field targetPeriodFiled = ReflectionUtils.findField(param1.getClass(), "targetPeriod");
        Field dataTypeFiled = ReflectionUtils.findField(param1.getClass(), "dataType");
        ReflectionUtils.makeAccessible(bgFiled);
        ReflectionUtils.makeAccessible(targetPeriodFiled);
        ReflectionUtils.makeAccessible(dataTypeFiled);
        String bgCode = (String) ReflectionUtils.getField(bgFiled, param1);
        String targetPeriod = (String) ReflectionUtils.getField(targetPeriodFiled, param1);
        String dataType = (String) ReflectionUtils.getField(dataTypeFiled, param1);
        setOriginData(forecastsRequest, bgCode, targetPeriod, dataType, param1, param2);
        setFutureData(forecastsRequest, bgCode, targetPeriod, dataType, param1, param2);
        setAiCombinedData(forecastsRequest, bgCode, targetPeriod, dataType, param1, param2);
    }

    /**
     * 设置历史数据
     *
     * @param forecastsRequest
     * @param bgCode
     * @param targetPeriod
     * @param param
     * @param object
     * @param <T>
     */
    public <T> void setOriginData(ForecastsRequest forecastsRequest, String bgCode, String targetPeriod, String dataType, T param, T object) {
        // 实际数字段值设置
        if (!"ACT".equalsIgnoreCase(dataType)) {
            return;
        }
        // 默认年度
        String curPeriod = TimeUtils.getPeriod(forecastsRequest.getCurYear(), -1);
        // 半年度
        if (StringUtils.equals(forecastsRequest.getFcstStep(), CommonConstant.FCST_HALF_YEAR_STEP)) {
            curPeriod = forecastsRequest.getActStepPeriod();
        }
        // 季度色设置
        if (StringUtils.equals(forecastsRequest.getFcstStep(), CommonConstant.FCST_QUARTER_STEP)) {
            curPeriod = forecastsRequest.getActStepPeriod();
        }
        if (StringUtils.equals(curPeriod, targetPeriod)) {
            if (StringUtils.equals(bgCode, CommonConstant.BG_CNBG)) {
                ObjectUtils.setFieldVal(object, "cnbgLastYearActure", param);
            } else if (StringUtils.equals(bgCode, CommonConstant.BG_EBG)) {
                ObjectUtils.setFieldVal(object, "ebgLastYearActure", param);
            } else if (StringUtils.equals(bgCode, CommonConstant.BG_GROUP)) {
                ObjectUtils.setFieldVal(object, "lastYearActure", param);
            }
        }
        if (StringUtils.equals(PeriodTypeEnum.CUR_YEAR_SUM.getDesc(), targetPeriod)
                && StringUtils.equals(forecastsRequest.getPredictionType(), PredictionEnum.MONTH.getCode())) {
            if (StringUtils.equals(bgCode, CommonConstant.BG_CNBG)) {
                ObjectUtils.setFieldVal(object, "cnbgCurYearSum", param);
            } else if (StringUtils.equals(bgCode, CommonConstant.BG_EBG)) {
                ObjectUtils.setFieldVal(object, "ebgCurYearSum", param);
            } else if (StringUtils.equals(bgCode, CommonConstant.BG_GROUP)) {
                ObjectUtils.setFieldVal(object, "curYearSum", param);
            }
        }
        if (StringUtils.equals(PeriodTypeEnum.LAST_YEAR_PERIOD.getDesc(), targetPeriod)
                && StringUtils.equals(forecastsRequest.getPredictionType(), PredictionEnum.MONTH.getCode())) {
            if (StringUtils.equals(bgCode, CommonConstant.BG_CNBG)) {
                ObjectUtils.setFieldVal(object, "cnbgLastYearPeriod", param);
            } else if (StringUtils.equals(bgCode, CommonConstant.BG_EBG)) {
                ObjectUtils.setFieldVal(object, "ebgLastYearPeriod", param);
            } else if (StringUtils.equals(bgCode, CommonConstant.BG_GROUP)) {
                ObjectUtils.setFieldVal(object, "lastYearPeriod", param);
            }
        }
    }

    /**
     * 设置预测数据
     *
     * @param forecastsRequest
     * @param bgCode
     * @param targetPeriod
     * @param param
     * @param object
     * @param <T>
     */
    public <T> void setFutureData(ForecastsRequest forecastsRequest, String bgCode, String targetPeriod, String dataType, T param, T object) {
        // 处理预测表数据设置字段值
        if (!"FCST".equalsIgnoreCase(dataType) && !"BUDGET".equalsIgnoreCase(dataType)) {
            return;
        }
        // 年度赋值
        String curPeriod = TimeUtils.getPeriod(forecastsRequest.getCurYear(), 0);
        // 半年度赋值
        if (StringUtils.equals(forecastsRequest.getFcstStep(), CommonConstant.FCST_HALF_YEAR_STEP)) {
            curPeriod = forecastsRequest.getFcstStepPeriod();
        }
        // 季度赋值
        if (StringUtils.equals(forecastsRequest.getFcstStep(), CommonConstant.FCST_QUARTER_STEP)) {
            curPeriod = forecastsRequest.getFcstStepPeriod();
        }
        if (StringUtils.equals(curPeriod, targetPeriod) && StringUtils.equals("FCST", dataType)) {
            if (StringUtils.equals(bgCode, CommonConstant.BG_CNBG)) {
                ObjectUtils.setFieldVal(object, "cnbgAiAuto", param);
            } else if (StringUtils.equals(bgCode, CommonConstant.BG_EBG)) {
                ObjectUtils.setFieldVal(object, "ebgAiAuto", param);
            } else if (StringUtils.equals(bgCode, CommonConstant.BG_GROUP)) {
                ObjectUtils.setFieldVal(object, "aiAuto", param);
            }
        }
        if (StringUtils.equals(targetPeriod, getPeriod(forecastsRequest))
                && StringUtils.equals(forecastsRequest.getPredictionType(), PredictionEnum.YEAR.getCode())
                && StringUtils.equals("BUDGET", dataType)
        ) {
            if (StringUtils.equals(bgCode, CommonConstant.BG_CNBG)) {
                ObjectUtils.setFieldVal(object, "cnbgAi2Auto", param);
            } else if (StringUtils.equals(bgCode, CommonConstant.BG_EBG)) {
                ObjectUtils.setFieldVal(object, "ebgAi2Auto", param);
            } else if (StringUtils.equals(bgCode, CommonConstant.BG_GROUP)) {
                ObjectUtils.setFieldVal(object, "ai2Auto", param);
            }
        }
    }


    /**
     * 设置历史数据
     *
     * @param forecastsRequest
     * @param bgCode
     * @param targetPeriod
     * @param param
     * @param object
     * @param <T>
     */
    public <T> void setAiCombinedData(ForecastsRequest forecastsRequest, String bgCode, String targetPeriod, String dataType, T param, T object) {
        // 实际数字段值设置
        if (CommonConstant.DATA_TYPE_GROUP_ANALYSTS.equalsIgnoreCase(dataType)) {
            ObjectUtils.setFieldVal(object, DiffColumnTyp.GROUP_C6.getCode(), param);
            return;
        }
        setCombineValue(bgCode, dataType, param, object);
    }

    // 处理融合调优数据
    private static <T> void setCombineValue(String bgCode, String dataType, T param, T object) {
        if (!CommonConstant.DATA_TYPE_AI_COMBINED.equalsIgnoreCase(dataType)) {
            return;
        }
        Field combinedExpertTypeFiled = ReflectionUtils.findField(param.getClass(), "combinedExpertType");
        ReflectionUtils.makeAccessible(combinedExpertTypeFiled);
        String combinedExpertType = (String) ReflectionUtils.getField(combinedExpertTypeFiled, param);
        // 设置产业分析师字段值
        if (AI_COMBINED_EXPERT_TYPE_BG.equalsIgnoreCase(combinedExpertType)) {
            if (StringUtils.equals(bgCode, CommonConstant.BG_CNBG)) {
                ObjectUtils.setFieldVal(object, DiffColumnTyp.CNBG_C6.getCode(), param);
            }
            if (StringUtils.equals(bgCode, CommonConstant.BG_EBG)) {
                ObjectUtils.setFieldVal(object, DiffColumnTyp.EBG_C6.getCode(), param);
            }
        }
        // 设置产业分析师和集团分析师字段值
        if (StringUtils.equals(bgCode, CommonConstant.BG_GROUP)) {
            // 融合调优AI预测-集团版
            if (AI_COMBINED_EXPERT_TYPE_GROUP.equalsIgnoreCase(combinedExpertType)) {
                ObjectUtils.setFieldVal(object, DiffColumnTyp.GROUP_C7.getCode(), param);
            }
            // 融合调优AI预测
            if (AI_COMBINED_EXPERT_TYPE_BG.equalsIgnoreCase(combinedExpertType)) {
                ObjectUtils.setFieldVal(object, DiffColumnTyp.GROUP_C8.getCode(), param);
            }
        }
    }


    private String getPeriod(ForecastsRequest forecastsRequest) {
        Calendar systemCruYear = DateUtil.getPeriodCalendar(null);
        if (forecastsRequest.getCurYear() == systemCruYear.get(Calendar.YEAR)
                && forecastsRequest.getCurMonth() < 10) {
            return TimeUtils.getPeriod(forecastsRequest.getCurYear(), 0);
        } else {
            return TimeUtils.getPeriod(forecastsRequest.getCurYear(), 1);
        }
    }

    /**
     * 设置扩展信息
     *
     * @param result
     * @param resultList
     */
    public void setUserExtendInfo(List<SpartProfitingRelationVO> result, LabelConfigRequest requestVO, List<Map<String, Object>> resultList) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        Map<Long, String> userMap = getUserIds(requestVO);
        Map<String, String> statusMap = new HashMap<>();
        statusMap.put("Submit", "提交");
        statusMap.put("Save", "待提交");
        statusMap.put("Add", "新增标签");
        statusMap.put("His", "历史标签");
        result.stream().forEach(spart -> {
            spart.setCreatedCn(userMap.get(spart.getCreatedBy()));
            spart.setLastUpdateCn(userMap.get(spart.getLastUpdatedBy()));
            convert2List(statusMap, resultList, spart);
        });
    }

    public Map<Long, String> getUserIds(LabelConfigRequest requestVO) {
        Map<Long, String> resultMap = new HashMap<>();
        resultMap.put(-1L, "system");
        resultMap.put(0L, "system");
        resultMap.put(null, "system");
        List<LabelConfigRequest> userIds = iSpartProfitingRelationDao.findUserIdsByParam(requestVO);
        if (CollectionUtils.isEmpty(userIds)) {
            return resultMap;
        }
        Set<Long> uids = new HashSet<>();
        userIds.stream().forEach(user -> {
            if (null == user) {
                return;
            }
            if (user.getCreatedBy() != null && user.getCreatedBy() != -1 && user.getCreatedBy() != 0) {
                uids.add(user.getCreatedBy());
            }
            if (user.getLastUpdatedBy() != null && user.getLastUpdatedBy() != -1 && user.getLastUpdatedBy() != 0) {
                uids.add(user.getLastUpdatedBy());
            }
        });
        if (CollectionUtils.isEmpty(uids)) {
            return resultMap;
        }
        Map<Long, String> tempMap = getUserCnName(uids);
        resultMap.putAll(tempMap);
        return resultMap;
    }

    /**
     * 根据用户ID,批量获取用户中文名称
     *
     * @param uids
     * @return
     */
    public Map<Long, String> getUserCnName(Set<Long> uids) {
        List<UserVO> users = iUserQueryService.findUsersByMultiId(uids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(users)) {
            return new HashMap<>();
        }
        return users.stream().collect(Collectors.toMap(UserVO::getUserId, UserVO::getUserCN, (K, V) -> V));
    }


    /**
     * 对象转Map,填充数据
     *
     * @param resultList
     * @return
     */
    public void convert2List(Map<String, String> statusMap, List<Map<String, Object>> resultList, SpartProfitingRelationVO result) {
        Map<String, Object> data = new HashMap<>();
        data.put("itemCode", result.getItemCode());
        data.put("itemDesc", result.getItemDesc());
        data.put("lv1Name", result.getLv1Name());
        data.put("l1Name", result.getL1Name());
        data.put("l2Name", result.getL2Name());
        data.put("l3Name", result.getL3Name());
        data.put("l2CoefficientProb", result.getL2CoefficientProb());
        data.put("l2Coefficient", result.getL2Coefficient());
        data.put("l3CoefficientProb", result.getL3CoefficientProb());
        data.put("l3Coefficient", result.getL3Coefficient());
        data.put("l1CoefficientProb", result.getL1CoefficientProb());
        data.put("l1Coefficient", result.getL1Coefficient());
        data.put("l2NameProb", result.getL2NameProb());
        data.put("l3NameProb", result.getL3NameProb());
        data.put("dataType", statusMap.get(result.getDataType()));
        data.put("status", statusMap.get(result.getStatus()));
        data.put("createdBy", result.getCreatedCn());
        // TimeStamp数据转换成正常日期格式，去除毫秒展示
        String tempDate = result.getCreationDate().toString();
        if (tempDate.length() > 19) {
            tempDate = tempDate.substring(0, 19);
        }
        data.put("creationDate", tempDate);
        tempDate = result.getLastUpdateDate().toString();
        if (tempDate.length() > 19) {
            tempDate = tempDate.substring(0, 19);
        }
        data.put("lastUpdateDate", tempDate);
        data.put("lastUpdatedBy", result.getLastUpdateCn());
        resultList.add(data);
    }


    /**
     * 构建LV1
     *
     * @param originResponse
     */
    public KrCpfLv1AggrResponse buildLv1Info(KrCpfLv1AggrResponse originResponse) {
        KrCpfLv1AggrResponse lv1Row = new KrCpfLv1AggrResponse();
        lv1Row.setLv1Code(originResponse.getLv1Code());
        lv1Row.setLv1Name(originResponse.getLv1Name());
        if (null != originResponse.getLv2Name()) {
            lv1Row.setLv2Name(originResponse.getLv2Name());
            lv1Row.setLv2Code(originResponse.getLv2Code());
            if (null != originResponse.getL1Name()) {
                lv1Row.setL1Name(originResponse.getL1Name());
                setArticulationFlag(lv1Row);
            }
        }
        return lv1Row;
    }


    /**
     * 设置对应场景下钻信息
     *
     * @param lv2Row
     */
    public void setArticulationFlag(KrCpfLv1AggrResponse lv2Row) {
        lv2Row.setArticulationFlag(getArticulationVal(lv2Row.getLv1Name(), lv2Row.getLv2Name(), lv2Row.getL1Name()));
    }

    /**
     * 组装Key
     *
     * @param lv1
     * @param lv2Code
     * @param l1Name
     * @return
     */
    public String buildTempKey(KrCpfLv1AggrResponse lv1, String lv2Code, String l1Name) {
        String finalLv2Code = null;
        String finalL1Name = null;
        if (null != lv2Code) {
            finalLv2Code = lv1.getLv2Code();
        }
        if (null != l1Name) {
            finalL1Name = lv1.getL1Name();
        }
        return GenerateUtils.buildTempKey(lv1.getLv1Code(), finalLv2Code, finalL1Name, null);
    }

    /**
     * 获取场景信息
     *
     * @param lv1Name
     * @param lv2Name
     * @param l1Name
     * @return
     */
    public String getArticulationVal(String lv1Name, String lv2Name, String l1Name) {
        StringBuilder builder = new StringBuilder();
        builder.append(lv1Name)
                .append("|").append(lv2Name)
                .append("|").append(l1Name);
        List<String> sceno1 = FcstGlobalParameterUtil.getConfigValue().get("SCENO1");
        List<String> sceno2 = FcstGlobalParameterUtil.getConfigValue().get("SCENO2");
        if (CollectionUtils.isNotEmpty(sceno1) && sceno1.contains(builder.toString())) {
            return "1";
        } else if (CollectionUtils.isNotEmpty(sceno2) && sceno2.contains(builder.toString())) {
            return "2";
        } else {
            return "3";
        }
    }

    /**
     * 获取文件名称
     *
     * @param forecastsRequest
     * @param type
     * @return
     */
    public String getFileNameByBg(ForecastsRequest forecastsRequest, String type) {
        StringBuilder builder = new StringBuilder();
        if (StringUtils.equals("1", type)) {
            builder.append("设备收入及制毛率-");
        } else {
            builder.append("结构及制毛率-");
        }
        forecastsRequest.setBgCodes(Arrays.asList(forecastsRequest.getBgCode()));
        if (forecastsRequest.getBgCodes().size() == 1) {
            if (forecastsRequest.getBgCodes().contains("PROD0002")) {
                builder.append("产业整体-");
            } else if (forecastsRequest.getBgCodes().contains("PDCG901159")) {
                builder.append("CNBG-");
            }
            if (forecastsRequest.getBgCodes().contains("PDCG901160")) {
                builder.append("EBG-");
            }
        } else if (forecastsRequest.getBgCodes().size() == 2 && forecastsRequest.getBgCodes().contains("PDCG901159")
                && forecastsRequest.getBgCodes().contains("PDCG901160")) {
            builder.append("CNBG-EBG-");
        }
        if (StringUtils.isNotEmpty(forecastsRequest.getOverseaDesc())) {
            builder.append(forecastsRequest.getOverseaDesc()).append("-");
        }
        builder.append(forecastsRequest.getCurYear());
        builder.append("年");
        builder.append(forecastsRequest.getCurMonth());
        builder.append("月");
        if (StringUtils.equals("MONTH", forecastsRequest.getPredictionType())) {
            builder.append("预测");
        } else if (StringUtils.equals("YEAR", forecastsRequest.getPredictionType())) {
            builder.append("预算");
        }
        return builder.toString();
    }


    /**
     * 校验权限
     *
     * @param forecastsRequest
     * @param bgCodes
     * @return boolean
     */
    public boolean checkPermission(ForecastsRequest forecastsRequest, List<String> bgCodes) {
        DataPermissionsVO rightVO = getUserRoleOpt(forecastsRequest.getRoleId());
        if (!rightVO.isOptLv1Right() || !rightVO.isOptBgRight()) {
            return false;
        }
        Arrays.asList(forecastsRequest.getBgCode()).forEach(bg -> {
            if (StringUtils.equals("ALL", rightVO.getBgDataType())) {
                bgCodes.add(bg);
            } else if (rightVO.getBgDimensionSet().contains(bg)) {
                bgCodes.add(bg);
            }
        });
        if (CollectionUtils.isEmpty(bgCodes)) {
            return false;
        }
        forecastsRequest.setRightVO(rightVO);
        return true;
    }


    /**
     * 获取权限信息
     *
     * @return list
     * @throws CommonApplicationException
     */
    public DataPermissionsVO getRights(ForecastsRequest forecastsRequest) {
        return getUserRoleOpt(forecastsRequest.getRoleId());
    }


    /**
     * 按key分类
     *
     * @param lv2Code l2代码
     * @param l1Name  l1名称
     * @return Map
     */
    public Map<String, List<KrCpfLv1AggrResponse>> groupByKey(ForecastsRequest forecastsRequest, String
            lv2Code, String l1Name, AtomicReference<Map<String, List<KrCpfLv1AggrResponse>>> referenceVal) throws
            CommonApplicationException {
        // 202403版本，查询预测表 ytd法包含（时序法，ytd法） 数据，查询group by ，sum 了指标值
        List<KrCpfLv1AggrResponse> sourceData = iKrCpfLv1AggrDao.findDataByTop(forecastsRequest);
        return sourceData.stream().filter(source -> {
            String tempKey = buildTempKey(source, lv2Code, l1Name);
            source.setGroupKey(tempKey);
            if (StringUtils.equals("01", forecastsRequest.getLevel())) {
                return referenceVal.get().containsKey(tempKey);
            }
            return true;
        }).collect(Collectors.groupingBy(act -> act.getGroupKey()));
    }

    //  权限检验
    public boolean checkLv1Result(ForecastsRequest requestVO) {
        UserVO currentUser = (UserVO) RequestContext.getCurrent(true).getUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        DataPermissionsVO rightVO = getUserRoleOpt(currentRole.getRoleId());
        // 校验权限信息
        if (!rightVO.isOptLv1Right()) {
            return true;
        }
        // 判断是否为ALL
        if (!StringUtils.equals("ALL", rightVO.getLv1DataType())) {
            ArrayList<String> lv1s = new ArrayList<>(rightVO.getLv1DimensionSet());
            // 无产业权限
            if (org.springframework.util.CollectionUtils.isEmpty(lv1s)) {
                return true;
            }
            //  查询为全量产业，则设置查询为当前权限范围
            if (Objects.isNull(requestVO.getLv1Codes()) || requestVO.getLv1Codes().length == 0) {
                requestVO.setLv1Codes(lv1s.toArray(new String[0]));
            } else {
                //  否则查询产业为交集
                List<String> list = lv1s.stream()
                        .filter(Arrays.asList(requestVO.getLv1Codes())::contains)
                        .collect(Collectors.toList());
                if(CollectionUtil.isNullOrEmpty(list)){
                    return true;
                }
                requestVO.setLv1Codes(list.toArray(new String[0]));
            }
        }
        return false;
    }
}
