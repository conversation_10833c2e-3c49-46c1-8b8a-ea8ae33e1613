/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.huawei.it.fcst.profits.vo.BaseVOCoverUtilsTest;

class ProfitGranularityLv1InfoVoTest extends BaseVOCoverUtilsTest<ProfitGranularityLv1InfoVo> {
    @Override
    protected Class<ProfitGranularityLv1InfoVo> getTClass() {
        return ProfitGranularityLv1InfoVo.class;
    }
}