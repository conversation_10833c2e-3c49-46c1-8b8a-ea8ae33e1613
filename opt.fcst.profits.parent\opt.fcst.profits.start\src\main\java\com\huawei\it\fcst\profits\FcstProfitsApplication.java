/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.fcst.profits;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * TaxtpProviderApplication.
 *
 * <AUTHOR>
 * @since 2019年12月16日
 */
@SpringBootApplication
public class FcstProfitsApplication {
    /** 
     * 启动
     * 本地启动：System.setProperty("hwenvironment", "uat");
     * <AUTHOR> zhaofei
     * @since 2019年12月16日 上午11:26:44
     * @param args 参数
     * @return [void] 返回值
     */
    public static void main(String[] args) {
        // 此处的这个值是用于在sit,uat,prodution环境时使用的，暂时不要删除。因为删除了会导致Pcloud上不能部登陆成功。
        // 如果登陆发现日志包，IP没有注册，则需要SSO的人，是不是注册的在uat，一般测试环境的值为sit或者uat.发生产包用pro
        SpringApplication.run(FcstProfitsApplication.class);
    }
}
