/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.huawei.it.fcst.profits.vo.request.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

/**
 * The Entity of LabelModifyStaticsVO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-17 19:38:39
 */
@Getter
@Setter
public class LabelModifyStaticsVO extends PageRequest {
    private static final long serialVersionUID = 1L;

    /**
     * 最后更新人
     **/
    @JsonProperty("last_updated_by")
    private String lastUpdatedBy;

    /**
     * 模块
     **/
    @JsonProperty("page_module")
    private String pageModule;

    /**
     * 修改前
     **/
    @JsonProperty("status")
    private String status;

    /**
     * 产业
     **/
    @JsonProperty("lv1_name")
    private String lv1Name;

    private String id;

    /**
     * 创建时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("creation_date")
    private Timestamp creationDate;

    /**
     * 版本
     **/
    @JsonProperty("period_id")
    private Long periodId;

    /**
     * 创建人
     **/
    @JsonProperty("created_by")
    private String createdBy;

    /**
     * 最后更新时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("last_update_date")
    private Timestamp lastUpdateDate;

    @JsonProperty("modify_size")
    private Integer modifySize;

    private List<String> periods;
}
