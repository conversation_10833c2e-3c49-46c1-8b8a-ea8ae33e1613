/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.json.JsonSanitizer;
import com.huawei.it.fcst.profits.common.vo.HeaderVo;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * JsonUtils
 *
 * <AUTHOR>
 * @since 2022/10/02.
 */
public class JsonUtils {
    private static ObjectMapper mapper;

    private static final int COLLECTION_START = 0;

    private static final int GROUP_FORMER = 1;

    private static final int GROUP_LATTER = 2;

    static {
        mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private JsonUtils() {
    }

    /**
     * toJsonString
     *
     * @param object Object
     * @return string
     * @throws IOException IOException
     */
    public static String toJsonString(Object object) throws IOException {
        return mapper.writeValueAsString(object);
    }

    /**
     * toJsonStream
     *
     * @param object json object
     * @param out    outputstream
     * @throws IOException
     */
    public static void toJsonStream(Object object, OutputStream out) throws IOException {
        mapper.writeValue(out, object);
    }

    /**
     * stringToObject
     *
     * @param jsonString String
     * @param clz        Class
     * @param <T>        Class
     * @return T instance
     * @throws IOException IOException
     */
    public static <T> T stringToObject(String jsonString, Class<T> clz) throws IOException {
        return mapper.readValue(JsonSanitizer.sanitize(jsonString), clz);
    }

    /**
     * stringToObject
     *
     * @param jsonString    String
     * @param typeReference TypeReference
     * @param <T>           TypeReference
     * @return T instance
     * @throws IOException IOException
     */
    public static <T> T stringToObject(String jsonString, TypeReference<T> typeReference) throws IOException {
        return mapper.readValue(JsonSanitizer.sanitize(jsonString), typeReference);
    }

    /**
     * bytesToObject
     *
     * @param jsonBytes 　jsonBytes
     * @param clz       　clz
     * @param <T>       　<T>
     * @return Ｔ <T>
     * @throws IOException
     */
    public static <T> T bytesToObject(byte[] jsonBytes, Class<T> clz) throws IOException {
        return mapper.readValue(jsonBytes, clz);
    }

    /**
     * bytesToObject
     *
     * @param jsonBytes     jsonBytes
     * @param typeReference typeReference
     * @param <T>           T
     * @return T
     * @throws IOException
     */
    public static <T> T bytesToObject(byte[] jsonBytes, TypeReference<T> typeReference) throws IOException {
        return mapper.readValue(jsonBytes, typeReference);
    }

    /**
     * toJsonBytes
     *
     * @param object object
     * @return byte[]
     * @throws IOException
     */
    public static byte[] toJsonBytes(Object object) throws IOException {
        return mapper.writeValueAsBytes(object);
    }

    /**
     * mapToObject
     *
     * @param model model
     * @param maps  maps
     * @return JSONArray
     */
    public static JSONArray mapToObject(List<HeaderVo> model, List<Map<String, Object>> maps) {
        JSONArray jsonArray = new JSONArray();
        for (int i = COLLECTION_START; i < maps.size(); i++) {
            Map<String, Object> map = maps.get(i);
            JSONObject jsonObject = new JSONObject();
            Set<String> keys = map.keySet();
            for (String key : keys) {
                for (HeaderVo headerVo : model) {
                    if (StringUtils.equals(key, headerVo.getTitle())) {
                        jsonObject.put(headerVo.getField(), map.get(headerVo.getTitle()));
                    }
                }
            }
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    /**
     * formatField
     *
     * @param string  string
     * @param pattern pattern
     * @return string
     */
    public static String formatField(String string, Pattern pattern) {
        String tmp = "";
        Matcher matcher = pattern.matcher(string);
        if (matcher.find()) {
            String group = matcher.group(GROUP_FORMER);
            String replace = string.replace(group, matcher.group(GROUP_LATTER).toUpperCase(Locale.ROOT));
            tmp = formatField(replace, pattern);
        }
        return tmp;
    }
}
