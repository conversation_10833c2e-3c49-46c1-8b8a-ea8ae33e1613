/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2024. All rights reserved.
 */

package com.huawei.it.fcst.profits.utils;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.vo.GroupAnalystsVO;

import cn.hutool.core.util.NumberUtil;

import org.apache.commons.lang.StringUtils;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * GroupAnalystsUtils
 *
 * @since 2024-04-12
 */
public class GroupAnalystsUtils {

    public static final Pattern PATTERN = Pattern.compile("^\\d{4}(0[1-9]|1[0-2])$");

    /**
     * checkVersionCode
     *
     * @param data data
     * @return boolean
     */
    public static boolean checkVersionCode(GroupAnalystsVO data) {
        if (Objects.isNull(data) || Objects.isNull(data.getVersionCode())) {
            return true;
        }
        Matcher matcher = PATTERN.matcher(data.getVersionCode());
        if (matcher.find()) {
            return false;
        } else {
            return true;
        }
    }

    public static boolean checkEquipRevAmt(GroupAnalystsVO data) {
        String equipRevAmt = data.getEquipRevAmt();
        if (Objects.isNull(data) || Objects.isNull(equipRevAmt)) {
            return true;
        }
        if (!NumberUtil.isNumber(equipRevAmt)) {
            return true;
        }
        return false;
    }

    public static boolean checkMgpRatio(GroupAnalystsVO data) {
        String mgpRatio = data.getMgpRatio();
        if (Objects.isNull(data) || Objects.isNull(mgpRatio)) {
            return true;
        }
        if (mgpRatio.contains("%")) {
            mgpRatio = mgpRatio.replace("%","");
        }
        if (!NumberUtil.isNumber(mgpRatio)) {
            return true;
        }
        return false;
    }

    public static boolean checkTargetDesc(GroupAnalystsVO data) {
        String targetDesc = data.getTargetDesc();
        return Objects.isNull(data) || Objects.isNull(targetDesc) || !CommonConstant.GROUP_ANALYSTS_FCST_STEP_LIST.contains(targetDesc);
    }

    public static String getTargetCode(GroupAnalystsVO vo) {
        if(StringUtils.equals("年度",vo.getTargetDesc())){
            return "Y";
        } else if (StringUtils.equals("季度",vo.getTargetDesc())) {
            return "Q";
        } else if (StringUtils.equals("半年度",vo.getTargetDesc())) {
            return "H";
        } else {
            return "M";
        }
    }
}
