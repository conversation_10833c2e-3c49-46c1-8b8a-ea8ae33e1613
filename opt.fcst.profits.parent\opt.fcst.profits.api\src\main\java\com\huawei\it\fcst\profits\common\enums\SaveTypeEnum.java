/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * SaveTypeEnum
 *
 * <AUTHOR>
 * @date 2022/10/06 10:34
 **/
public enum SaveTypeEnum {
    MODIFY_FLAG("Modify", "Y"),
    SAVE("Save", "保存"),
    SUBMIT("Submit", "提交");

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String desc;

    SaveTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * getByCode
     *
     * @param code code
     * @return StepTypeEnum
     */
    public static SaveTypeEnum getByCode(String code) {
        for (SaveTypeEnum value : SaveTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

