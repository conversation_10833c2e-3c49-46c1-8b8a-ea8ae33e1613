/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.comm;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.poi.ExcelUtil;
import com.huawei.it.fcst.profits.common.poi.ExcelUtilPro;
import com.huawei.it.fcst.profits.common.poi.PoiEnum;
import com.huawei.it.fcst.profits.common.utils.FileProcessUtis;
import com.huawei.it.fcst.profits.common.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.BranchExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.ExcelCellStyle;
import com.huawei.it.fcst.profits.common.vo.LeafExcelTitleVO;
import com.huawei.it.fcst.profits.common.vo.TableHeaderVo;
import com.huawei.it.fcst.profits.service.IExportService;
import com.huawei.it.fcst.profits.utils.HeaderTitleUtils;
import com.huawei.it.fcst.profits.utils.HeaderUtils;
import com.huawei.it.fcst.profits.vo.response.KrCpfL2Response;
import com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.awt.Color;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * The Service of ExportService
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:25:12
 */
@Service
public class ExportService extends AbstractService implements IExportService {

    private static final Logger logger = LoggerFactory.getLogger(ExportService.class);

    private static final String DATA_FORMAT_DECIMALS = "######0.00";

    @Autowired
    private ExcelUtil excelUtil;

    @Autowired
    private ExcelUtilPro excelUtilPro;

    /**
     * 导出Lv1数据
     *
     * @param lv1Result Lv1数据
     * @param response
     */
    @Override
    public void exportLv1Data(String type, List<KrCpfLv1AggrResponse> lv1Result, List<TableHeaderVo> lv1Header, HttpServletResponse response, Map<String, Object> params) throws CommonApplicationException {
        exportExcelFile(type, buidLv1Data(lv1Result), lv1Header, response, params);
    }

    /**
     * 导出Lv2数据
     *
     * @param l2Result Lv2数据
     */
    @Override
    public void exportL2Data(Workbook workbook, List<KrCpfL2Response> l2Result, List<TableHeaderVo> l2Header, Map<String, Object> params) throws CommonApplicationException {
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        rebuildHeader(l2Header, titleVoList, titles);
        params.put("objs", buidL2Data(l2Result));
        params.put("selTitles", titles);
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = new ArrayList<>();
        int titleRowCount = excelUtilPro.adjustTitleVoList(titleVoList, titles, selectedLeafExcelTitleVO);
        params.put("titleRowCount", titleRowCount);
        params.put("excelTitleVOS", titleVoList);
        params.put("sheetName", "sheet1");
        params.put("selectedLeafExcelTitleVO", selectedLeafExcelTitleVO);
        PoiEnum.exportExcel(workbook,0, params);
    }


    @Override
    public void exportL1Factor(Workbook workbook, List<Map<String,Object>> list, Map<String, Object> params) throws CommonApplicationException {
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        // 构建导出Excel的表头信息
        HeaderUtils.buildHeader(titleVoList, titles,
                HeaderTitleUtils.setExportL1());
        params.put("objs", list);
        params.put("selTitles", titles);
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = new ArrayList<>();
        int titleRowCount = excelUtilPro.adjustTitleVoList(titleVoList, titles, selectedLeafExcelTitleVO);
        params.put("titleRowCount", titleRowCount);
        params.put("excelTitleVOS", titleVoList);
        params.put("selectedLeafExcelTitleVO", selectedLeafExcelTitleVO);
        PoiEnum.exportExcel(workbook,0, params);
    }

    @Override
    public void exportL2Factor(Workbook workbook, List<Map<String,Object>> list , Map<String, Object> params) throws CommonApplicationException {
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        // 构建导出Excel的表头信息
        HeaderUtils.buildHeader(titleVoList, titles,
                HeaderTitleUtils.setExportL2());
        params.put("objs", list);
        params.put("selTitles", titles);
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = new ArrayList<>();
        int titleRowCount = excelUtilPro.adjustTitleVoList(titleVoList, titles, selectedLeafExcelTitleVO);
        params.put("titleRowCount", titleRowCount);
        params.put("excelTitleVOS", titleVoList);
        params.put("selectedLeafExcelTitleVO", selectedLeafExcelTitleVO);
        PoiEnum.exportExcel(workbook,1, params);
    }

    /**
     * 导出标签审视数据
     */
    @Override
    public void exportAuditData(Map<String, Object> params, String userId, List<?> list) {
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        // 构建导出Excel的表头信息
        HeaderUtils.buildHeader(titleVoList, titles,
                HeaderUtils.buildHeader(HeaderUtils.AUDIT_CHILD_IMP_ERROR_HEADER));
        String fileName = FileProcessUtis.getFileName(params);
        File targetFile = null;
        FileOutputStream outputStream = null;
        try {
            targetFile = File.createTempFile(fileName, ".xlsx");
            outputStream = new FileOutputStream(targetFile);
            excelUtil.expSelectColumnExcel(outputStream, params, userId, titles, titleVoList, list);
            params.put("fileKey", FileProcessUtis.uploadToS3(targetFile, fileName, String.valueOf(params.get("userId"))));
            params.put("fileName", fileName);
        } catch (Exception ex) {
            logger.error("导出修改数据异常");
        } finally {
            deleteTargetFile(targetFile, outputStream);
        }
    }

    /**
     * 导出标签审视数据
     */
    @Override
    public void exportAuditData(Map<String, Object> params, List<?> resultList, HttpServletResponse response) throws CommonApplicationException {
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        // 构建导出Excel的表头信息
        HeaderUtils.buildHeader(titleVoList, titles, HeaderUtils.buildHeader(HeaderUtils.AUDIT_CHILD_EXP_HEADER));
        excelUtil.expSelectColumnExcel(params, titles, titleVoList, resultList, response);
        File targetFile = null;
        FileOutputStream outputStream = null;
        try {
            String tempFileName = String.valueOf(params.get("fileName"));
            targetFile = File.createTempFile(tempFileName, ".xlsx");
            outputStream = new FileOutputStream(targetFile);
            PoiEnum.exportExcel(outputStream, params);
            params.put("fileKey", FileProcessUtis.uploadToS3(targetFile, tempFileName + ".xlsx", String.valueOf(params.get("userId"))));
            params.put("fileSize", targetFile.length() / 1024);
        } catch (Exception ex) {
            logger.error("导出上传s3失败");
        } finally {
            deleteTargetFile(targetFile, outputStream);
        }
    }

    private void deleteTargetFile(File targetFile, FileOutputStream outputStream) {
        try {
            if (null != targetFile) {
                if (!targetFile.delete()) {
                    logger.error("删除临时文件失败");
                }
            }
        } catch (Exception ex) {
            logger.error("删除临时文件失败");
        }
        if (null != outputStream) {
            try {
                outputStream.close();
            } catch (IOException e) {
                logger.error("close Wb-Exception.{}", CommonConstant.STREAM_OUT_CLOSE_FAILLED);
            }
        }
    }

    /**
     * 导出修改数据
     *
     * @throws CommonApplicationException
     */
    @Override
    public void exportModifyData(Map<String, Object> params, List<?> list) throws CommonApplicationException {
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        // 构建导出Excel的表头信息
        HeaderUtils.buildHeader(titleVoList, titles, HeaderUtils.buildHeader(HeaderUtils.AUDIT_CHILD_MODIFY_HEADER));
        String fileName = FileProcessUtis.getFileName(params);
        File targetFile = null;
        FileOutputStream outputStream = null;
        try {
            targetFile = File.createTempFile(fileName, ".xlsx");
            outputStream = new FileOutputStream(targetFile);
            excelUtil.expSelectColumnExcel(outputStream, params, String.valueOf(params.get("userId")), titles, titleVoList, list);
            params.put("fileKey", FileProcessUtis.uploadToS3(targetFile, fileName, String.valueOf(params.get("userId"))));
        } catch (Exception ex) {
            logger.error("导出修改数据异常");
        } finally {
            deleteTargetFile(targetFile, outputStream);
        }
    }

    /**
     * 组装CNBG-EBG数据
     *
     * @param lv1Result
     * @return
     */
    public List<Map> buidLv1Data(List<KrCpfLv1AggrResponse> lv1Result) {
        List<Map> list = new ArrayList<>();
        lv1Result.stream().forEach(result -> {
            Map<String, Object> tempRow = new HashMap<>();
            // lv1名称
            tempRow.put("lv1Name", result.getLv1Name());
            // Lv2名称
            tempRow.put("lv2Name", result.getLv2Name());
            // L1名称
            tempRow.put("l1Name", result.getL1Name());
            tempRowPut(tempRow, result);
            tempRowPuts(tempRow, result);
            tempRowPuts1(tempRow,result);
            list.add(tempRow);
        });
        return list;
    }
    private void tempRowPuts1(Map<String, Object> tempRow, KrCpfLv1AggrResponse result){
        if (null != result.getCombinedExpertGroup()) {
            // AI auto设备收入
            tempRow.put("combinedExpertGroupEquipRevAfter", result.getCombinedExpertGroup().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("combinedExpertGroupMgpRateAfter", result.getCombinedExpertGroup().getMgpRateAfter());
        }
        if (null != result.getCombinedExpert()) {
            // AI auto设备收入
            tempRow.put("combinedExpertEquipRevAfter", result.getCombinedExpert().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("combinedExpertMgpRateAfter", result.getCombinedExpert().getMgpRateAfter());
        }
        if (null != result.getGroupAnalyst()) {
            // AI auto设备收入
            tempRow.put("groupAnalystEquipRevAfter", result.getGroupAnalyst().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("groupAnalystMgpRateAfter", result.getGroupAnalyst().getMgpRateAfter());
        }
        if (null != result.getCnbgCombinedExpert()) {
            // AI auto设备收入
            tempRow.put("cnbgCombinedExpertEquipRevAfter", result.getCnbgCombinedExpert().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("cnbgCombinedExpertMgpRateAfter", result.getCnbgCombinedExpert().getMgpRateAfter());
        }
        if (null != result.getEbgCombinedExpert()) {
            // AI auto设备收入
            tempRow.put("ebgCombinedExpertEquipRevAfter", result.getEbgCombinedExpert().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("ebgCombinedExpertMgpRateAfter", result.getEbgCombinedExpert().getMgpRateAfter());
        }
    }
    private void tempRowPuts(Map<String, Object> tempRow, KrCpfLv1AggrResponse result) {
        if (null != result.getEbgAiAuto()) {
            // AI auto设备收入
            tempRow.put("ebgAiAutoEquipRevAfter", result.getEbgAiAuto().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("ebgAiAutoMgpRateAfter", result.getEbgAiAuto().getMgpRateAfter());
        }
        if (null != result.getEbgAi2Auto()) {
            // AI auto设备收入
            tempRow.put("ebgAi2AutoEquipRevAfter", result.getEbgAi2Auto().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("ebgAi2AutoMgpRateAfter", result.getEbgAi2Auto().getMgpRateAfter());
        }
        if (null != result.getCnbgLastYearActure()) {
            // 去年实际设备收入
            tempRow.put("cnbgLastActureEquipRevAfter", result.getCnbgLastYearActure().getEquipRevAfter());
            // 去年实际制毛率
            tempRow.put("cnbgLastActureMgpRateAfter", result.getCnbgLastYearActure().getMgpRateAfter());
        }
        if (null != result.getCnbgCurYearSum()) {
            // 当年累计设备收入
            tempRow.put("cnbgCurSumEquipRevAfter", result.getCnbgCurYearSum().getEquipRevAfter());
            // 当年累计制毛率
            tempRow.put("cnbgCurSumMgpRateAfter", result.getCnbgCurYearSum().getMgpRateAfter());
        }
        if (null != result.getCnbgLastYearPeriod()) {
            // 去年同期设备收入
            tempRow.put("cnbgLastPeriodEquipRevAfter", result.getCnbgLastYearPeriod().getEquipRevAfter());
            // 去年同期制毛率
            tempRow.put("cnbgLastPeriodmgpRateAfter", result.getCnbgLastYearPeriod().getMgpRateAfter());
        }
        if (null != result.getCnbgAiAuto()) {
            // AI auto设备收入
            tempRow.put("cnbgAiAutoEquipRevAfter", result.getCnbgAiAuto().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("cnbgAiAutoMgpRateAfter", result.getCnbgAiAuto().getMgpRateAfter());
        }
        if (null != result.getCnbgAi2Auto()) {
            // AI auto设备收入
            tempRow.put("cnbgAi2AutoEquipRevAfter", result.getCnbgAi2Auto().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("cnbgAi2AutoMgpRateAfter", result.getCnbgAi2Auto().getMgpRateAfter());
        }
    }

    private void tempRowPut(Map<String, Object> tempRow,
                            KrCpfLv1AggrResponse result) {
        if (null != result.getLastYearActure()) {
            // 去年实际设备收入
            tempRow.put("lastActureEquipRevAfter", result.getLastYearActure().getEquipRevAfter());
            // 去年实际制毛率
            tempRow.put("lastActureMgpRateAfter", result.getLastYearActure().getMgpRateAfter());
        }
        if (null != result.getCurYearSum()) {
            // 当年累计设备收入
            tempRow.put("curSumEquipRevAfter", result.getCurYearSum().getEquipRevAfter());
            // 当年累计制毛率
            tempRow.put("curSumMgpRateAfter", result.getCurYearSum().getMgpRateAfter());
        }
        if (null != result.getLastYearPeriod()) {
            // 去年同期设备收入
            tempRow.put("lastPeriodEquipRevAfter", result.getLastYearPeriod().getEquipRevAfter());
            // 去年同期制毛率
            tempRow.put("lastPeriodmgpRateAfter", result.getLastYearPeriod().getMgpRateAfter());
        }
        if (null != result.getAiAuto()) {
            // AI auto设备收入
            tempRow.put("aiAutoEquipRevAfter", result.getAiAuto().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("aiAutoMgpRateAfter", result.getAiAuto().getMgpRateAfter());
        }
        if (null != result.getAi2Auto()) {
            // AI auto设备收入
            tempRow.put("ai2AutoEquipRevAfter", result.getAi2Auto().getEquipRevAfter());
            // AI auto制毛率
            tempRow.put("ai2AutoMgpRateAfter", result.getAi2Auto().getMgpRateAfter());
        }
        if (null != result.getEbgLastYearActure()) {
            // 去年实际设备收入
            tempRow.put("ebgLastActureEquipRevAfter", result.getEbgLastYearActure().getEquipRevAfter());
            // 去年实际制毛率
            tempRow.put("ebgLastActureMgpRateAfter", result.getEbgLastYearActure().getMgpRateAfter());
        }
        if (null != result.getEbgCurYearSum()) {
            // 当年累计设备收入
            tempRow.put("ebgCurSumEquipRevAfter", result.getEbgCurYearSum().getEquipRevAfter());
            // 当年累计制毛率
            tempRow.put("ebgCurSumMgpRateAfter", result.getEbgCurYearSum().getMgpRateAfter());
        }
        if (null != result.getEbgLastYearPeriod()) {
            // 去年同期设备收入
            tempRow.put("ebgLastPeriodEquipRevAfter", result.getEbgLastYearPeriod().getEquipRevAfter());
            // 去年同期制毛率
            tempRow.put("ebgLastPeriodmgpRateAfter", result.getEbgLastYearPeriod().getMgpRateAfter());
        }
    }

    /**
     * 构建lv2结构制毛率数据
     *
     * @param l2Result Lv2数据
     * @return List
     */
    public List<Map> buidL2Data(List<KrCpfL2Response> l2Result) {
        List<Map> list = new ArrayList<>();
        l2Result.stream().forEach(result -> {
            Map<String, Object> tempRow = new HashMap<>();
            tempRow.put("l2Name", result.getL2Name());
            setTempRow1(result, tempRow);
            setTempRow2(result, tempRow);
            setTempRow3(result, tempRow);
            list.add(tempRow);
        });
        return list;
    }

    private void setTempRow3(KrCpfL2Response result, Map<String, Object> tempRow) {
        if (null != result.getCnbgLastYearActure()) {
            // 去年实际设备收入
            tempRow.put("cnbgLastActureRevPercent", result.getCnbgLastYearActure().getRevPercent());
            // 去年实际制毛率
            tempRow.put("cnbgLastActureMgpRateBefore", result.getCnbgLastYearActure().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("cnbgLastActureMcaAdjustRatio", result.getCnbgLastYearActure().getMcaAdjustRatio());
                tempRow.put("cnbgLastActureMgpAdjustRatio", result.getCnbgLastYearActure().getMgpAdjustRatio());
            }
        }
        if (null != result.getCnbgCurYearSum()) {
            // 当年累计设备收入
            tempRow.put("cnbgCurSumRevPercent", result.getCnbgCurYearSum().getRevPercent());
            // 当年累计制毛率
            tempRow.put("cnbgCurSumMgpRateBefore", result.getCnbgCurYearSum().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("cnbgCurSumMcaAdjustRatio", result.getCnbgCurYearSum().getMcaAdjustRatio());
                tempRow.put("cnbgCurSumMgpAdjustRatio", result.getCnbgCurYearSum().getMgpAdjustRatio());
            }
        }
        if (null != result.getCnbgLastYearPeriod()) {
            // 去年同期设备收入
            tempRow.put("cnbgLastPeriodRevPercent", result.getCnbgLastYearPeriod().getRevPercent());
            // 去年同期制毛率
            tempRow.put("cnbgLastPeriodMgpRateBefore", result.getCnbgLastYearPeriod().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("cnbgLastPeriodMcaAdjustRatio", result.getCnbgLastYearPeriod().getMcaAdjustRatio());
                tempRow.put("cnbgLastPeriodMgpAdjustRatio", result.getCnbgLastYearPeriod().getMgpAdjustRatio());
            }
        }
        if (null != result.getCnbgAiAuto()) {
            // AI auto设备收入
            tempRow.put("cnbgAiAutoRevPercent", result.getCnbgAiAuto().getRevPercent());
            // AI auto制毛率
            tempRow.put("cnbgAiAutoMgpRateBefore", result.getCnbgAiAuto().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("cnbgAiAutoMcaAdjustRatio", result.getCnbgAiAuto().getMcaAdjustRatio());
                tempRow.put("cnbgAiAutoMgpAdjustRatio", result.getCnbgAiAuto().getMgpAdjustRatio());
            }
        }
        if (null != result.getCnbgAi2Auto()) {
            // AI auto设备收入
            tempRow.put("cnbgAi2AutoRevPercent", result.getCnbgAi2Auto().getRevPercent());
            // AI auto制毛率
            tempRow.put("cnbgAi2AutoMgpRateBefore", result.getCnbgAi2Auto().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("cnbgAi2AutoMcaAdjustRatio", result.getCnbgAi2Auto().getMcaAdjustRatio());
                tempRow.put("cnbgAi2AutoMgpAdjustRatio", result.getCnbgAi2Auto().getMgpAdjustRatio());
            }
        }
    }

    private void setTempRow2(KrCpfL2Response result, Map<String, Object> tempRow) {
        if (null != result.getEbgLastYearActure()) {
            // 去年实际设备收入
            tempRow.put("ebgLastActureRevPercent", result.getEbgLastYearActure().getRevPercent());
            // 去年实际制毛率
            tempRow.put("ebgLastActureMgpRateBefore", result.getEbgLastYearActure().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("ebgLastActureMcaAdjustRatio", result.getEbgLastYearActure().getMcaAdjustRatio());
                tempRow.put("ebgLastActureMgpAdjustRatio", result.getEbgLastYearActure().getMgpAdjustRatio());
            }
        }
        if (null != result.getEbgCurYearSum()) {
            // 当年累计设备收入
            tempRow.put("ebgCurSumRevPercent", result.getEbgCurYearSum().getRevPercent());
            // 当年累计制毛率
            tempRow.put("ebgCurSumMgpRateBefore", result.getEbgCurYearSum().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("ebgCurSumMcaAdjustRatio", result.getEbgCurYearSum().getMcaAdjustRatio());
                tempRow.put("ebgCurSumMgpAdjustRatio", result.getEbgCurYearSum().getMgpAdjustRatio());
            }
        }
        if (null != result.getEbgLastYearPeriod()) {
            // 去年同期设备收入
            tempRow.put("ebgLastPeriodRevPercent", result.getEbgLastYearPeriod().getRevPercent());
            // 去年同期制毛率
            tempRow.put("ebgLastPeriodMgpRateBefore", result.getEbgLastYearPeriod().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("ebgLastPeriodMcaAdjustRatio", result.getEbgLastYearPeriod().getMcaAdjustRatio());
                tempRow.put("ebgLastPeriodMgpAdjustRatio", result.getEbgLastYearPeriod().getMgpAdjustRatio());
            }
        }
        if (null != result.getEbgAiAuto()) {
            // AI auto设备收入
            tempRow.put("ebgAiAutoRevPercent", result.getEbgAiAuto().getRevPercent());
            // AI auto制毛率
            tempRow.put("ebgAiAutoMgpRateBefore", result.getEbgAiAuto().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("ebgAiAutoMcaAdjustRatio", result.getEbgAiAuto().getMcaAdjustRatio());
                tempRow.put("ebgAiAutoMgpAdjustRatio", result.getEbgAiAuto().getMgpAdjustRatio());
            }
        }
        if (null != result.getEbgAi2Auto()) {
            // AI auto设备收入
            tempRow.put("ebgAi2AutoRevPercent", result.getEbgAi2Auto().getRevPercent());
            // AI auto制毛率
            tempRow.put("ebgAi2AutoMgpRateBefore", result.getEbgAi2Auto().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("ebgAi2AutoMcaAdjustRatio", result.getEbgAi2Auto().getMcaAdjustRatio());
                tempRow.put("ebgAi2AutoMgpAdjustRatio", result.getEbgAi2Auto().getMgpAdjustRatio());
            }
        }
    }

    private void setTempRow1(KrCpfL2Response result, Map<String, Object> tempRow) {
        if (null != result.getLastYearActure()) {
            tempRow.put("lastActureRevPercent", result.getLastYearActure().getRevPercent());
            tempRow.put("lastActureMgpRateBefore", result.getLastYearActure().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("lastActureMcaAdjustRatio", result.getLastYearActure().getMcaAdjustRatio());
                tempRow.put("lastActureMgpAdjustRatio", result.getLastYearActure().getMgpAdjustRatio());
            }
        }
        if (null != result.getCurYearSum()) {
            tempRow.put("curSumRevPercent", result.getCurYearSum().getRevPercent());
            tempRow.put("curSumMgpRateBefore", result.getCurYearSum().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("curSumMcaAdjustRatio", result.getCurYearSum().getMcaAdjustRatio());
                tempRow.put("curSumMgpAdjustRatio", result.getCurYearSum().getMgpAdjustRatio());
            }
        }
        if (null != result.getLastYearPeriod()) {
            tempRow.put("lastPeriodRevPercent", result.getLastYearPeriod().getRevPercent());
            tempRow.put("lastPeriodMgpRateBefore", result.getLastYearPeriod().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("lastPeriodMcaAdjustRatio", result.getLastYearPeriod().getMcaAdjustRatio());
                tempRow.put("lastPeriodMgpAdjustRatio", result.getLastYearPeriod().getMgpAdjustRatio());
            }
        }
        if (null != result.getAiAuto()) {
            tempRow.put("aiAutoRevPercent", result.getAiAuto().getRevPercent());
            tempRow.put("aiAutoMgpRateBefore", result.getAiAuto().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("aiAutoMcaAdjustRatio", result.getAiAuto().getMcaAdjustRatio());
                tempRow.put("aiAutoMgpAdjustRatio", result.getAiAuto().getMgpAdjustRatio());
            }
        }
        if (null != result.getAi2Auto()) {
            tempRow.put("ai2AutoRevPercent", result.getAi2Auto().getRevPercent());
            tempRow.put("ai2AutoMgpRateBefore", result.getAi2Auto().getMgpRateBefore());
            // 若为L2小计，增加展示项
            if ("L2小计".equals(result.getL2Name())) {
                tempRow.put("ai2AutoMcaAdjustRatio", result.getAi2Auto().getMcaAdjustRatio());
                tempRow.put("ai2AutoMgpAdjustRatio", result.getAi2Auto().getMgpAdjustRatio());
            }
        }
    }

    /**
     * 导出文件
     *
     * @param list     list信息
     * @param headers  Vo信息
     * @param response 处理响应
     */
    public void exportExcelFile(String type, List<Map> list, List<TableHeaderVo> headers, HttpServletResponse response, Map<String, Object> params) throws CommonApplicationException {
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        // Bg的种类不同，构建表格式不同。type值跟Bgcode有关:Group(1),其余bgcode(2)
        if (StringUtils.equals("1", type)) {
            columnTitle(headers, titles, titleVoList);
        } else {
            rebuildHeader(headers, titleVoList, titles);
        }
        File targetFile = null;
        FileOutputStream outputStream = null;
        try {
            excelUtil.expSelectColumnExcel(params, titles, titleVoList, list, response);
            if (Objects.nonNull(params.get("fileName"))) {
                String fileName = String.valueOf(params.get("fileName"));
                targetFile = File.createTempFile(fileName, ".xlsx");
                outputStream = new FileOutputStream(targetFile);
                PoiEnum.exportExcel(outputStream, params);
                params.put("fileKey", FileProcessUtis.uploadToS3(targetFile, fileName + ".xlsx", String.valueOf(params.get("userId"))));
                params.put("fileSize", targetFile.length() / 1024);
            }
        } catch (Exception ex) {
            logger.error("导出上传s3失败");
        } finally {
            deleteTargetFile(targetFile, outputStream);
        }
    }

    private void rebuildHeader(List<TableHeaderVo> header, List<AbstractExcelTitleVO> titleVoList, Set<String> titles) {
        int column = 0;
        for (TableHeaderVo tableHeaderVo : header) {
            column++;
            // 设置单元格格式
            if (CollectionUtil.isNullOrEmpty(tableHeaderVo.getChildren())) {
                // 单元文本格式：@
                LeafExcelTitleVO chnRelOutColumn1 = new LeafExcelTitleVO(tableHeaderVo.getTitle(), 8 * 640, true,
                        tableHeaderVo.getField(), "column" + column, CellType.STRING, "@");
                titles.add(tableHeaderVo.getField());
                titleVoList.add(chnRelOutColumn1);
            } else {
                BranchExcelTitleVO chnRelOutColumn8 = new BranchExcelTitleVO(tableHeaderVo.getTitle(), 13 * 640);
                titleVoList.add(chnRelOutColumn8);
                List<TableHeaderVo> children = tableHeaderVo.getChildren();
                // 区分BgCode不同导致增加了一行表头的问题---判断Children是否还有Children
                for (TableHeaderVo headers : children) {
                    AtomicInteger subColumn = new AtomicInteger();
                    // 单元文本格式：@
                    LeafExcelTitleVO chnRelOutColumn2 = new LeafExcelTitleVO(headers.getTitle(), 8 * 640, true,
                            headers.getField(), "column" + column, CellType.NUMERIC, "@");
                    titles.add(tableHeaderVo.getField());
                    if (null != headers.getChildren() && null != headers.getField() && headers.getField().isEmpty()) {
                        BranchExcelTitleVO chnRelOutColumn3 = new BranchExcelTitleVO(headers.getTitle(), 13 * 640);
                        List<AbstractExcelTitleVO> childExcelTitleVOs = new ArrayList<>();
                        headers.getChildren().forEach(subChild -> {
                            subColumn.getAndIncrement();
                            childExcelTitleVOs.add(new LeafExcelTitleVO(subChild.getTitle(), 8 * 640, true, subChild.getField(),
                                    "column" + subColumn, CellType.NUMERIC, DATA_FORMAT_DECIMALS));
                        });
                        chnRelOutColumn3.setChildExcelTitleList(childExcelTitleVOs);
                        chnRelOutColumn8.addChild(chnRelOutColumn3);
                    } else {
                        chnRelOutColumn8.addChild(chnRelOutColumn2);
                    }
                }
            }
        }
    }

    private void columnTitle(List<TableHeaderVo> topHeaders, Set<String> titles, List<AbstractExcelTitleVO> titleVoList) {
        int column = 0;
        for (TableHeaderVo tableHeaderVo : topHeaders) {
            column++;
            if (CollectionUtil.isNullOrEmpty(tableHeaderVo.getChildren())) {
                LeafExcelTitleVO chnRelOutColumn1 = new LeafExcelTitleVO(tableHeaderVo.getTitle(), 6 * 640, true,
                        tableHeaderVo.getField(), "column" + column, CellType.STRING, "@");
                titles.add(tableHeaderVo.getField());
                titleVoList.add(chnRelOutColumn1);
            } else {
                BranchExcelTitleVO chnRelOutColumn8 = new BranchExcelTitleVO(tableHeaderVo.getTitle(), 12 * 640);
                setDefineCellStyle(chnRelOutColumn8, tableHeaderVo.getTitle());
                titleVoList.add(chnRelOutColumn8);
                List<TableHeaderVo> children = tableHeaderVo.getChildren();
                for (TableHeaderVo headers : children) {
                    if (CollectionUtil.isNullOrEmpty(headers.getChildren())) {
                        LeafExcelTitleVO chnRelOutColumn2 = new LeafExcelTitleVO(headers.getTitle(), 6 * 640, true,
                                headers.getField(), "column" + column, CellType.STRING, "@");
                        setDefineCellStyle(chnRelOutColumn2, tableHeaderVo.getTitle());
                        chnRelOutColumn8.addChild(chnRelOutColumn2);
                        titles.add(headers.getField());
                    } else {
                        BranchExcelTitleVO chnRelOutColumn9 = new BranchExcelTitleVO(headers.getTitle(), 12 * 640);
                        setDefineCellStyle(chnRelOutColumn9, headers.getTitle());
                        chnRelOutColumn8.addChild(chnRelOutColumn9);
                        List<TableHeaderVo> children2 = headers.getChildren();
                        for (TableHeaderVo headers2 : children2) {
                            LeafExcelTitleVO chnRelOutColumn13 = new LeafExcelTitleVO(headers2.getTitle(), 6 * 640,
                                    true, headers2.getField(), "column" + column, CellType.STRING, "@");
                            chnRelOutColumn9.addChild(chnRelOutColumn13);
                            titles.add(headers2.getField());
                        }
                    }
                }
            }
        }
    }

    public void setDefineCellStyle(AbstractExcelTitleVO excelTitleVO, String title) {
        ExcelCellStyle forcastTitleCellStyle = new ExcelCellStyle();
        forcastTitleCellStyle.setFillColor(new Color(169, 208, 142));
        excelTitleVO.setDefineCellStyle(forcastTitleCellStyle);
    }

}
