/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.common.utils;

import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;


/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年02月27日
 */
class TimeUtilsTest {

    @Test
    void getEndTimeTest() {
        Assertions.assertEquals("20220131 23:59:59", TimeUtils.getEndTime("202201"));
    }

    @Test
    void getBeginTimeTest() {
        Assertions.assertEquals("20220101 00:00:00", TimeUtils.getBeginTime("202201"));
    }

    @Test
    void getPeriod() {
        Assertions.assertEquals("2023", TimeUtils.getPeriod(2022, 01));
    }

    @Test
    void getCurTime() {
        Assertions.assertNotNull(TimeUtils.getCurTime());
    }

    @Test
    void getBathNo() {
        Assertions.assertNotNull(TimeUtils.getBathNo());
    }

    @Test
    void getCurPeriod() {
        Assertions.assertNotNull(TimeUtils.getCurPeriod());
    }

    @Test
    void getDayOfMonth() {
        Assertions.assertNotNull(TimeUtils.getDayOfMonth());
    }

    @Test
    void getCurYear() {
        Assertions.assertNotNull(TimeUtils.getCurYear());
        Assertions.assertEquals(2022, TimeUtils.getCurYear(202202));
        Assertions.assertEquals(0, TimeUtils.getCurYear(20200201));
    }

    @Test
    void getCurMonth() {
        Assertions.assertNotNull(TimeUtils.getCurMonth(202201));
        Assertions.assertEquals(0, TimeUtils.getCurMonth(20220101));
    }

    @Test
    void getNowTime() {
        Assertions.assertNotNull(TimeUtils.getNowTime());
    }

    @Test
    void getNowHHTime() {
        Assertions.assertTrue(TimeUtils.getNowHHTime(10));
        Assertions.assertFalse(TimeUtils.getNowHHTime(21));
        Assertions.assertNotNull(TimeUtils.getNowHHTime(20));
    }

    @Test
    void getNowTimeAtMin() {
        Assertions.assertNotNull(TimeUtils.getNowTimeAtMin());
    }
}