/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.service;

import java.util.Map;

import com.huawei.it.fcst.profits.vo.request.LabelConfigRequest;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年04月12日
 */
public interface IFcstProfitsCommonService {
    /**
     * 校验结果返回值
     *
     * @param requestVO 请求参数
     * @return map{“isSubmit":false|true,"startDay":1,"endDay":8”}
     */
    Map<String ,Object> validateSumbit(LabelConfigRequest requestVO);

    /**
     * 校验结果返回值
     *
     * @return map{“isSubmit":false|true,"startDay":1,"endDay":8”}
     */
    Map<String ,Object> validateSumbit();
}
