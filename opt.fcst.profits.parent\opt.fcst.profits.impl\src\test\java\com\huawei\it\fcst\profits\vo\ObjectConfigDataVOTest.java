package com.huawei.it.fcst.profits.vo;

import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class ObjectConfigDataVOTest {
    ObjectConfigDataVO dataVO = new ObjectConfigDataVO();
    @Test
    void getLv1Code() {
        // run the test
        dataVO.setLv1Code("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getLv1Code());
    }

    @Test
    public void getL1Name() throws Exception {
        // run the test
        dataVO.setL1Name("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getL1Name());
    }

    @Test
    void getL2Name() {
        // run the test
        dataVO.setL2Name("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getL2Name());
    }

    @Test
    void getL3Name() {
        // run the test
        dataVO.setL3Name("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getL3Name());
    }
    @org.junit.Test
    public void testEquals() {
        // run the test
        dataVO.setLv1Name("Test");
        dataVO.setL3Name("Test");
        dataVO.setL1Name("Test");
        dataVO.setL2Name("Test");
        ObjectConfigDataVO testVo =new ObjectConfigDataVO();
        testVo.setLv1Name("Test");
        testVo.setL3Name("Test");
        testVo.setL1Name("Test");
        testVo.setL2Name("Test");
        // verify the results
        Assert.assertEquals(true, dataVO.equals(testVo));
    }
    @org.junit.Test
    public void testEquals1() {
        // verify the results
        Assert.assertEquals(true, dataVO.equals(dataVO));
    }
    @org.junit.Test
    public void testEquals2() {
        // verify the results
        Assert.assertEquals(false, dataVO.equals(null));
    }
    @org.junit.Test
    public void testHashCode() {
        // verify the results
        Assert.assertNotNull(dataVO.hashCode());
    }

}