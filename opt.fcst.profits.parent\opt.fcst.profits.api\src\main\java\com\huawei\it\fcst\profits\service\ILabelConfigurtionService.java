/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.CoaConfigDataVO;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.fcst.profits.vo.ObjectConfigDataVO;
import com.huawei.it.fcst.profits.vo.PlanComConfigDataVO;
import com.huawei.it.fcst.profits.vo.request.LabelConfigQueryRequest;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ISpartProfitingRelationService
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
public interface ILabelConfigurtionService {

    /**
     * 导入数据
     *
     * @return
     */
    CommonResult importCoaData(Attachment attachment, String roleId) throws CommonApplicationException;

    /**
     * 导入数据
     *
     * @return
     */
    CommonResult importPlanComData(Attachment attachment, String roleId)
        throws CommonApplicationException;

    /**
     * 导入数据
     *
     * @return
     */
    CommonResult importIctData(Attachment attachment, String roleId) throws CommonApplicationException;

    /**
     * 导出数据
     *
     * @return
     */
    void exportCoaData(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest)
        throws CommonApplicationException;

    void exportPlanComData(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest)
        throws CommonApplicationException;

    void exportIctData(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest)
        throws CommonApplicationException;

    void exportObjectData(HttpServletResponse response, LabelConfigQueryRequest labelConfigRequest)
        throws CommonApplicationException;

    /**
     * 模板下载
     *
     * @return
     */
    void templateDownload(HttpServletResponse response, String dataType) throws CommonApplicationException;

    PagedResult<CoaConfigDataVO> findCoaDataByPage(LabelConfigQueryRequest request) throws CommonApplicationException;

    PagedResult<PlanComConfigDataVO> findPlanComDataByPage(LabelConfigQueryRequest request)
        throws CommonApplicationException;

    PagedResult<HolisticViewConfigDataVO> findIctDataByPage(LabelConfigQueryRequest request)
        throws CommonApplicationException;

    List<HolisticViewConfigDataVO> getIctArticulationFlagNames(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<HolisticViewConfigDataVO> getIctL1Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<HolisticViewConfigDataVO> getIctLv3Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<HolisticViewConfigDataVO> getIctLv2Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<HolisticViewConfigDataVO> getIctLv1Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<PlanComConfigDataVO> getPlanComL2Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<PlanComConfigDataVO> getPlanComL1Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<PlanComConfigDataVO> getBusiLv4Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<PlanComConfigDataVO> getPlanComLv3Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<PlanComConfigDataVO> getPlanComLv2Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<PlanComConfigDataVO> getPlanComLv1Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<CoaConfigDataVO> getCoaCodes(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<CoaConfigDataVO> getCoaL2Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<CoaConfigDataVO> getCoaL1Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<CoaConfigDataVO> getCoaProductionNames(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    PagedResult<ObjectConfigDataVO> findObjectDataByPage(LabelConfigQueryRequest request)
        throws CommonApplicationException;

    CommonResult importObjectData(Attachment attachment, String roleId)
        throws CommonApplicationException;

    List<ObjectConfigDataVO> getObjectL3Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<ObjectConfigDataVO> getObjectL2Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<ObjectConfigDataVO> getObjectL1Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<ObjectConfigDataVO> getObjectLv1Names(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<ObjectConfigDataVO> getObjectUpdatedNames(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<CoaConfigDataVO> getCoaUpdatedNames(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<PlanComConfigDataVO> getPlanComUpdatedNames(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    List<HolisticViewConfigDataVO> getIctUpdatedNames(LabelConfigQueryRequest requestVO)
        throws CommonApplicationException;

    CommonResult submitPlanComData(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    CommonResult submitCoaData(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    CommonResult submitConfigIctData(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    CommonResult submitConfigObjectData(LabelConfigQueryRequest requestVO) throws CommonApplicationException;

    List<PlanComConfigDataVO> getPlanComProductionNames(LabelConfigQueryRequest requestVO) throws CommonApplicationException;;

    CommonResult importCoaFullData(Attachment file, String roleId) throws CommonApplicationException;

    CommonResult importPlanComFullData(Attachment file, String roleId) throws CommonApplicationException;

    CommonResult importIctFullData(Attachment file, String roleId) throws CommonApplicationException;

    CommonResult importObjectFullData(Attachment file, String roleId) throws CommonApplicationException;
}
