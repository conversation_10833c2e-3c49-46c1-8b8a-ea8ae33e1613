/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.vo.response;

import com.huawei.it.fcst.profits.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年02月28日
 */
public class QueryVersionResponseTest extends BaseVOCoverUtilsTest<QueryVersionResponse> {

    @Override
    protected Class getTClass() {
        return QueryVersionResponse.class;
    }

    @Test
    public void testBuilder() {
        // Setup
        // Run the test
        final QueryVersionResponse.QueryVersionResponseBuilder result = QueryVersionResponse.builder();

        // Verify the results
        Assert.assertNotNull(result);
    }
}