package com.huawei.it.fcst.profits.vo;

import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class HolisticViewConfigDataVOTest {
    HolisticViewConfigDataVO dataVO = new HolisticViewConfigDataVO();
    @Test
    void getLv1Code() {
        // run the test
        dataVO.setLv1Code("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getLv1Code());
    }

    @Test
    void getLv2Code() {
        // run the test
        dataVO.setLv2Code("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getLv2Code());
    }

    @Test
    void getLv2Name() {
        // run the test
        dataVO.setLv2Name("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getLv2Name());
    }

    @Test
    void getLv3Code() {
        // run the test
        dataVO.setLv3Code("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getLv3Code());
    }

    @Test
    void getLv3Name() {
        // run the test
        dataVO.setLv3Name("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getLv3Name());
    }

    @Test
    void getL1Name() {
        // run the test
        dataVO.setL1Name("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getL1Name());
    }

    @Test
    void getArticulationFlag() {
        // run the test
        dataVO.setArticulationFlag("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getArticulationFlag());
    }

    @Test
    void getIndustryType() {
        // run the test
        dataVO.setIndustryType("Test");
        // verify the results
        Assert.assertEquals("Test", dataVO.getIndustryType());
    }
    @org.junit.Test
    public void testEquals() {
        // run the test
        dataVO.setLv1Name("Test");
        dataVO.setLv3Name("Test");
        dataVO.setL1Name("Test");
        dataVO.setLv2Name("Test");
        HolisticViewConfigDataVO testVo =new HolisticViewConfigDataVO();
        testVo.setLv1Name("Test");
        testVo.setLv3Name("Test");
        testVo.setL1Name("Test");
        testVo.setLv2Name("Test");
        // verify the results
        Assert.assertEquals(true, dataVO.equals(testVo));
    }
    @org.junit.Test
    public void testEquals1() {
        // verify the results
        Assert.assertEquals(true, dataVO.equals(dataVO));
    }
    @org.junit.Test
    public void testEquals2() {
        // verify the results
        Assert.assertEquals(false, dataVO.equals(null));
    }
    @org.junit.Test
    public void testHashCode() {
        // verify the results
        Assert.assertNotNull(dataVO.hashCode());
    }
}