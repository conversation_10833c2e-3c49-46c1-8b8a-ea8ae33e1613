/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.dao;

import com.huawei.it.fcst.profits.vo.GroupAnalystsVO;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * IGroupAnalystsDao
 *
 **/
public interface IGroupAnalystsDao {

    List<GroupAnalystsVO> getDropDownBox(GroupAnalystsVO requestVO);

    List<GroupAnalystsVO> getGroupAnalystsPageInfo(GroupAnalystsVO request);

    List<GroupAnalystsVO> getLv1GroupAnalystsInfo(GroupAnalystsVO request);

    List<GroupAnalystsVO> getProdInfoList();

    void deleteDataVOs(@Param("paramMap") Map<String,String> paramMap);

    int deleteSubmitData();

    int deleteFullSubmitData();

    void batchInsertDataVOs(List<GroupAnalystsVO> importDataList);

    List<GroupAnalystsVO> getGroupAnalystsPageInfoList(GroupAnalystsVO request);

    int checkImportDataNum(Long userId);

    String checkImportFlag();

    void updateDataStatus(Long userId);
}
