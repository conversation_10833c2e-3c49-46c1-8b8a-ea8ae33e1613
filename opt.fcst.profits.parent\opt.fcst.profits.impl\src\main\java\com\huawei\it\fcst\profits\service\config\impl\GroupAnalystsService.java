/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.service.config.impl;

import static java.io.File.separator;

import com.huawei.apic.client.common.constant.Constants;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.LabelConfigEnum;
import com.huawei.it.fcst.profits.common.enums.ModuleEnum;
import com.huawei.it.fcst.profits.common.enums.SaveTypeEnum;
import com.huawei.it.fcst.profits.common.enums.TaskStatusEnum;
import com.huawei.it.fcst.profits.common.poi.ExcelUtil;
import com.huawei.it.fcst.profits.common.user.JalorUserTools;
import com.huawei.it.fcst.profits.common.utils.FileProcessUtis;
import com.huawei.it.fcst.profits.common.utils.ObjectUtils;
import com.huawei.it.fcst.profits.common.utils.UserHandle;
import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.common.vo.UploadInfoVO;
import com.huawei.it.fcst.profits.dao.IGroupAnalystsDao;
import com.huawei.it.fcst.profits.dao.IKrCpfLv1AggrDao;
import com.huawei.it.fcst.profits.dao.ILabelConfigDao;
import com.huawei.it.fcst.profits.dao.ILabelModifyDao;
import com.huawei.it.fcst.profits.dao.ILabelOperateLogDao;
import com.huawei.it.fcst.profits.service.IGroupAnalystsService;
import com.huawei.it.fcst.profits.service.IKrCpfLv1AggrService;
import com.huawei.it.fcst.profits.service.ILabelProductionService;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.utils.ExcelExportUtil;
import com.huawei.it.fcst.profits.utils.HeaderTitleUtils;
import com.huawei.it.fcst.profits.vo.DmFopRecordVO;
import com.huawei.it.fcst.profits.vo.GroupAnalystsImpVO;
import com.huawei.it.fcst.profits.vo.GroupAnalystsVO;
import com.huawei.it.fcst.profits.vo.HolisticViewConfigDataVO;
import com.huawei.it.fcst.profits.vo.LabelModifyVO;
import com.huawei.it.fcst.profits.vo.LabelOperateLogVO;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.core.util.PathUtil;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import com.huawei.it.jalor5.security.service.IUserQueryService;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.servlet.http.HttpServletResponse;

@Service
@Slf4j
public class GroupAnalystsService implements IGroupAnalystsService {

    private static final Logger logger = LoggerFactory.getLogger(GroupAnalystsService.class);

    @Inject
    private IGroupAnalystsDao groupAnalystsDao;

    @Autowired
    private JalorUserTools jalorUserTools;

    @Inject
    private ILabelProductionService labelProductionService;

    @Inject
    private ILabelConfigDao iLabelConfigDao;

    @Inject
    private ILabelModifyDao labelModifyDao;
    @Inject
    private IKrCpfLv1AggrDao iKrCpfLv1AggrDao;

    @Inject
    private ILabelOperateLogDao iOperateLogDao;

    @Inject
    private IUserQueryService userQueryService;

    @Autowired
    private IKrCpfLv1AggrService iKrCpfLv1AggrService;

    @Autowired
    private CoaConfigDataImporter coaConfigDataImporter;

    private PlatformTransactionManager transactionManager;

    @Autowired
    private ExcelUtil excelUtil;

    /**
     * 获取 下拉框值
     * <p>
     * 1. 按权限获取产业值
     * 2。 按照月度预测的方式排序
     *
     * @param requestVO
     * @return
     * @throws CommonApplicationException
     */
    @Override
    public List<GroupAnalystsVO> getDropDownBox(GroupAnalystsVO requestVO) throws CommonApplicationException {
        // 判断当前用户产业
        UserVO curUser = (UserVO) RequestContext.getCurrent(true).getUser();
        RoleVO currentRole = curUser.getCurrentRole();
        List<String> userProduction = getUserProduction(currentRole.getRoleId());
        // 无权限用户
        if (Objects.isNull(userProduction)) {
            return new ArrayList<>();
        }
        // 查询集合 应和 权限集合取交集
        if (CollectionUtil.isNullOrEmpty(requestVO.getLv1Codes())) {
            requestVO.setLv1Names(userProduction);
        } else {
            List<String> list = userProduction.stream().filter(requestVO.getLv1Names()::contains)
                .collect(Collectors.toList());
            requestVO.setLv1Names(list);
        }
        // 查询结果集合
        List<GroupAnalystsVO> dropDownBoxList = groupAnalystsDao.getDropDownBox(requestVO);
        // 查询Lv1需要排序
        if (StringUtils.isNotEmpty(requestVO.getVersionCode())) {
            // 需要按着 月度预测的 AI auto排序
            List<String> lv1Codes = dropDownBoxList.stream()
                .map(GroupAnalystsVO::getLv1Code)
                .distinct()
                .collect(Collectors.toList());
            ForecastsRequest forecastsRequest = setUpRequest(lv1Codes);
            // 完整排序集合
            List<String> recordOrders = iKrCpfLv1AggrDao.findRecordOrder(forecastsRequest);
            if (CollectionUtils.isEmpty(recordOrders)) {
                return dropDownBoxList;
            }
            Set<String> linkedSet = new LinkedHashSet<>(recordOrders);
            List<GroupAnalystsVO> finalResultList = new ArrayList<>();
            // 排序列表，按 lv1Code顺序 对应
            linkedSet.forEach(lv1 -> {
                GroupAnalystsVO groupAnalystsVO = Optional.ofNullable(
                        dropDownBoxList.stream().filter(data -> StringUtils.equals(lv1, data.getLv1Code())))
                    .get()
                    .findFirst()
                    .orElse(null);
                if (groupAnalystsVO != null) {
                    finalResultList.add(groupAnalystsVO);
                }
            });
            List<GroupAnalystsVO> finalOtherList = dropDownBoxList.stream()
                .filter(data -> !linkedSet.contains(data.getLv1Code()))
                .collect(Collectors.toList());
            finalResultList.addAll(finalOtherList);
            return finalResultList;
        } else {
            return dropDownBoxList;
        }
    }

    //  设置查询参数
    @NotNull
    private ForecastsRequest setUpRequest(List<String> lv1Codes) throws CommonApplicationException {
        // 构造请求Request
        ForecastsRequest forecastsRequest = new ForecastsRequest();
        // 设置默认查询条件-产业
        forecastsRequest.setLv1s(lv1Codes);
        // 设置默认查询条件-产业
        forecastsRequest.setFcstType(CommonConstant.FCST_YEAR_STEP);
        // 设置默认查询条件 - 预测种类
        forecastsRequest.setPredictionType("MONTH");
        // 设置默认查询条件-基期
        forecastsRequest.setPeriodId(iKrCpfLv1AggrDao.getVersions().get(0));
        // 设置默认查询条件-区域
        forecastsRequest.setOverseaDesc("全球");
        // 设置默认查询条件-BG
        forecastsRequest.setBgCodes(Arrays.asList("PROD0002"));
        // 设置默认查询条件-queryType
        forecastsRequest.setQueryType("F");
        // 设置默认查询条件-fcstStep
        forecastsRequest.setFcstStep("MONTH_YEAR_YTD");
        forecastsRequest.setOrderColum("equipRevAfter");
        // 设置默认查询条件-期次
        forecastsRequest.setPhaseDate(iKrCpfLv1AggrService.getSopInfo(forecastsRequest).get(0).getPhaseDate());
        forecastsRequest.buildFcstStepQueryParam();
        return forecastsRequest;
    }

    @Override
    public List<GroupAnalystsVO> getGroupAnalystsPageInfo(GroupAnalystsVO request) {
        UserVO curUser = (UserVO) RequestContext.getCurrent(true).getUser();
        RoleVO currentRole = curUser.getCurrentRole();
        // 判断当前用户产业
        List<String> userProduction = getUserProduction(currentRole.getRoleId());
        // 无权限用户
        if (Objects.isNull(userProduction)) {
            return new ArrayList<>();
        }
        // 查询集合 应和 权限集合取交集
        if (CollectionUtil.isNullOrEmpty(request.getLv1Codes())) {
            request.setLv1Names(userProduction);
        } else {
            List<String> list = userProduction.stream()
                .filter(request.getLv1Names()::contains)
                .collect(Collectors.toList());
            request.setLv1Names(list);
        }
        // 查询LV2层级的数据，并根据LV1的名称分组
        List<GroupAnalystsVO> groupAnalystsInfoList = groupAnalystsDao.getGroupAnalystsPageInfo(request);
        setUserName(groupAnalystsInfoList);
        Map<String, List<GroupAnalystsVO>> collect = groupAnalystsInfoList.stream()
                .filter(vo -> StringUtils.isNotBlank(vo.getLv2Name()))
                .collect(Collectors.groupingBy(GroupAnalystsVO::getLv1Name));
        // 查询LV1层级的数据
        List<GroupAnalystsVO> lv1GroupAnalystsInfo = groupAnalystsDao.getLv1GroupAnalystsInfo(request);
        setUserName(lv1GroupAnalystsInfo);
        boolean fullImportFlag = labelProductionService.checkUserPermissionAllProd(UserHandle.getRoleId());
        lv1GroupAnalystsInfo.stream().forEach(item -> {
            item.setChildren(collect.get(item.getLv1Name()));
            item.setFullImportFlag(fullImportFlag);
        });
        return lv1GroupAnalystsInfo;
    }

    private void setUserName(List<? extends GroupAnalystsVO> userLists) {
        Set<Long> UserInfos = userLists.stream().filter(groupAnalystsVO -> groupAnalystsVO.getLastUpdatedBy() != null)
                .map(k -> k.getLastUpdatedBy()).collect(Collectors.toSet());
        Map<Long, String> userInfoSet = getUserInfoSet(UserInfos);
        userLists.remove(null);
        userLists.stream().forEach(vo -> {
            if (!ObjectUtil.isEmpty(vo)&&!ObjectUtil.isEmpty(vo.getLastUpdatedBy())) {
                vo.setLastUpdatedByName(userInfoSet.get(vo.getLastUpdatedBy()));
            }
        });
    }

    private Map<Long, String> getUserInfoSet(Set<Long> uids) {
        Map<Long, String> usrUuidInfos = new HashMap<>();
        if (!CollectionUtils.isEmpty(uids)) {
            List<UserVO> users = userQueryService.findUsersByMultiId(uids.toArray(new Long[0]));
            usrUuidInfos = users.stream().collect(Collectors.toMap(UserVO::getUserId, UserVO::getUserCN));
        }
        usrUuidInfos.put(-1L, "system");
        usrUuidInfos.put(0L, "system");
        return usrUuidInfos;
    }

    /**
     * 模板下载
     *
     * @param response
     * @throws CommonApplicationException
     */
    @Override
    public void groupAnalystsTemplateDownload(HttpServletResponse response) throws CommonApplicationException {
        Workbook wb = null;
        InputStream inputStream = null;
        try {
            Resource classPathResource = new ClassPathResource("template/groupAnalystsTemplate.xlsx");
            inputStream = classPathResource.getInputStream();
            if (!Objects.isNull(inputStream)) {
                wb = WorkbookFactory.create(inputStream);
                response.setContentType("application/vnd.ms-excel;charset=UTF-8");
                response.setHeader("Content-Disposition",
                    "attachment;filename=\"" + URLEncoder.encode(CommonConstant.GROUP_ANALYSTS_TEMPLATE,
                        Constants.UTF_8) + "\"");
                wb.write(response.getOutputStream());
            }
        } catch (Exception ex) {
            logger.error("模板文件不存在");
            throw new CommonApplicationException("模板文件不存在");
        } finally {
            ExcelUtil.closeStreamAndWorkbook(inputStream, wb);
        }
    }

    @Override
    public LabelOperateLogVO getOperateRecordLogStatus(String objectId) {
        return iOperateLogDao.getOperateRecordLogStatus(objectId);
    }

    // TODO 全量导入权限校验
    @Override
    public CommonResult fullImportGroupAnalystsInfo(Attachment attachment) throws CommonApplicationException {
        if (null == attachment) {
            throw new CommonApplicationException("文件流为空");
        }
        File tempFile = new File(
            PathUtil.getPathByDay(ModuleEnum.MODULE_GROUP_ANALYS.getCode()) + separator + TimeUtils.getNowTime() + ".xlsx");
        try {
            FileUtils.copyInputStreamToFile(attachment.getDataHandler().getInputStream(), tempFile);
            // 校验导入的Excel文件格式和文件大小
            excelUtil.verifyExcelFile(attachment, ModuleEnum.MODULE_GROUP_ANALYS.getDesc(), tempFile);
            // 模板信息检查
            if (checkError(tempFile)) {
                // 保存请求上下文信息
                IRequestContext current = RequestContextManager.getCurrent();
                // 封装用户以及保存文件的相关信息
                UploadInfoVO uploadInfoVO = setUserUploadInfo(attachment,tempFile);
                // 设置时间
                uploadInfoVO.setStartTime(TimeUtils.getCurTime());
                // 设置任务刷新状态
                LabelOperateLogVO labelOperateLogVO = LabelOperateLogVO.builder()
                    .isSkip(false)
                    .status("START")
                    .objectId(ObjectUtils.genRanCode())
                    .methodName(ModuleEnum.MODULE_GROUP_ANALYS.getCode() + "FullImport")
                    .userId(UserHandle.getUserId())
                    .build();
                iOperateLogDao.insertLongInfo(labelOperateLogVO);
                // 生成导入信息
                CompletableFuture.runAsync(() -> {
                    asyncImportGroupAnalystsInfo(uploadInfoVO, current, labelOperateLogVO, tempFile, true);
                });
                return CommonResult.success(labelOperateLogVO);
            } else {
                excelUtil.checkFile(attachment, ModuleEnum.MODULE_GROUP_ANALYS.getDesc(), "输入文件模板错误或为空文件",tempFile);
                throw new CommonApplicationException("输入文件模板错误或为空文件");
            }
        }catch (IOException e){
            throw new CommonApplicationException("输入文件模板错误或为空文件");
        }
    }

    @Override
    public CommonResult importGroupAnalystsInfo(Attachment attachment) throws CommonApplicationException {
        if (null == attachment) {
            throw new CommonApplicationException("文件流为空");
        }
        File tempFile = new File(PathUtil.getPathByDay(ModuleEnum.MODULE_GROUP_ANALYS.getCode()) + separator + TimeUtils.getNowTime()
                + ".xlsx");
        try {
            FileUtils.copyInputStreamToFile(attachment.getDataHandler().getInputStream(), tempFile);
            // 校验导入的Excel文件格式和文件大小
            excelUtil.verifyExcelFile(attachment, ModuleEnum.MODULE_GROUP_ANALYS.getDesc(), tempFile);
            // 模板信息检查
            if (checkError(tempFile)) {
                // 保存请求上下文信息
                IRequestContext current = RequestContextManager.getCurrent();
                // 封装用户以及保存文件的相关信息
                UploadInfoVO uploadInfoVO = setUserUploadInfo(attachment, tempFile);
                // 设置时间
                uploadInfoVO.setStartTime(TimeUtils.getCurTime());
                // 设置任务刷新状态
                LabelOperateLogVO labelOperateLogVO = LabelOperateLogVO.builder()
                    .isSkip(false)
                    .status("START")
                    .objectId(ObjectUtils.genRanCode())
                    .methodName(ModuleEnum.MODULE_GROUP_ANALYS.getCode() + "Import")
                    .userId(UserHandle.getUserId())
                    .build();
                iOperateLogDao.insertLongInfo(labelOperateLogVO);
                // 生成导入信息
                CompletableFuture.runAsync(() -> {
                    asyncImportGroupAnalystsInfo(uploadInfoVO, current, labelOperateLogVO, tempFile, false);
                });
                return CommonResult.success(labelOperateLogVO);
            } else {
                excelUtil.checkFile(attachment, ModuleEnum.MODULE_GROUP_ANALYS.getDesc(), "输入文件模板错误或为空文件", tempFile);
                throw new CommonApplicationException("输入文件模板错误或为空文件");
            }
        } catch (IOException e) {
            throw new CommonApplicationException("输入文件模板错误或为空文件");
        }
    }

    @Override
    public CommonResult exportGroupAnalystsInfo(HttpServletResponse response, GroupAnalystsVO request)
        throws CommonApplicationException {
        log.info(">>>Begin exportManufactureTreeData");
        long startTime = System.currentTimeMillis();
        UserVO curUser = (UserVO) RequestContext.getCurrent(true).getUser();
        RoleVO currentRole = curUser.getCurrentRole();
        // 判断当前用户产业
        List<String> userProduction = getUserProduction(currentRole.getRoleId());
        // 无权限用户
        if (Objects.isNull(userProduction)) {
            return CommonResult.success("无数据可导出");
        }
        // 查询集合 应和 权限集合取交集
        if (CollectionUtil.isNullOrEmpty(request.getLv1Codes())) {
            request.setLv1Names(userProduction);
        } else {
            List<String> list = userProduction.stream()
                .filter(request.getLv1Names()::contains)
                .collect(Collectors.toList());
            request.setLv1Names(list);
        }
        // 查询submit的集团分析师预测结果表数据
        request.setStatus("SUBMIT");
        List<GroupAnalystsVO> dataList = groupAnalystsDao.getGroupAnalystsPageInfoList(request);
        if (CollectionUtils.isEmpty(dataList)) {
            return CommonResult.success("无数据可导出");
        }
        // 处理设备收入（单位为亿）和制毛率（百分比的形式）与页面展示保持一致
        dataList.stream().forEach(item -> {
            item.setEquipRevAmt(String.format(Locale.ROOT, "%.1f", Double.parseDouble(item.getEquipRevAmt()) / 100000000));
            item.setMgpRatio(String.format(Locale.ROOT, "%.1f", Double.parseDouble(item.getMgpRatio()) * 100).concat("%"));
        });
        // 指定模板路径
        String exportTemplate = CommonConstant.GROUP_ANALYSTS_EXPORT_TEMPLATE_PATH;
        XSSFWorkbook workbook = null;
        try {
            workbook = excelUtil.getWorkbookByTemplate(exportTemplate);

            // 导出模板对应的数据
            Sheet sheet = workbook.getSheetAt(0);
            // 填充Sheet数据
            new ExcelExportUtil<GroupAnalystsVO>(1, 1, GroupAnalystsVO.class).fillSheetData(sheet, dataList);
            String fileName = "配置管理-集团分析师预测结果_".concat(String.valueOf(System.currentTimeMillis()));
            // Excel文件下载到浏览器
            ExcelExportUtil.downloadExcel(workbook, fileName, response);
            // 插入数据，上传文件
            DmFopRecordVO dmFopRecordVO = excelUtil.uploadExportExcel(workbook, dataList.size(), fileName);
            // 插入数据
            dmFopRecordVO.setPageModule(CommonConstant.LABEL_CONFIG_MODULE_NAME9);
            dmFopRecordVO.setRecSts("OK");
            // 个人中心信息记录
            excelUtil.statisticsExportRecord(dmFopRecordVO);
        } catch (Exception ex) {
            logger.error(CommonConstant.STREAM_IN_OPEN_FAILLED);
        }
        if (workbook != null) {
            try {
                workbook.close();
            } catch (IOException e) {
                throw new CommonApplicationException("文件输入流关闭失败");
            }
        }
        log.info("End ConfigManagementService::exportEditItem and total time:{}",
            (System.currentTimeMillis() - startTime));
        return CommonResult.success("导出完成");
    }

    @Override
    public CommonResult submit() throws ApplicationException {
        if(groupAnalystsDao.checkImportDataNum(UserHandle.getUserId()) == 0){
            return CommonResult.failed("您没有修改任何数据，无法提交");
        }else {
            /**
             *  判断用户执行的是全量导入还是普通导入
             *  全量导入的数据和一般导入的数据，区别在remark字段，全量导入会有remark=Full_Import的标识
             */
            String versionCode = groupAnalystsDao.checkImportFlag();
            if (StringUtils.isNotBlank(versionCode)) {
                // 全量导入，删除全量导入版本下所有submit的数据
                groupAnalystsDao.deleteFullSubmitData();
            } else {
                // 非全量导入，删除与未提交数据的【版本+LV1名称】相同的已提交数据
                groupAnalystsDao.deleteSubmitData();
            }
            // 生成我的修改记录
            generateModifyRecord();
            // 更新未提交数据为已提交的数据
            groupAnalystsDao.updateDataStatus(UserHandle.getUserId());
            return CommonResult.success("提交完成");
        }
    }

    private void generateModifyRecord() throws ApplicationException {
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("fileName", "集团分析师预测结果-修改记录-" + TimeUtils.getNowTime());
        paramMap.put("dataType", "COA");
        paramMap.put("moduleName", CommonConstant.LABEL_CONFIG_MODULE_NAME9);
        paramMap.put("userId", String.valueOf(UserHandle.getUserId()));
        // 查询我修改的数据
        GroupAnalystsVO groupAnalystsVO = new GroupAnalystsVO();
        groupAnalystsVO.setCreatedBy(UserHandle.getUserId());
        groupAnalystsVO.setStatus("IMPORT");
        List<GroupAnalystsVO> dataList = groupAnalystsDao.getGroupAnalystsPageInfoList(groupAnalystsVO);
        setUserName(dataList);
        // 上传数据到服务，随后生成 我的修改 记录
        coaConfigDataImporter.buildExportStreamAndUpload(paramMap, String.valueOf(UserHandle.getUserId()),
                dataList, HeaderTitleUtils.creatMyEditedDataHeaderList(LabelConfigEnum.GROUP_ANALYST.getCode()));
        // 生成我的修改记录
        buildModifyRecord(paramMap);
    }

    /**
     * 生成我的修改记录
     *
     * @param params  params
     */
    private void buildModifyRecord(Map<String, Object> params) {
        LabelModifyVO labelModifyVO = new LabelModifyVO();
        labelModifyVO.setId(UUID.randomUUID().toString());
        labelModifyVO.setPageModule(String.valueOf(params.get("moduleName")));
        labelModifyVO.setCreatedBy(String.valueOf(UserHandle.getUserId()));
        labelModifyVO.setLastUpdatedBy(String.valueOf(UserHandle.getUserId()));
        labelModifyVO.setCreationDate(TimeUtils.getCurTime());
        labelModifyVO.setLastUpdateDate(TimeUtils.getCurTime());
        labelModifyVO.setStatus(SaveTypeEnum.SAVE.getCode());
        labelModifyVO.setFileName(String.valueOf(params.get("fileName")));
        labelModifyVO.setFileSourceKey(String.valueOf(params.get("fileKey")));
        labelModifyDao.createList(Arrays.asList(labelModifyVO));
    }

    private void asyncImportGroupAnalystsInfo(UploadInfoVO uploadInfoVO, IRequestContext current,
        LabelOperateLogVO labelOperateLogVO, File file, boolean flag) {
        log.info(">>>Begin asyncImportGroupAnalystsInfo");
        long startTime = System.currentTimeMillis();    // 记录服务时间
        RequestContextManager.setCurrent(current);      // 设置相关context
        TransactionStatus status = getTransactionStatus();
        Long usrId = UserHandle.getUserId();
        // 指定模板路径
        String exportTemplate = CommonConstant.GROUP_ANALYSTS_IMPORT_ERROR_TEMPLATE_PATH;
        XSSFWorkbook workbook = null;
        InputStream fileStream = null;
        try {
            Map<String, Object> context = setContextMap(usrId);
            context.put("flag", String.valueOf(flag));
            // 组装校验所需的集合信息
            List<GroupAnalystsVO> prodInfoList = groupAnalystsDao.getProdInfoList();
            context.put("prodInfoLv1Map", prodInfoList.stream().distinct()
                .collect(Collectors.toMap(GroupAnalystsVO::getLv1Name, GroupAnalystsVO::getLv1Code, (oldData, newData) -> newData)));
            context.put("prodInfoLv2Map", prodInfoList.stream().distinct()
                .collect(Collectors.toMap(GroupAnalystsVO::getLv2Name, GroupAnalystsVO::getLv2Code, (oldData, newData) -> newData)));
            context.put("lv1DimessionSet",jalorUserTools.getRolePermission(UserHandle.getRoleId()).getLv1DimensionSet());
            GroupAnalystsImpListener groupAnalystsImpListener = new GroupAnalystsImpListener(groupAnalystsDao, context);
            // 获取excel传入的文件流
            fileStream = new FileInputStream(file);
            EasyExcel.read(fileStream, GroupAnalystsVO.class, groupAnalystsImpListener)
                .excelType(ExcelTypeEnum.XLSX).doReadAll();
            // 将导入的数据回写到个人中心-我的导入
            workbook = excelUtil.getWorkbookByTemplate(exportTemplate);
            Sheet sheet = workbook.getSheetAt(0);
            // 填充Sheet数据
            List<GroupAnalystsImpVO> importDataList = (List<GroupAnalystsImpVO>) context.get("importDataList");
            new ExcelExportUtil<GroupAnalystsImpVO>(1, 1, GroupAnalystsImpVO.class)
                    .fillSheetData(sheet, importDataList);
            String fileName = uploadInfoVO.getFileName().concat(String.valueOf(System.currentTimeMillis()));
            // 插入数据，上传文件
            Integer totalNum = Integer.valueOf(String.valueOf(context.get("totalNum")));
            DmFopRecordVO recordVO = excelUtil.uploadExportExcel(workbook, totalNum, fileName);
            recordVO.setPageModule(CommonConstant.LABEL_CONFIG_MODULE_NAME9);
            if (!context.containsKey("errorMsg")) {
                // 导入成功信息记录
                personCenterDataInsert(recordVO, uploadInfoVO, true, totalNum);
                updateDataRefreshStatusVO(labelOperateLogVO, TaskStatusEnum.TASK_SUCCESS.getValue());
                transactionManager.commit(status);
            } else {
                processErrorInfo(context, recordVO);
                personCenterDataInsert(recordVO, uploadInfoVO, false, totalNum);
                throw new CommonApplicationException("输入值不合法");
            }
        } catch (Exception ex) {
            transactionManager.rollback(status);
            log.error("AsyncConfigService::asyncImportRoutePriceData Exception:", ex);
            updateDataRefreshStatusVO(labelOperateLogVO, TaskStatusEnum.TASK_FAIL.getValue());    // 更新任务状态表
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException ex) {
                    log.error("close workbook error:{}", CommonConstant.STREAM_IN_CLOSE_FAILLED);
                }
            }
            if (fileStream != null) {
                try {
                    fileStream.close();
                } catch (IOException ex) {
                    log.error("close fileStream error:{}", CommonConstant.STREAM_IN_CLOSE_FAILLED);
                }
            }
            if (!file.exists()) {
                log.error("template file not exist!");
            } else {
                file.delete();
            }
            // 清除公服缓存
            RequestContextManager.removeCurrent();
            log.info("End asyncImportRoutePriceData and total time:{}", (System.currentTimeMillis() - startTime));
        }
    }

    private void updateDataRefreshStatusVO(LabelOperateLogVO labelOperateLogVO, String status) {
        labelOperateLogVO.setStatus(status);
        iOperateLogDao.updateUpsertNum(labelOperateLogVO);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void personCenterDataInsert(DmFopRecordVO recordVO, UploadInfoVO uploadInfoVO, boolean flag,
        Integer context) {
        excelUtil.insertImportExcelRecord(recordVO, uploadInfoVO, flag, context);
    }

    @NotNull
    private DmFopRecordVO getFoiRecordVO(UploadInfoVO uploadInfoVO, Map<String, Object> context, File file,
        String fileSourceKey) {
        DmFopRecordVO recordVO = new DmFopRecordVO();
        recordVO.setFileSize(String.valueOf(file.length() / 1024));
        recordVO.setFileSourceKey(fileSourceKey);
        recordVO.setRecordNum(Integer.valueOf(String.valueOf(context.get("totalNum"))));
        recordVO.setFileName(uploadInfoVO.getFileName());
        recordVO.setPageModule(CommonConstant.LABEL_CONFIG_MODULE_NAME9);
        return recordVO;
    }

    private void processErrorInfo(Map<String, Object> context, DmFopRecordVO recordVO) {
        // 文件错误
        String errMsg = String.valueOf(context.get("errorMsg"));
        if (errMsg.getBytes(StandardCharsets.UTF_8).length > 2000) {
            errMsg = errMsg.substring(0, 666);
        }
        recordVO.setFileErrorKey(recordVO.getFileSourceKey());
        recordVO.setExceptionFeedback(errMsg);
        recordVO.setFileSourceKey("");
    }

    @NotNull
    private Map<String, Object> setContextMap(Long userId) {
        Map<String, Object> context = new HashMap<>();  // 导入所需上下文信息
        context.put("totalNum", 0); // 总处理数
        context.put("batchNum", 0); // 总批次数
        context.put("userId", userId);
        return context;
    }

    @NotNull
    private TransactionStatus getTransactionStatus() {
        // 事务定义
        DefaultTransactionDefinition transDef = new DefaultTransactionDefinition();
        // 设置事务传播级别
        transDef.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        transDef.setName("Excel Import");
        return getTransactionStatus(transDef);
    }

    private TransactionStatus getTransactionStatus(DefaultTransactionDefinition transDef) {
        TransactionStatus transStatus = null;
        this.transactionManager = getTransactionManager();
        transStatus = transactionManager.getTransaction(transDef);
        return transStatus;
    }

    /**
     * 获取平台事务管理器
     *
     * @return result
     * <AUTHOR>
     * @since Mar 23, 2012
     */
    private PlatformTransactionManager getTransactionManager() {
        return Jalor.getContext().getBean("txManager", PlatformTransactionManager.class);
    }

    private boolean checkError(File attachment) {
        ByteArrayOutputStream byteArrayOutputStream = null;
        InputStream inputStream = null;
        XSSFWorkbook workbook = null;
        try {
            byteArrayOutputStream = excelUtil.putInputStreamCacher(Files.newInputStream(attachment.toPath()));
            inputStream = excelUtil.getInputStream(byteArrayOutputStream);
            workbook = new XSSFWorkbook(inputStream);
            if (Objects.isNull(workbook)) {
                return false;
            }
            // 表头为2行
            if (workbook.getSheetAt(0).getLastRowNum() < 1) {
                return false;
            }
        } catch (IOException e) {
            log.error("steam handle error");
        } finally {
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (IOException e) {
                    log.error("steam close error!");
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("steam close error!");
                }
            }
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("steam close error!");
                }
            }
        }
        return true;
    }

    private UploadInfoVO setUserUploadInfo(Attachment attachment,File file) {
        Map<String, Object> params = new HashMap<>();
        Map<String, String> resultMap = new HashMap<>();
        params.put("module", ModuleEnum.MODULE_GROUP_ANALYS.getDesc());
        FileProcessUtis.getFileName(attachment, resultMap);
        Long userId = UserHandle.getUserId();
        // 设置上传参数
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileSize(file.length()/1024);
        uploadInfoVO.setFileName(resultMap.get("prefix"));
        uploadInfoVO.setParams(params);
        uploadInfoVO.setUserId(userId);
        uploadInfoVO.setOptFlag(false);
        uploadInfoVO.setCheckList(null);
        uploadInfoVO.setSuffix(resultMap.get("suffix"));
        return uploadInfoVO;
    }

    public List<String> getUserProduction(int roleId) {
        DataPermissionsVO rightVO = jalorUserTools.getRolePermission(roleId);
        log.info("GroupAnalystsService getUserProduction: " + roleId);
        Map<String, HolisticViewConfigDataVO> hierarchySortInfoLv1 = iLabelConfigDao.getHierarchySortInfoLv1();
        Map<String, String> infos = hierarchySortInfoLv1.entrySet()
            .stream()
            .collect(Collectors.toMap(k -> k.getKey(), v -> v.getValue().getLv1Name()));
        return CommUtils.checkUserPermissions(rightVO, infos);
    }
}
