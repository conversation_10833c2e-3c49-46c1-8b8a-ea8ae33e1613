package com.huawei.it.fcst.profits.vo;

import com.huawei.it.fcst.profits.common.utils.biz.TimeUtils;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.sql.Timestamp;

class LabelConfigVoTest {
    LabelConfigVo dataVO = new LabelConfigVo();
    @Test
    void getRemark() {
        // run the test
        dataVO.setRemark("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getRemark());
    }

    @Test
    void getLv1Name() {
        // run the test
        dataVO.setLv1Name("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getLv1Name());
    }

    @Test
    void getCreationDate() {
        // run the test
        Timestamp curTime = TimeUtils.getCurTime();
        dataVO.setCreationDate(curTime);
        // verify the results
        Assert.assertEquals(curTime, dataVO.getCreationDate());
    }

    @Test
    void getCreatedBy() {
        // run the test
        dataVO.setCreatedBy("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getCreatedBy());
    }

    @Test
    void getCreatedByName() {
        // run the test
        dataVO.setCreatedByName("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getCreatedByName());
    }

    @Test
    void getLastUpdateDate() {
        // run the test
        Timestamp curTime = TimeUtils.getCurTime();
        dataVO.setLastUpdateDate(curTime);
        // verify the results
        Assert.assertEquals(curTime, dataVO.getLastUpdateDate());
    }

    @Test
    void getLastUpdatedBy() {
        // run the test
        dataVO.setLastUpdatedBy("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getLastUpdatedBy());
    }

    @Test
    void getLastUpdatedByName() {
        // run the test
        dataVO.setLastUpdatedByName("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getLastUpdatedByName());
    }

    @Test
    void getDelFlag() {
        // run the test
        dataVO.setDelFlag("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getDelFlag());
    }

    @Test
    void getErrorMsg() {
        // run the test
        dataVO.setErrorMsg("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getErrorMsg());
    }

    @Test
    void getStatus() {
        // run the test
        dataVO.setStatus("test");
        // verify the results
        Assert.assertEquals("test", dataVO.getStatus());
    }
}