package com.huawei.it.fcst.profits.vo.request;

import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ForecastsParamWrapperTest {

    private ForecastsParamWrapper forecastsParamWrapperUnderTest;

    @BeforeEach
    void setUp() {
        forecastsParamWrapperUnderTest = new ForecastsParamWrapper();
    }
    @Test
    public void test_MONTHLY_FORECAST_Y(){
        String str = "{\"periodId\":202311,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"MONTH_YEAR_YTD\",\"phaseDate\":\"20231030\",\"predictionType\":\"MONTH\",\"roleId\":13086,\"overseaDesc\":\"全球\",\"lv2Code\":\"101824\",\"l1Name\":\"室内数字化\",\"tabGraphType\":\"Y\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        ForecastsParamWrapper.ForceastParamEnum.MONTHLY_FORECAST_Y.beforeBuildParam(request);
        Assertions.assertEquals(2023, request.getCurYear());
    }

    @Test
    public void test_MONTHLY_FORECAST_H(){
        String str = "{\"periodId\":202311,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"MONTH_YEAR_YTD\",\"phaseDate\":\"20231030\",\"predictionType\":\"MONTH\",\"roleId\":13086,\"overseaDesc\":\"全球\",\"lv2Code\":\"101824\",\"l1Name\":\"室内数字化\",\"tabGraphType\":\"H\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        ForecastsParamWrapper.ForceastParamEnum.MONTHLY_FORECAST_H.beforeBuildParam(request);
        Assertions.assertEquals(2023, request.getCurYear());
    }

    @Test
    public void test_MONTHLY_FORECAST_Q(){
        String str = "{\"periodId\":202311,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"MONTH_YEAR_YTD\",\"phaseDate\":\"20231030\",\"predictionType\":\"MONTH\",\"roleId\":13086,\"overseaDesc\":\"全球\",\"lv2Code\":\"101824\",\"l1Name\":\"室内数字化\",\"tabGraphType\":\"Q\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        ForecastsParamWrapper.ForceastParamEnum.MONTHLY_FORECAST_Q.beforeBuildParam(request);
        Assertions.assertEquals(2023, request.getCurYear());
    }

    @Test
    public void test_MONTHLY_FORECAST_M(){
        String str = "{\"periodId\":202311,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"MONTH_YEAR_YTD\",\"phaseDate\":\"20231030\",\"predictionType\":\"MONTH\",\"roleId\":13086,\"overseaDesc\":\"全球\",\"lv2Code\":\"101824\",\"l1Name\":\"室内数字化\",\"tabGraphType\":\"M\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        ForecastsParamWrapper.ForceastParamEnum.MONTHLY_FORECAST_M.beforeBuildParam(request);
        Assertions.assertEquals(2023, request.getCurYear());
    }
    @Test
    public void test_ANNUAL_BUDGET_Y(){
        String str = "{\"periodId\":202311,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"MONTH_YEAR_YTD\",\"phaseDate\":\"20231030\",\"predictionType\":\"YEAR\",\"roleId\":13086,\"overseaDesc\":\"全球\",\"lv2Code\":\"101824\",\"l1Name\":\"室内数字化\",\"tabGraphType\":\"Y\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        ForecastsParamWrapper.ForceastParamEnum.ANNUAL_BUDGET_Y.beforeBuildParam(request);
        Assertions.assertEquals(2023, request.getCurYear());
    }
    @Test
    public void test_ANNUAL_BUDGET_M(){
        String str = "{\"periodId\":202311,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"MONTH_YEAR_YTD\",\"phaseDate\":\"20231030\",\"predictionType\":\"YEAR\",\"roleId\":13086,\"overseaDesc\":\"全球\",\"lv2Code\":\"101824\",\"l1Name\":\"室内数字化\",\"tabGraphType\":\"M\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        ForecastsParamWrapper.ForceastParamEnum.ANNUAL_BUDGET_M.beforeBuildParam(request);
        Assertions.assertEquals(2023, request.getCurYear());
    }

    @Test
    public void testGetSplicedPredictionTabGraphValue(){
        String str = "{\"periodId\":202311,\"lv1Codes\":[\"100001\"],\"bgCode\":[\"PROD0002\"],\"fcstStep\":\"年度\",\"fcstType\":\"MONTH_YEAR_YTD\",\"phaseDate\":\"20231030\",\"predictionType\":\"YEAR\",\"roleId\":13086,\"overseaDesc\":\"全球\",\"lv2Code\":\"101824\",\"l1Name\":\"室内数字化\",\"tabGraphType\":\"M\"}";
        ForecastsRequest request = JSONObject.parseObject(str, ForecastsRequest.class);
        Assertions.assertEquals("YEAR_M", ForecastsParamWrapper.getSplicedPredictionTabGraphValue(request));
    }

    @Test
    public void testGetByCode(){
        ForecastsParamWrapper.ForceastParamEnum yearM = ForecastsParamWrapper.ForceastParamEnum.getByCode("YEAR_M");
        Assertions.assertEquals("YEAR_M", yearM.getCode());
    }

    @Test
    public void testGetByCodeNull(){
        ForecastsParamWrapper.ForceastParamEnum yearM = ForecastsParamWrapper.ForceastParamEnum.getByCode("YEAR_M_TEST");
        Assertions.assertEquals(null, yearM);
    }
}
