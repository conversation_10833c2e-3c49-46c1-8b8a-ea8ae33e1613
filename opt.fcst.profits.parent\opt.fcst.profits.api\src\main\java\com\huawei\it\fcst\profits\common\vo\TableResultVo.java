/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * TableHeaderVo Class
 *
 * <AUTHOR>
 * @since 2022-09-28
 */
@Getter
@Setter
@NoArgsConstructor
public class TableResultVo {
    /**
     * 表头
     */
    private List<TableHeaderVo> header;

    /**
     * 表中数据
     */
    private List<Map> list;
}
