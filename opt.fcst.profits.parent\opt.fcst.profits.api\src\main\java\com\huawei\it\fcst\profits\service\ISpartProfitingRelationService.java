/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service;

import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.vo.LabelCountVO;
import com.huawei.it.fcst.profits.vo.LabelOperateLogVO;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.LabelConfigRequest;
import com.huawei.it.fcst.profits.vo.request.LabelInfoRequest;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * ISpartProfitingRelationService
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
public interface ISpartProfitingRelationService {
    /**
     * 获取标签审视分页信息
     *
     * @return
     */
    PagedResult<SpartProfitingRelationVO> findByPage(LabelConfigRequest vo) throws CommonApplicationException;

    /**
     * 获取标签配置LV1名称信息
     *
     * @return
     */
    List<SpartProfitingRelationVO> getLv1List(LabelConfigRequest labelConfigRequest) throws CommonApplicationException;

    /**
     * 获取标签配置L1名称信息
     *
     * @return
     */
    List<SpartProfitingRelationVO> getL1NameList(LabelConfigRequest request) throws CommonApplicationException;

    /**
     * 获取标签配置L2名称信息
     *
     * @return
     */
    List<SpartProfitingRelationVO> getL2NameList(LabelConfigRequest request) throws CommonApplicationException;

    /**
     * 获取标签配置L3名称信息
     *
     * @return
     */
    List<SpartProfitingRelationVO> getL3NameList(LabelConfigRequest request) throws CommonApplicationException;

    /**
     * 获取标签配置版本信息
     *
     * @return
     */
    List<String> getVersions() throws CommonApplicationException;

    /**
     * 获取标签配置分页信息
     *
     * @return
     */
    PagedResult<SpartProfitingRelationVO> findConfigByPage(LabelConfigRequest spartVO)
            throws CommonApplicationException;

    /**
     * 获取复盘率信息数据
     *
     * @return
     */
    List<LabelCountVO> getManualModifyInfo(String year, String roleId) throws CommonApplicationException;

    /**
     * 保存数据
     *
     * @return
     */
    void saveData(String updateFlag, List<LabelInfoRequest> voList);

    /**
     * 提交数据
     *
     * @return
     */
    CommonResult submitData();

    /**
     * 导入数据
     *
     * @return
     */
    CommonResult importData(Attachment attachment, String fileName) throws CommonApplicationException;

    /**
     * 导出数据
     *
     * @return
     */
    CommonResult exportData(HttpServletResponse response, LabelConfigRequest labelConfigRequest);

    /**
     * 模板下载
     *
     * @return
     */
    void templateDownload(HttpServletResponse response) throws CommonApplicationException;

    /**
     * 获取人员信息列表
     * @param request 请求参数
     * @return map
     * @throws CommonApplicationException
     */
    List<Map<String ,Object>> getLastUpdatedBys(LabelConfigRequest request) throws CommonApplicationException;

    /**
     * 获取状态条件
     *
     * @param request 请求参数
     * @return 状态集合
     * @throws CommonApplicationException 异常信息
     */
    List<SpartProfitingRelationVO> getStatusList(LabelConfigRequest request) throws CommonApplicationException;

    /**
     * 获取导入，提交任务执行状态
     * @param labelOperateLogVO 参数
     * @return 状态信息
     * @throws CommonApplicationException 异常
     */
    LabelOperateLogVO getOperateRecordLogStatus(LabelOperateLogVO labelOperateLogVO)throws CommonApplicationException;
}
