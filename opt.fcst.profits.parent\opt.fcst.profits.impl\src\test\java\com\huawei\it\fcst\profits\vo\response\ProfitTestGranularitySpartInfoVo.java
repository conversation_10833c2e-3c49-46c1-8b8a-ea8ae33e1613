/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.huawei.it.fcst.profits.vo.BaseVOCoverUtilsTest;

public class ProfitTestGranularitySpartInfoVo extends BaseVOCoverUtilsTest<ProfitGranularitySpartInfoVo> {
    @Override
    protected Class<ProfitGranularitySpartInfoVo> getTClass() {
        return ProfitGranularitySpartInfoVo.class;
    }
}