/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.utils;

import org.apache.commons.lang.StringUtils;

/**
 * GenerateUtils
 *
 * <AUTHOR>
 * @since 2020/06/02.
 */
public class GenerateUtils {
    /**
     * buildTempKey
     *
     * @param lv1Code     lv1Code
     * @param lv2Code     lv2Code
     * @param l1Name      l1Name
     * @param l2Name      l2Name
     * @return String
     */
    public static String buildTempKey(String lv1Code, String lv2Code, String l1Name,
        String l2Name) {
        StringBuilder builder = new StringBuilder();
        builder.append(lv1Code);
        if (!StringUtils.isEmpty(lv2Code)) {
            builder.append("|").append(lv2Code);
        }
        if (!StringUtils.isEmpty(l1Name)) {
            builder.append("|").append(l1Name);
        }
        if (!StringUtils.isEmpty(l2Name)) {
            builder.append("|").append(l2Name);
        }
        return builder.toString();
    }

    /**
     * buildKey
     *
     * @param lv1Code      lv1Code
     * @param lv2Code      lv2Code
     * @param l1Name       l1Name
     * @param targetPeriod targetPeriod
     * @return String
     */
    public static String buildKey(String lv1Code, String lv2Code, String l1Name, String targetPeriod,String dataType) {
        StringBuilder builder = new StringBuilder();
        builder.append(lv1Code);
        if (!StringUtils.isEmpty(lv2Code)) {
            builder.append("|").append(lv2Code);
        }
        if (!StringUtils.isEmpty(l1Name)) {
            builder.append("|").append(l1Name);
        }
        if (!StringUtils.isEmpty(targetPeriod)) {
            builder.append("|").append(targetPeriod);
        }
        if(!StringUtils.isEmpty(dataType)){
            builder.append("|").append(dataType);
        }
        return builder.toString();
    }


    /**
     * getKey
     *
     * @param key key
     * @return String
     */
    public static String getKey(String key) {
        return key.substring(0, key.lastIndexOf("|"));
    }

}
