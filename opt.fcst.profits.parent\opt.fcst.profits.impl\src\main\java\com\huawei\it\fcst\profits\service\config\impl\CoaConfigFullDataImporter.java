/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.config.impl;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.vo.CommonResult;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.common.vo.ResultCode;
import com.huawei.it.fcst.profits.vo.CoaConfigDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import org.apache.commons.lang.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * coa导入功能
 *
 * @since 20230208
 * <AUTHOR>
 */
@Component
public class CoaConfigFullDataImporter extends CoaConfigDataImporter {

    @Override
    public CommonResult importData(Attachment attachment, String roleId) throws CommonApplicationException {
        DataPermissionsVO rightVO = getUserRoleOpt(Integer.parseInt(roleId));
        // 非产业全权限用户不可执行全量导入
        if (!StringUtils.equals("ALL", rightVO.getLv1DataType())) {
            return CommonResult.failed(ResultCode.FORBIDDEN, "导入失败");
        }
        return super.importData(attachment, roleId);
    }

    @Override
    public void doCoaData(int batchNum, Set<String> lv1NameSet, List<Object> voList) {
        // 删除规则，全部删除待提交数据
        iLabelConfigDao.deleteImportCoaData();
        // 分批次插入
        voList.stream()
            .forEach(vo -> ((CoaConfigDataVO) vo).setRemark(
                StringUtils.defaultString(((CoaConfigDataVO) vo).getRemark()) + CommonConstant.FULL_IMPORT_STATUS));
        updateImportCoaData(batchNum, voList);
    }
}