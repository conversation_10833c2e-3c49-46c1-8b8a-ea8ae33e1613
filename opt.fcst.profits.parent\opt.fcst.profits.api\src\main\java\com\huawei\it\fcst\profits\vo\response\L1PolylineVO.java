/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.Map;

/**
 * The Entity of L1PolylineVO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-15 16:00:24
 */

@Getter
@Setter
public class L1PolylineVO {
    private String l1Name;

    private int curYear;

    private Map<String, KrCpfL1Response> axisData = new LinkedHashMap<>();

    private LinkedList<AxisVO> axisHeader = new LinkedList<>();

}
