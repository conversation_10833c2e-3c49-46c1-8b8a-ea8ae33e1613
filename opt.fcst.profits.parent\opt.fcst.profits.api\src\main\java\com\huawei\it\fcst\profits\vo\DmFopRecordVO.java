/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.huawei.it.fcst.profits.vo.request.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

/**
 * The Entity of DmFopRecordVO
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-29 09:47:45
 */

@Getter
@Setter
public class DmFopRecordVO extends PageRequest {
    private static final long serialVersionUID = 1L;

    /**
     * 结束时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("end_date")
    private Timestamp endDate;

    /**
     * 是否删除
     **/
    @JsonProperty("del_flag")
    private String delFlag;

    /**
     * 页面模块
     **/
    @JsonProperty("page_module")
    private String pageModule;

    /**
     * 文件名称
     **/
    @JsonProperty("file_name")
    private String fileName;

    /**
     * 异常反馈
     **/
    @JsonProperty("exception_feedback")
    private String exceptionFeedback;

    /**
     * 记录条数
     **/
    @JsonProperty("record_num")
    private Integer recordNum;

    /**
     * 创建时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("creation_date")
    private Timestamp creationDate;

    /**
     * 创建人
     **/
    @JsonProperty("created_by")
    private String createdBy;

    /**
     * 文件大小
     **/
    @JsonProperty("file_size")
    private String fileSize;

    /**
     * 修改人
     **/
    @JsonProperty("last_updated_by")
    private String lastUpdatedBy;

    /**
     * 错误文件
     **/
    @JsonProperty("file_error_key")
    private String fileErrorKey;

    /**
     * 主键
     **/
    private Long id;

    /**
     * 源文件
     **/
    @JsonProperty("file_source_key")
    private String fileSourceKey;

    /**
     * 版本
     **/
    @JsonProperty("period_id")
    private String periodId;

    @JsonProperty("rec_sts")
    private String recSts;

    /**
     * 状态（success：成功、fail：失败）
     **/
    private String status;

    /**
     * 修改时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("last_update_date")
    private Timestamp lastUpdateDate;

    private String timeInterval;

    private String beginTime;

    private String endTime;

    @JsonProperty("opt_type")
    private String optType;

    /**
     * 添加文件类型
     */
    private String fileType = "xlsx";
}
