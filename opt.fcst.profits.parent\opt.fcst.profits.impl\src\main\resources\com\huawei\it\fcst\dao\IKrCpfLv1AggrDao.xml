<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.profits.dao.IKrCpfLv1AggrDao">

    <select id="getLv1List" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse">
        select t1.lv1Code, t1.lv1Name ,t2.equip_rev_after_fcst as equipRevAfter
        from (SELECT lv1_code lv1Code, lv1_name lv1Name
        FROM FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t
        where currency = 'CNY'
        and del_flag = 'N'
        <if test='periodId != null'>
            AND period_id = #{periodId,jdbcType=VARCHAR}
        </if>
        <if test='lv1s != null and lv1s.size() > 0'>
            AND lv1_code IN
            <foreach collection='lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by lv1_code, lv1_name ) t1
        left join (SELECT lv1_code lv1Code, lv1_name lv1Name, max(equip_rev_after_fcst) as equip_rev_after_fcst
        FROM FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t
        where currency = 'CNY'
        and del_flag = 'N'
        and lv2_code is null
        <if test='periodId != null'>
            AND period_id = #{periodId,jdbcType=VARCHAR}
        </if>
        <if test='phaseDate != null and phaseDate !="" '>
            AND (phase_date = #{phaseDate,jdbcType=VARCHAR} or phase_date is null)
        </if>
        <if test='orderTargetPeriodId !=null and orderTargetPeriodId!=""'>
            and target_period = #{orderTargetPeriodId,jdbcType=VARCHAR}
        </if>
        <if test='specPeriods != null and specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='lv1s != null and lv1s.size() > 0'>
            AND lv1_code IN
            <foreach collection='lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCodes != null  and bgCodes.size() > 0'>
            AND bg_code IN
            <foreach collection='bgCodes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by lv1_code, lv1_name ) t2 on t1.lv1Code = t2.lv1Code
        order by t2.equip_rev_after_fcst desc nulls last ,t1.lv1Code
    </select>
    <select id="getVersions" resultType="java.lang.Long">
        SELECT period_id
        FROM FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t
        where currency = 'CNY'
          and del_flag = 'N'
        group by period_id
        order by period_id desc
    </select>

    <select id="getSopInfo" resultType="java.lang.String">
        SELECT phase_date phaseDate
        FROM FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t
        where currency = 'CNY'
          and del_flag = 'N' and phase_date is not null
        <if test='periodId != null'>
            AND period_id = #{periodId,jdbcType=VARCHAR}
        </if>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        group by phase_date
        order by phase_date desc
    </select>

    <sql id="queryCondition">
        <if test='_parameter.get("0").fcstStep == "半年度" and _parameter.get("0").fcstStepPeriod != null'>
            AND target_period = #{0.fcstStepPeriod,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").fcstStep == "季度" and _parameter.get("0").fcstStepPeriod != null'>
            AND target_period = #{0.fcstStepPeriod,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").periodId != null'>
            AND period_id = #{0.periodId,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv1s != null and _parameter.get("0").lv1s.size() > 0'>
            AND lv1_code IN
            <foreach collection='_parameter.get("0").lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <sql id="sortedId">
        <choose>
            <when test='_parameter.get("0").orderTargetPeriodId != null and _parameter.get("0").orderType == "desc"'>
                and target_period = #{0.orderTargetPeriodId,jdbcType=VARCHAR} order by equip_rev_after_fcst desc
            </when>
            <when test='_parameter.get("0").orderTargetPeriodId != null and _parameter.get("0").orderType == "asc"'>
                and target_period = #{0.orderTargetPeriodId,jdbcType=VARCHAR} order by equip_rev_after_fcst asc
            </when>
            <otherwise>
                order by lv1_name
            </otherwise>
        </choose>
    </sql>

    <select id="findLevelByPage" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse">
        SELECT lv1_code lv1Code,lv1_name lv1Name,period_id periodId
        FROM FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t
        where currency='CNY' and del_flag = 'N' and lv2_code is null
        AND oversea_desc =#{0.overseaDesc, jdbcType=VARCHAR}
        <if test='_parameter.get("0").phaseDate != null'>
            AND (phase_date = #{0.phaseDate,jdbcType=VARCHAR} or phase_date is null)
        </if>
        <if test='_parameter.get("0").specPeriods != null and _parameter.get("0").specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection='_parameter.get("0").specPeriods' item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='_parameter.get("0").bgCodes != null  and _parameter.get("0").bgCodes != ""'>
            AND bg_code IN
            <foreach collection='_parameter.get("0").bgCodes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <include refid="queryCondition"></include>
        group by lv1_code,lv1_name,period_id
    </select>

    <select id="findLevelByPageCount" resultType="int">
        select count(1) from (SELECT lv1_code,lv1_name,period_id FROM FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t
        where currency='CNY' and del_flag = 'N' and lv2_code is null
        AND oversea_desc =#{0.overseaDesc, jdbcType=VARCHAR}
        <if test='_parameter.get("0").phaseDate != null'>
            AND (phase_date = #{0.phaseDate,jdbcType=VARCHAR} or phase_date is null)
        </if>
        <if test='_parameter.get("0").specPeriods != null and _parameter.get("0").specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection='_parameter.get("0").specPeriods' item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='_parameter.get("0").bgCodes != null  and _parameter.get("0").bgCodes != ""'>
            AND bg_code IN
            <foreach collection='_parameter.get("0").bgCodes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <include refid="queryCondition"></include>
        group by lv1_code,lv1_name,period_id)T
    </select>

    <select id="findSubtotalGroup" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse">
        select period_id periodId,
        target_period targetPeriod,
        bg_code bgCode,
        sum(equip_rev_after_act*mgp_rate_after_act) mgpRateAfter,
        sum(equip_rev_after_act) equipRevAfter,
        'ACT' as dataType
        from FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_act_t
        where currency='CNY' and del_flag = 'N' and (lv2_code is null) and (l1_name is null)
        AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        <include refid="commonCondition"></include>
        <if test='actPeriods != null'>
            AND target_period IN
            <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by period_Id,target_period,bg_code
        union
        select period_id periodId,
        target_period targetPeriod,
        bg_code bgCode,
        sum(equip_rev_after_fcst*mgp_rate_after_fcst) mgpRateAfter,
        sum(equip_rev_after_fcst) equipRevAfter,
        data_type as dataType
        from fin_dm_opt_fop.dm_fop_kr_cpf_lv1_aggr_fcst_v
        where currency='CNY' and del_flag = 'N' and (lv2_code is null) and (l1_name is null)
        AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        <include refid="commonCondition"></include>
        <if test='phaseDate != null'>
            AND (phase_date = #{phaseDate,jdbcType=VARCHAR}
            <if test='specAutoTarget != null and specAutoTarget != ""'>
                or phase_date = (select max(phase_date) from FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t where del_flag='N' and period_id = #{periodId,jdbcType=VARCHAR}
                and target_period = #{specAutoTarget,jdbcType=VARCHAR} and phase_date not like '%-%')
            </if>
            or phase_date is null)
        </if>
        <if test='dataType != null and dataType !=""'>
            and data_type = #{dataType ,jdbcType=VARCHAR}
        </if>
        <if test='fcstPeriods != null'>
            AND target_period IN
            <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='specPeriods != null and specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by period_Id,target_period,bg_code,data_type
    </select>

    <sql id="commonCondition">
        <if test='lv2Code != null  and lv2Code != ""'>
            AND lv2_code = #{lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='periodId != null  and periodId != ""'>
            AND period_id = #{periodId,jdbcType=VARCHAR}
        </if>
        <if test='lv1s != null  and lv1s != "" and lv1s.size > 0'>
            AND lv1_code IN
            <foreach collection='lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCodes != null  and bgCodes != ""'>
            AND bg_code IN
            <foreach collection='bgCode' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <select id="findDataByTop" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse">
        SELECT bg_code bgCode,
        target_period targetPeriod,
        lv1_code lv1Code,
        lv1_name lv1Name,
        <if test='level != null and level == "1"'>
            lv2_code lv2Code,lv2_name lv2Name,
        </if>
        <if test='level != null and level == "2"'>
            lv2_code lv2Code,lv2_name lv2Name,l1_name l1Name,
        </if>
        max(equip_rev_after_act) equipRevAfter,
        max(mgp_rate_after_act) mgpRateAfter,
        period_id periodId,
        'ACT' as dataType
        FROM FIN_DM_OPT_FOP.KR_CPF_LV1_AGGR_ACT_T
        WHERE currency = 'CNY' AND del_flag = 'N'
        <if test='overseaDesc != null and overseaDesc != ""'>
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test='level != null and level == "01"'>
            and (lv2_code is null) and (l1_name is null)
        </if>
        <if test='level != null and level == "1"'>
            and (lv2_code is not null) and (l1_name is null)
        </if>
        <if test='level != null and level == "2"'>
            and (lv2_code is not null) and (l1_name is not null)
        </if>
        <if test='actPeriods != null and actPeriods != ""'>
            AND target_period IN
            <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <include refid="commonCondition"></include>
        <if test='lv2Codes != null  and lv2Codes.size() > 0'>
            AND lv2_code IN
            <foreach collection='lv2Codes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='l1Names != null  and l1Names != ""'>
            AND l1_name IN
            <foreach collection='l1Names' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by  bg_code ,target_period ,lv1_code ,lv1_name ,
        <if test='level != null and level == "1"'>
            lv2_code ,lv2_name ,
        </if>
        <if test='level != null and level == "2"'>
            lv2_code ,lv2_name ,l1_name ,
        </if>
        period_id ,dataType
        UNION
        SELECT bg_code bgCode,target_period targetPeriod,lv1_code lv1Code,lv1_name lv1Name,
        <if test='level != null and level == "1"'>
            lv2_code lv2Code,lv2_name lv2Name,
        </if>
        <if test='level != null and level == "2"'>
            lv2_code lv2Code,lv2_name lv2Name,l1_name l1Name,
        </if>
        sum(equip_rev_after_fcst) equipRevAfter,sum(mgp_rate_after_fcst) mgpRateAfter,
        period_id periodId,
        data_type as dataType
        FROM fin_dm_opt_fop.dm_fop_kr_cpf_lv1_aggr_fcst_v
        WHERE currency = 'CNY'
          AND del_flag = 'N'
        <if test='overseaDesc != null and overseaDesc != ""'>
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test='specPeriods != null and specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='level != null and level == "01"'>
            and (lv2_code is null) and (l1_name is null)
        </if>
        <if test='level != null and level == "1"'>
            and (lv2_code is not null) and (l1_name is null)
        </if>
        <if test='level != null and level == "2"'>
            and (lv2_code is not null) and (l1_name is not null)
        </if>
        <if test='fcstPeriods != null and fcstPeriods != ""'>
            AND target_period IN
            <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <include refid="commonCondition"></include>
        <if test='phaseDate != null'>
            AND (phase_date = #{phaseDate,jdbcType=VARCHAR}
            <if test='specAutoTarget != null and specAutoTarget != ""'>
                or phase_date = (select max(phase_date) from FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t where del_flag='N'
                and period_id = #{periodId,jdbcType=VARCHAR}
                and target_period = #{specAutoTarget,jdbcType=VARCHAR} and phase_date not like '%-%')
            </if>
            or phase_date is null)
        </if>
        <if test='dataType != null and dataType !=""'>
            and data_type = #{dataType ,jdbcType=VARCHAR}
        </if>
        <if test='lv2Codes != null  and lv2Codes.size() > 0'>
            AND lv2_code IN
            <foreach collection='lv2Codes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='l1Names != null  and l1Names != ""'>
            AND l1_name IN
            <foreach collection='l1Names' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by  bg_code ,target_period ,lv1_code ,lv1_name ,
        <if test='level != null and level == "1"'>
            lv2_code ,lv2_name ,
        </if>
        <if test='level != null and level == "2"'>
            lv2_code ,lv2_name ,l1_name ,
        </if>
        period_id ,data_type
    </select>

    <select id="findRecordOrder" resultType="java.lang.String">
        SELECT lv1_code lv1Code
        <if test='queryType!=null and queryType!=""'>
            <if test='queryType == "F"'>
                FROM FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t
            </if>
            <if test='queryType == "H"'>
                FROM FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_act_t
            </if>
        </if>
        where currency='CNY'
        and del_flag = 'N'
        and lv2_code is null
        AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        <if test='periodId != null'>
            AND period_id = #{periodId,jdbcType=VARCHAR}
        </if>
        <if test='lv1s != null and lv1s.size() > 0'>
            AND lv1_code IN
            <foreach collection='lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCodes != null and bgCodes.size() > 0'>
            AND bg_code IN
            <foreach collection='bgCodes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='orderTargetPeriodId !=null and orderTargetPeriodId!=""'>
            and target_period = #{orderTargetPeriodId,jdbcType=VARCHAR}
        </if>
        <if test='queryType!=null and queryType!="" and queryType == "F"'>
            <if test='phaseDate != null'>
                AND (phase_date = #{phaseDate,jdbcType=VARCHAR} or phase_date is null)
            </if>
            <if test='specPeriods != null and specPeriods.size() > 0'>
                and fcst_type in
                <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        <if test='queryType!=null and queryType!=""'>
            order by
            <if test='queryType=="F"'>
                <choose>
                    <when test='orderColum!=null and orderColum!="" and orderColum =="equipRevAfter"'>
                        equip_rev_after_fcst
                    </when>
                    <when test='orderColum!=null and orderColum!="" and orderColum =="mgpRateAfter"'>
                        mgp_rate_after_fcst
                    </when>
                </choose>
            </if>
            <if test='queryType=="H"'>
                <choose>
                    <when test='orderColum!=null and orderColum!="" and orderColum =="equipRevAfter"'>
                        equip_rev_after_act
                    </when>
                    <when test='orderColum!=null and orderColum!="" and orderColum =="mgpRateAfter"'>
                        mgp_rate_after_act
                    </when>
                </choose>
            </if>
            <if test='orderType!=null and orderType=="desc"'>
                desc
            </if>
            <if test='orderType!=null and orderType=="asc"'>
                asc
            </if>
            ,lv1_code desc
        </if>
    </select>
    <select id="getFutureYearVersions" resultType="java.lang.Long">
        SELECT period_id
        FROM FIN_DM_OPT_FOP.kr_cpf_lv1_aggr_fcst_t
        where currency = 'CNY'
          and del_flag = 'N'
          and phase_date like '%' || '-' || '%'
        group by period_id
        order by period_id desc
    </select>

    <select id="findGroupAnalystsData"
            resultType="com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse">
        SELECT bg_code bgCode,
        target_code targetPeriod,
        lv1_code lv1Code,
        lv1_name lv1Name,
        <if test='level != null and level == "1"'>
            lv2_code lv2Code,lv2_name lv2Name,
        </if>
        max(equip_rev_amt) equipRevAfter,
        max(mgp_ratio) mgpRateAfter,
        version_code periodId,
        'GROUP_ANALYSTS' as dataType
        FROM fin_dm_opt_fop.dm_fop_group_analysts_result_t
        WHERE currency = 'CNY' AND del_flag = 'N' and status = 'SUBMIT'
        <if test='level != null and level == "01"'>
            and (lv2_code is null)
        </if>
        <if test='level != null and level == "1"'>
            and (lv2_code is not null)
        </if>
        <if test='overseaDesc != null and overseaDesc != ""'>
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        and target_desc =#{fcstStep,jdbcType=VARCHAR}
        <if test='periodId != null  and periodId != ""'>
            AND version_code = #{periodId,jdbcType=VARCHAR}
        </if>
        <if test='lv1s != null  and lv1s != "" and lv1s.size > 0'>
            AND lv1_code IN
            <foreach collection='lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCodes != null  and bgCodes != ""'>
            AND bg_code IN
            <foreach collection='bgCode' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by bg_code ,target_code ,lv1_code ,lv1_name ,
        <if test='level != null and level == "1"'>
            lv2_code ,lv2_name ,
        </if>
        version_code ,dataType
    </select>
    <select id="findAiCombinedRecordOrder" resultType="java.lang.String">
        SELECT lv1_code lv1Code FROM fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t
        where currency='CNY'
        and del_flag = 'N'
        and lv2_code is null
        AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        AND period_id = #{periodId,jdbcType=VARCHAR}
        AND combined_expert =#{combinedExpertType ,jdbcType=VARCHAR}
        <if test='lv1s != null and lv1s.size() > 0'>
            AND lv1_code IN
            <foreach collection='lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCodes != null and bgCodes.size() > 0'>
            AND bg_code IN
            <foreach collection='bgCodes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='orderTargetPeriodId !=null and orderTargetPeriodId!=""'>
            and target_period = #{orderTargetPeriodId,jdbcType=VARCHAR}
        </if>
        AND (phase_date = #{phaseDate,jdbcType=VARCHAR} or phase_date is null)
        <if test='specPeriods != null and specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by
        <choose>
            <when test='orderColum!=null and orderColum!="" and orderColum =="equipRevAfter"'>
                equip_rev_after_fcst
            </when>
            <when test='orderColum!=null and orderColum!="" and orderColum =="mgpRateAfter"'>
                mgp_rate_after_fcst
            </when>
        </choose>
        <if test='orderType!=null and orderType=="desc"'>
            desc
        </if>
        <if test='orderType!=null and orderType=="asc"'>
            asc
        </if>
        ,lv1_code desc
    </select>
    <select id="findGroupAnalystsRecordOrder" resultType="java.lang.String">
        SELECT lv1_code lv1Code from
        fin_dm_opt_fop.dm_fop_group_analysts_result_t
        where currency='CNY'
        and del_flag = 'N'
        and status = 'SUBMIT'
        and lv2_code is null
        AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        <if test='periodId != null'>
            AND version_code = #{periodId,jdbcType=VARCHAR}
        </if>
        <if test='lv1s != null and lv1s.size() > 0'>
            AND lv1_code IN
            <foreach collection='lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCodes != null and bgCodes.size() > 0'>
            AND bg_code IN
            <foreach collection='bgCodes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        and target_desc = #{fcstStep,jdbcType=VARCHAR}
        order by
        <choose>
            <when test='orderColum!=null and orderColum!="" and orderColum =="equipRevAfter"'>
                equip_rev_amt
            </when>
            <when test='orderColum!=null and orderColum!="" and orderColum =="mgpRateAfter"'>
                mgp_ratio
            </when>
        </choose>
        <if test='orderType!=null and orderType=="desc"'>
            desc
        </if>
        <if test='orderType!=null and orderType=="asc"'>
            asc
        </if>
        ,lv1_code desc
    </select>
    <select id="findAiCombinedData" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse">
        SELECT bg_code bgCode,
        target_period targetPeriod,
        lv1_code lv1Code,
        lv1_name lv1Name,
        <if test='level != null and level == "1"'>
            lv2_code lv2Code,lv2_name lv2Name,
        </if>
        combined_expert as combinedExpertType,
        max(equip_rev_after_fcst) equipRevAfter,
        max(mgp_rate_after_fcst) mgpRateAfter,
        period_id periodId,
        'AI_COMBINED' as dataType
        FROM fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t
        WHERE currency = 'CNY' AND del_flag = 'N'
        <if test='level != null and level == "01"'>
            and (lv2_code is null)
        </if>
        <if test='level != null and level == "1"'>
            and (lv2_code is not null)
        </if>
        <if test='overseaDesc != null and overseaDesc != ""'>
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test='specPeriods != null and specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='fcstPeriods != null and fcstPeriods != ""'>
            AND target_period IN
            <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        AND (phase_date = #{phaseDate,jdbcType=VARCHAR} or phase_date is null)
        <include refid="commonCondition"></include>
        <if test='lv2Codes != null  and lv2Codes.size() > 0'>
            AND lv2_code IN
            <foreach collection='lv2Codes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by  bg_code ,target_period ,lv1_code ,lv1_name ,
        <if test='level != null and level == "1"'>
            lv2_code ,lv2_name ,
        </if>
        period_id ,dataType ,combined_expert
    </select>

    <select id="findSubtotalAiCombinedData" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse">
        SELECT bg_code bgCode,
        target_period targetPeriod,
        sum(equip_rev_after_fcst*mgp_rate_after_fcst) mgpRateAfter,
        sum(equip_rev_after_fcst) equipRevAfter,
        period_id periodId,
        'AI_COMBINED' as dataType,
        combined_expert as combinedExpertType
        FROM fin_dm_opt_fop.kr_cpf_lv1_aggr_fcst_comb_t
        where currency='CNY' and del_flag = 'N' and (lv2_code is null)
        <if test='overseaDesc != null and overseaDesc != ""'>
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test='fcstPeriods != null and fcstPeriods != ""'>
            AND target_period IN
            <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        AND (phase_date = #{phaseDate,jdbcType=VARCHAR} or phase_date is null)
        <if test='specPeriods != null and specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <include refid="commonCondition"></include>
        group by  bg_code ,target_period , period_id ,dataType ,combined_expert
    </select>

    <select id="findSubtotalGroupAnalystsData"
            resultType="com.huawei.it.fcst.profits.vo.response.KrCpfLv1AggrResponse">
        SELECT bg_code bgCode,
        target_code targetPeriod,
        sum(equip_rev_amt*mgp_ratio) mgpRateAfter,
        sum(equip_rev_amt) equipRevAfter,
        version_code periodId,
        'GROUP_ANALYSTS' as dataType
        FROM fin_dm_opt_fop.dm_fop_group_analysts_result_t
        where currency='CNY' and del_flag = 'N' and status = 'SUBMIT' and (lv2_code is null)
        <if test='overseaDesc != null and overseaDesc != ""'>
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        and target_desc =#{fcstStep,jdbcType=VARCHAR}
        <if test='periodId != null  and periodId != ""'>
            AND version_code = #{periodId,jdbcType=VARCHAR}
        </if>
        <if test='lv1s != null  and lv1s != "" and lv1s.size > 0'>
            AND lv1_code IN
            <foreach collection='lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCodes != null  and bgCodes != ""'>
            AND bg_code IN
            <foreach collection='bgCode' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by bg_code ,target_code ,version_code ,dataType
    </select>
</mapper>
