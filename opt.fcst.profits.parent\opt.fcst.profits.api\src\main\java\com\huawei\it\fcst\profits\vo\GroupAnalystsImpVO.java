/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import com.huawei.it.fcst.profits.common.annotations.ExportAttribute;
import lombok.Getter;
import lombok.Setter;

/**
 * 预算预测 GroupAnalystsVO
 */
@Getter
@Setter
@ExcelIgnoreUnannotated
public class GroupAnalystsImpVO {

    @ExportAttribute(sort = 0)
    @ExcelProperty(value = "异常原因")
    private String errorMsg;

    @ExportAttribute(sort = 1)
    @ExcelProperty(value = "版本")
    private String versionCode;

    @ExportAttribute(sort = 2)
    @ExcelProperty(value = "LV1名称")
    private String lv1Name;

    @ExportAttribute(sort = 3)
    @ExcelProperty(value = "LV2名称")
    private String lv2Name;

    @ExportAttribute(sort = 4)
    @ExcelProperty(value = "设备收入")
    private String equipRevAmt;

    @ExportAttribute(sort = 5)
    @ExcelProperty(value = "制毛率")
    private String mgpRatio;

    @ExportAttribute(sort = 6)
    @ExcelProperty(value = "预测步长")
    private String targetDesc;

}
