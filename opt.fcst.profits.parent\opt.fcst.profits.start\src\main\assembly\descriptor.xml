<assembly
	xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
	<id>descriptor</id>
	<formats>
		<format>tar.gz</format>
	</formats>
	
	<fileSets>
		<fileSet>
			<directory>./src/main/shell</directory>
			<outputDirectory>/bin</outputDirectory>
			<directoryMode>755</directoryMode>
			<includes>
				<include>*.*</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>./target</directory>
			<outputDirectory>/libs</outputDirectory>
			<includes>
				<include>*.jar</include>
			</includes>
			<excludes>
				<exclude>*sso-5*.jar</exclude>
			</excludes>
		</fileSet>
	</fileSets>
</assembly>