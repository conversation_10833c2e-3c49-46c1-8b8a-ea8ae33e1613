/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * 出参
 *
 * <AUTHOR>
 * @since 2021年8月30
 */
@Getter
@Setter
public class CommonResult<T> {
    private long httpCode = 200L;
    private String message;
    private T entity;
    private String code = "0";
    private String faultUid = "";
    private String stackTrace = "";

    public CommonResult() {
    }

    public CommonResult(
            Long httpCode, String message) {
        this.httpCode = httpCode;
        this.message = message;
    }

    public CommonResult(
            Long httpCode, String message, T entity) {
        this.httpCode = httpCode;
        this.message = message;
        this.entity = entity;
    }

    public CommonResult(
            Long httpCode, String code, String message, T entity) {
        this.code = code;
        this.message = message;
        this.entity = entity;
        this.httpCode = httpCode;
    }

    /**
     * [服务名称]success
     *
     * @param result 入参
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> success(T result) {
        return new CommonResult<T>(ResultCode.SUCCESS.getCode(), "0", ResultCode.SUCCESS.getMessage(), result);
    }

    /**
     * 信息提示
     *
     * @param message 信息提示  继续剩余流程
     * @param code code
     * @return <T>
     */
    public static <T> CommonResult<T> messageTip(String message, String code) {
        return new CommonResult<T>(ResultCode.SUCCESS.getCode(), code, message, null);
    }

    /**
     * [服务名称]success 警告提示  code 1 不继续剩余流程 code 2  基本信息提示
     *
     * @param message 入参
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> success(String message) {
        return new CommonResult<T>(ResultCode.SUCCESS.getCode(), "1", message, null);
    }

    /**
     * [服务名称]success
     *
     * @param httpCode - 入参
     * @param Message - 入参
     * @return CommonResult
     */
    public static  CommonResult success(long httpCode, String Message) {
        return new CommonResult(httpCode, Message);
    }


    /**
     * success
     *
     * @param result 入参
     * @param message 入参
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> success(T result, String message) {
        return new CommonResult<T>(ResultCode.SUCCESS.getCode(), message, result);
    }

    /**
     * failed
     *
     * @param result 入参
     * @return CommonResult
     */
    public static <T> CommonResult<T> failed(T result) {
        return new CommonResult<T>(ResultCode.FAILED.getCode(), ResultCode.FAILED.getMessage(), result);
    }

    /**
     * [服务名称]failed
     *
     * @param errorCode 入参
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> failed(IErrorCode errorCode) {
        return new CommonResult<T>(errorCode.getCode(), errorCode.getMessage(), null);
    }

    /**
     * [服务名称]failed
     *
     * @param errorCode 入参
     * @param message 入参
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> failed(IErrorCode errorCode, String message) {
        return new CommonResult<T>(errorCode.getCode(), message, null);
    }

    /**
     * [服务名称]failed
     *
     * @param message 入参
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> failed(String message) {
        return new CommonResult<T>(ResultCode.FAILED.getCode(), message, null);
    }


    /**
     * [服务名称]failed
     *
     * @param message 入参
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> failed(Long errorCode,String message) {
        return new CommonResult<T>(errorCode, message, null);
    }

    /**
     * [服务名称]failed
     *
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> failed() {
        return failed(ResultCode.FAILED);
    }

    /**
     * [服务名称]validateFailed
     *
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> validateFailed() {
        return failed(ResultCode.VALIDATE_FAILED);
    }

    /**
     * [服务名称]validateFailed
     *
     * @param message 入参
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> validateFailed(String message) {
        return new CommonResult<T>(ResultCode.VALIDATE_FAILED.getCode(), message, null);
    }

    /**
     * [服务名称]unauthorized
     *
     * @param result 入参
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> unauthorized(T result) {
        return new CommonResult<T>(ResultCode.UNAUTHORIZED.getCode(), ResultCode.UNAUTHORIZED.getMessage(), result);
    }

    /**
     * [服务名称]forbidden
     *
     * @param result 入参
     * @return CommonResult<T>
     */
    public static <T> CommonResult<T> forbidden(T result) {
        return new CommonResult<T>(ResultCode.FORBIDDEN.getCode(), ResultCode.FORBIDDEN.getMessage(), result);
    }

    public long getHttpCode() {
        return httpCode;
    }

    public void setHttpCode(long httpCode) {
        this.httpCode = httpCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getEntity() {
        return entity;
    }

    public void setEntity(T entity) {
        this.entity = entity;
    }
}
