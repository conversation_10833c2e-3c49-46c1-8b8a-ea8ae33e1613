/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * The Entity of DmPfImportRecord
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-09-20 14:26:20
 */
@Getter
@Setter
@NoArgsConstructor
public class DmPfImportRecordVO {

    /**
     * 创建者
     **/
    private String createBy;

    /**
     * 文件名称
     **/
    private String fileName;

    /**
     * 结束时间
     **/
    private Date endTime;

    /**
     * 导入状态
     **/
    private String importStatus;

    /**
     * 异常反馈
     **/
    private String exceptionFeedback;

    /**
     * 记录条数
     **/
    private Long recordNum;

    /**
     * 序列id
     **/
    private Double id;

    /**
     * 总耗时间(s)
     **/
    private Double totalTimeConsumed;

    /**
     * 创建时间
     **/
    private Date createDate;

    /**
     * 文件大小(KB)
     **/
    private Long fileSize;

    /**
     * 版本
     **/
    private String periodId;

    /**
     * 模块(界面)
     **/
    private String module;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 一页数量
     */
    private Integer pageSize;

    /**
     * 创建时间:年月
     */
    private String createYearMonth;

    /**
     * 查询时的月初时间条件
     */
    private String monthBeginTime;

    /**
     * 查询时的月末时间条件
     */
    private String monthEndTime;
}
