/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.it.fcst.profits.common.poi;

import com.huawei.it.fcst.profits.common.constants.Constants;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class PoiUtils {

    public static final List<String> HEAD_FROMAT_CELL_TYPE_ONE = new ArrayList<>();
    public static final List<String> HEAD_FROMAT_CELL_TYPE_TWO = new ArrayList<>();

    static {
        HEAD_FROMAT_CELL_TYPE_ONE.add("制毛率");
        HEAD_FROMAT_CELL_TYPE_ONE.add("设备收入结构");
        HEAD_FROMAT_CELL_TYPE_ONE.add("量价到损益制毛调整率");
        HEAD_FROMAT_CELL_TYPE_ONE.add("对价等转换系数");
        HEAD_FROMAT_CELL_TYPE_ONE.add("发货量到收入转化率");
        HEAD_FROMAT_CELL_TYPE_ONE.add("损益制毛率");
        HEAD_FROMAT_CELL_TYPE_ONE.add("L2收入结构");
        HEAD_FROMAT_CELL_TYPE_ONE.add("量价制毛率");
        HEAD_FROMAT_CELL_TYPE_TWO.add("L3名称预测概率");
        HEAD_FROMAT_CELL_TYPE_TWO.add("L2名称预测概率");
        HEAD_FROMAT_CELL_TYPE_TWO.add("L1系数");
        HEAD_FROMAT_CELL_TYPE_TWO.add("L2系数");
        HEAD_FROMAT_CELL_TYPE_TWO.add("L3系数");
        HEAD_FROMAT_CELL_TYPE_TWO.add("L1系数预测概率");
        HEAD_FROMAT_CELL_TYPE_TWO.add("L2系数预测概率");
        HEAD_FROMAT_CELL_TYPE_TWO.add("L3系数预测概率");
    }
    public static String getStringCellValue(Cell cell) {
        if (cell == null) {
            return Constants.DEFAULT.getValue();
        }
        String cellvalue = Constants.DEFAULT.getValue();
        switch (cell.getCellType()) {
            case STRING:
                cellvalue = cell.getStringCellValue();
                break;
            case NUMERIC:
                cellvalue = getDateString(cellvalue, cell);
                break;
            case BLANK:
                cellvalue = Constants.DEFAULT.getValue();
                break;
            case BOOLEAN:
                cellvalue = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA:
                cellvalue = getRichNumber(cellvalue, cell);
                break;
            case ERROR:
                cellvalue = Constants.ERROR.getValue();
                break;
            default:
                cellvalue = Constants.DEFAULT.getValue();
                break;
        }
        return cellvalue;
    }

    private static String getDateString(String cellvalue, Cell cell) {
        String value = cellvalue;
        if (DateUtil.isCellDateFormatted(cell)) {
            value = cn.hutool.core.date.DateUtil.format(cell.getDateCellValue(), Constants.DATEFORMAT_SS.getValue());
        } else {
            if (!ObjectUtils.isEmpty(cell.getNumericCellValue())) {
                BigDecimal bigDecimal = BigDecimal.valueOf(cell.getNumericCellValue());
                value = bigDecimal.stripTrailingZeros().toPlainString();
            }
        }
        return value;
    }

    public static String getRichNumber(String cellvalue, Cell cell) {
        try {
            return String.valueOf(cell.getNumericCellValue());
        } catch (IllegalStateException e) {
            return String.valueOf(cell.getRichStringCellValue());
        }
    }
}
