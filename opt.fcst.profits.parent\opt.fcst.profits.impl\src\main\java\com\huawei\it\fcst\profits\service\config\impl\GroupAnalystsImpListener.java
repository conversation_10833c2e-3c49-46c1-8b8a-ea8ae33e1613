/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.config.impl;

import cn.hutool.core.util.NumberUtil;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.dao.IGroupAnalystsDao;
import com.huawei.it.fcst.profits.utils.GroupAnalystsUtils;
import com.huawei.it.fcst.profits.vo.GroupAnalystsImpVO;
import com.huawei.it.fcst.profits.vo.GroupAnalystsVO;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.google.common.collect.Lists;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * GroupAnalystsImpListener
 *
 * @since 2024-04-12
 */
@Slf4j
public class GroupAnalystsImpListener implements ReadListener<GroupAnalystsVO> {

    private IGroupAnalystsDao iGroupAnalystsDao;

    private Map<String, Object> context;

    // 个人中心填充所需List
    private List<GroupAnalystsImpVO> importDataList = new ArrayList<>();

    // 设置数据集合
    private ThreadLocal<ArrayList<GroupAnalystsVO>> currentDataList = ThreadLocal.withInitial(ArrayList::new);

    private ThreadLocal<StringBuilder> errTips = ThreadLocal.withInitial(StringBuilder::new);

    public GroupAnalystsImpListener(IGroupAnalystsDao iGroupAnalystsDao, Map<String, Object> context) {
        this.iGroupAnalystsDao = iGroupAnalystsDao;
        this.context = context;
    }

    /**
     * 导入写入方法
     *
     * @param dataVo dataVo
     * @param analysisContext analysisContext
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoke(GroupAnalystsVO dataVo, AnalysisContext analysisContext) {
        currentDataList.get().add(dataVo);
        if (currentDataList.get().size() >= 10000) {
            dataProcess();
        }
    }

    // 数据集合处理
    private void dataProcess() {
        ArrayList<GroupAnalystsVO> dataList = currentDataList.get();
        Map<String, String> lv1sInfo = (Map<String, String>) context.get("prodInfoLv1Map");
        Map<String, String> lv2sInfo = (Map<String, String>) context.get("prodInfoLv2Map");
        Set<String> lv1DimessionSet = (Set<String>) context.get("lv1DimessionSet");
        // 是否全量导入标识
        String flag = String.valueOf(context.get("flag"));
        long importTime = System.currentTimeMillis();
        String errorMsg = checkImportData(dataList, lv1sInfo, lv2sInfo, lv1DimessionSet, flag);
        log.info("GroupAnalystsImpListener dataProcess importTime cost:" + (System.currentTimeMillis() - importTime));
        // 转换List信息
        dataList.stream().forEach(vo -> {
            importDataList.add(BeanUtil.fillBeanWithMap(BeanUtil.beanToMap(vo), new GroupAnalystsImpVO(), true));
            vo.setLv1Code(lv1sInfo.get(vo.getLv1Name()));
            vo.setLv2Code(lv2sInfo.get(vo.getLv2Name()));
            vo.setTargetCode(GroupAnalystsUtils.getTargetCode(vo));
            // 转换设备收入（导入时默认单位是亿元）和制毛率
            if (NumberUtil.isNumber(vo.getEquipRevAmt())) {
                vo.setEquipRevAmt(String.valueOf(Double.parseDouble(vo.getEquipRevAmt()) * 100000000));
            }
            if (vo.getMgpRatio().contains("%")) {
                vo.setMgpRatio(String.valueOf(Double.parseDouble(vo.getMgpRatio().replace("%", "")) / 100));
            }
        });

        // 不存在问题数据
        if (StringUtils.isBlank(errorMsg)) {
            saveBatchData(flag, dataList);
        } else {
            errTips.get().append(errorMsg);
            if (context.containsKey("errorMsg")) {
                context.put("errorMsg", String.valueOf(context.get("errorMsg")) + errTips.get());
            } else {
                context.put("errorMsg", errTips.get().toString());
            }
        }
        int batchNum = (int) context.get("batchNum");
        batchNum++;
        context.put("batchNum", batchNum);
        context.put("totalNum", Integer.parseInt(String.valueOf(context.get("totalNum"))) + dataList.size());
        dataList.clear();
        errTips.remove();
    }

    //  插入数据
    private void saveBatchData(String flag, List<GroupAnalystsVO> importDataList) {
        long userId = Long.parseLong(String.valueOf(context.get("userId")));
        long delTime = System.currentTimeMillis();

        Map<String, String> conditionMap = importDataList.stream()
                .collect(Collectors.toMap(GroupAnalystsVO::getVersionCode, GroupAnalystsVO::getLv1Name,
                        (oldData, newData) -> oldData));
        // 先删后增
        Lists.partition(importDataList, 1000).stream().forEach(voList -> iGroupAnalystsDao.deleteDataVOs(conditionMap));
        log.info("GroupAnalystsImpListener saveBatchData delete cost:" + (System.currentTimeMillis() - delTime));
        // 设置创建人
        importDataList.stream().forEach(item -> {
            item.setCreatedBy(userId);
            item.setLastUpdatedBy(userId);
            // 全量导入
            if (StringUtils.equals("true", flag)) {
                item.setRemark(CommonConstant.FULL_IMPORT_STATUS);
            }
        });
        // 数据量小于1000条直接插入，否则分批插入
        if (importDataList.size() <= 1000) {
            iGroupAnalystsDao.batchInsertDataVOs(importDataList);
            return;
        }
        delTime = System.currentTimeMillis();
        Lists.partition(importDataList, 1000).stream()
                .forEach(voList -> iGroupAnalystsDao.batchInsertDataVOs(voList));
        log.info("GroupAnalystsImpListener saveBatchData insert cost:" + (System.currentTimeMillis() - delTime));
    }

    // 处理完后
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 处理尾部数据，不足1w的数据
        if (!currentDataList.get().isEmpty()) {
            dataProcess();
        }
        context.put("importDataList", new ArrayList<>(importDataList));
        importDataList.clear();
        log.info("导入完成!");
    }

    // 检查导入数据
    private String checkImportData(List<GroupAnalystsVO> dataList, Map<String, String> lv1sInfo,
            Map<String, String> lv2sInfo, Set<String> lv1DimessionSet, String flag) {
        log.info(">>>GroupAnalystsImpListener::checkImportData and lv1DimessionSet={}", lv1DimessionSet);
        StringBuilder str = new StringBuilder();
        // 记录行数
        AtomicInteger lineCount = new AtomicInteger(0);
        List<String> dupliDataList = new ArrayList<>();
        List<String> dupliCheckList = new ArrayList<>();
        Set<String> lv1Set = new HashSet<>();
        dataList.stream().forEach(data -> {
            lineCount.addAndGet(1);
            // 校验产业权限
            if (CollectionUtils.isNotEmpty(lv1DimessionSet) && !lv1DimessionSet.contains(lv1sInfo.get(data.getLv1Name()))) {
                data.setErrorMsg(CommonConstant.LABEL_CONFIG_WARNING1);
            }
            if (GroupAnalystsUtils.checkVersionCode(data)) {
                data.setErrorMsg("版本格式不为‘YYYYMM‘;");
            }
            if (!lv1sInfo.containsKey(data.getLv1Name())) {
                data.setErrorMsg("导入LV1不合规;" + StringUtils.defaultString(data.getErrorMsg()));
            }
            if (StringUtils.isNotBlank(data.getLv2Name()) && !lv2sInfo.containsKey(data.getLv2Name())) {
                data.setErrorMsg("导入LV2不合规;" + StringUtils.defaultString(data.getErrorMsg()));
            }
            if (GroupAnalystsUtils.checkEquipRevAmt(data)) {
                data.setErrorMsg("设备收入不合规;" + StringUtils.defaultString(data.getErrorMsg()));
            }
            if (GroupAnalystsUtils.checkMgpRatio(data)) {
                data.setErrorMsg("制毛率不合规;" + StringUtils.defaultString(data.getErrorMsg()));
            }
            if (GroupAnalystsUtils.checkTargetDesc(data)) {
                data.setErrorMsg("预测步长不合规;" + StringUtils.defaultString(data.getErrorMsg()));
            }
            if (StringUtils.equals("false", flag)) {
                // 产业数量检查（非全量导入不可导入多产业）
                if (!lv1Set.contains(data.getLv1Name()) && lv1Set.size() == 0) {
                    lv1Set.add(data.getLv1Name());
                } else if (!lv1Set.contains(data.getLv1Name())) {
                    data.setErrorMsg("导入仅支持导入单产业数据;" + StringUtils.defaultString(data.getErrorMsg()));
                }
            }
            // 重复校验
            if (dupliDataList.contains(data.toString())) {
                data.setErrorMsg("数据重复;" + StringUtils.defaultString(data.getErrorMsg()));
            } else {
                dupliDataList.add(data.toString());
            }
            // 唯一性数据校验
            if (dupliCheckList.contains(getDataInfo(data))) {
                data.setErrorMsg("数据重复;" + StringUtils.defaultString(data.getErrorMsg()));
            } else {
                dupliCheckList.add(getDataInfo(data));
            }
            if (!Objects.isNull(data.getErrorMsg())) {
                str.append("第" + ((int) context.get("batchNum") * 10000 + lineCount.get()) + "行存在"
                        + StringUtils.defaultString(data.getErrorMsg()));
            }
        });
        dupliDataList.clear();
        dupliCheckList.clear();
        lv1Set.clear();
        return str.toString();
    }

    // 唯一性数据校验
    private String getDataInfo(GroupAnalystsVO data) {
        return StringUtils.defaultString(data.getVersionCode()) + StringUtils.defaultString(data.getLv1Name())
            + StringUtils.defaultString(data.getLv2Name()) + StringUtils.defaultString(data.getTargetDesc());
    }
}
