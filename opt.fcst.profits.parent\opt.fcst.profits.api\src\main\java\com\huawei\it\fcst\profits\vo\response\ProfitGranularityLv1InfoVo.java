/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.vo.response;

import com.huawei.it.fcst.profits.common.utils.FcstJsonFormatVisitorWrapper;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The Entity of ProfitGranularityL2InfoVo
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:26:09
 */
@Getter
@Setter
public class ProfitGranularityLv1InfoVo {

    /**
     * 重量级团队LV1编码
     **/
    private String lv1Code;

    /**
     * BG编码
     **/
    private String bgCode;

    /**
     * 重量级团队LV1描述
     **/
    private String lv1Name;

    /**
     * BG名称
     **/
    private String bgName;

    /**
     * 币种
     **/
    private String currency;

    // 预测期
    private String targetPeriod;

    /**
     * 会计期
     **/
    private Long periodId;

    /**
     * 设备收入本年累计（对价后）
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal equipRevConsAfterAmt;

    /**
     * 设备成本本年累计（对价后）
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal equipCostConsAfterAmt;

    /**
     * 制毛率
     **/
    @JsonSerialize(using = FcstJsonFormatVisitorWrapper.class)
    private BigDecimal mgpRatio;
}
