<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.profits.dao.IBulletinInfoDao">
    <insert id="save">
        INSERT INTO fin_dm_opt_fop.dm_fop_bulletin_info_record_t
            (app_id,sub_app_name,bulletin_batch_no,user_id,status,remark,created_by,
             creation_date,last_updated_by,last_update_date,del_flag)
        VALUES (
                   #{appId,jdbcType=VARCHAR},
                   #{subAppName,jdbcType=VARCHAR},
                   #{bulletinBatchNo,jdbcType=VARCHAR},
                   #{userId,jdbcType=BIGINT},
                   'Y',
                    '',
                   #{userId,jdbcType=BIGINT},
                   now(),
                   #{userId,jdbcType=BIGINT},
                   now(),
                    'N'
               )
    </insert>
    <select id="getStatusByUserId" resultType="java.lang.String">
        SELECT status from fin_dm_opt_fop.dm_fop_bulletin_info_record_t
            where del_flag = 'N'
            AND app_id = #{appId,jdbcType=VARCHAR}
            AND sub_app_name = #{subAppName,jdbcType=VARCHAR}
            AND bulletin_batch_no = #{bulletinBatchNo,jdbcType=VARCHAR}
            AND user_id = #{userId,jdbcType=BIGINT}
    </select>
    <select id="checkDataIfExist" resultType="java.lang.Integer">
        SELECT COUNT(1) from fin_dm_opt_fop.dm_fop_bulletin_info_record_t
        where del_flag = 'N'
          AND app_id = #{appId,jdbcType=VARCHAR}
          AND sub_app_name = #{subAppName,jdbcType=VARCHAR}
          AND bulletin_batch_no = #{bulletinBatchNo,jdbcType=VARCHAR}
          AND user_id = #{userId,jdbcType=BIGINT}
    </select>
</mapper>
