/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.config.impl;

import cn.hutool.core.bean.BeanUtil;

import com.huawei.it.fcst.profits.comm.AbstractConfigDataExporter;
import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.vo.PlanComConfigDataVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class PlanComConfigDataExporter extends AbstractConfigDataExporter {

    @Override
    public void resetDataList(List resultResult, List<Map<String, Object>> infoList) {
        resultResult.clear();
        infoList.stream().forEach(item -> {
            if (item.containsKey("status")) {
                String status = String.valueOf(item.get("status"));
                item.put("status", status.equals("Import")
                    ? CommonConstant.LABEL_CONFIG_STATUS1
                    : CommonConstant.LABEL_CONFIG_STATUS2);
            }
            resultResult.add(BeanUtil.fillBeanWithMap(item, new PlanComConfigDataVO(), false));
        });
    }
}
