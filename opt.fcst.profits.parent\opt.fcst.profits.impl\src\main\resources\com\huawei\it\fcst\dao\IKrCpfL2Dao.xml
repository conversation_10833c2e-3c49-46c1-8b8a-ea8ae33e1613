<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.profits.dao.IKrCpfL2Dao">
    <select id="getL2Names" parameterType="com.huawei.it.fcst.profits.vo.request.ForecastsRequest"
            resultType="java.lang.String">
        SELECT l2_name
        FROM FIN_DM_OPT_FOP.kr_cpf_l2_fcst_t
        where currency='CNY' and del_flag = 'N'
        <include refid="commonCondition"></include>
        <if test='phaseDate != null and phaseDate != ""'>
            AND (phase_date = #{phaseDate,jdbcType=VARCHAR} or phase_date is null)
        </if>
        <if test='fcstPeriods != null and fcstPeriods != "" and fcstPeriods.size()>0'>
            AND target_period IN
            <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        group by l2_name
        order by l2_name
    </select>

    <select id="getHisL2Names" parameterType="com.huawei.it.fcst.profits.vo.request.ForecastsRequest"
            resultType="java.lang.String">
        SELECT l2_name
        FROM FIN_DM_OPT_FOP.dm_fop_l2_act_t
        where currency='CNY' and del_flag = 'N'
        <include refid="commonCondition"></include>
        <if test='fcstPeriods != null and fcstPeriods != "" and fcstPeriods.size()>0'>
            AND target_period IN
            <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        group by l2_name
        order by l2_name
    </select>

    <select id="getDataByPage" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfL2Response">
        <include refid="structId"></include>
    </select>

    <select id="getDataByPageCount" resultType="int">
        SELECT COUNT(1) FROM (<include refid="structId"></include>)T
    </select>

    <sql id="structId">
        select period_id as  periodId,
        lv1_code as  lv1Code,
        lv1_name as lv1Name,
        lv2_code as lv2Code,
        lv2_name as lv2Name,
        l1_name as l1Name,
        l2_name as l2Name,
        sum(rev_percent_fcst) as revPercent
        from FIN_DM_OPT_FOP.KR_CPF_L2_FCST_T
        where currency='CNY' and del_flag = 'N'
        <if test='_parameter.get("0").maxPeriods != null and _parameter.get("0").maxPeriods.size() > 0'>
            AND target_period IN
            <foreach collection='_parameter.get("0").maxPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='_parameter.get("0").specPeriods != null and _parameter.get("0").specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection='_parameter.get("0").specPeriods' item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='_parameter.get("0").phaseDate != null'>
            AND (phase_date = #{0.phaseDate,jdbcType=VARCHAR}
            or phase_date is null)
        </if>
        <if test='_parameter.get("0").lv2Code != null'>
            AND lv2_code = #{0.lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").l1Name != null'>
            AND l1_name =
            <![CDATA[
                #{0.l1Name,jdbcType=VARCHAR}
            ]]>
        </if>
        <if test='_parameter.get("0").l2Name != null  and _parameter.get("0").l2Name.length > 0'>
            AND l2_name IN
            <foreach collection='_parameter.get("0").l2Name' item="item" separator="," open="(" close=")" index="">
                <![CDATA[
                    #{item,jdbcType=VARCHAR}
                ]]>
            </foreach>
        </if>
        <if test='_parameter.get("0").periodId != null'>
            AND period_id = #{0.periodId,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv1Codes != null'>
            AND lv1_code IN
            <foreach collection='_parameter.get("0").lv1Codes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='_parameter.get("0").bgCode != null'>
            AND bg_code IN
            <foreach collection='_parameter.get("0").bgCode' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='_parameter.get("0").overseaDesc != null'>
            AND oversea_desc =#{0.overseaDesc, jdbcType=VARCHAR}
        </if>
        group by period_id,lv1_code,lv1_name,lv2_code,lv2_name,l1_name,l2_name
        order by revPercent
    </sql>

    <select id="getL2TotalInfo" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfL2Response">
        select period_id periodId,
        target_period targetPeriod,
        bg_code bgCode,
        lv1_code lv1Code,
        lv1_name lv1Name,
        lv2_code lv2Code,
        lv2_name lv2Name,
        l1_name l1Name,
        l2_name l2Name,
        'ACT' as dataType,
        mgp_rate_before_act mgpRateBefore,
        rev_percent_act revPercent,
        equip_rev_before_act equipRev,
        carryover_amount_act carryoverAmount,
        ship_qty shipQty,
        unit_price_act unitPrice,
        unit_cost_act unitCost,
        0 unitPriceUpper,
        0 unitPriceLower,
        0 unitCostUpper,
        0 unitCostLower,
        0 unitPriceAcc,
        0 unitCostAcc
        from
        FIN_DM_OPT_FOP.dm_fop_l2_act_t
        where currency='CNY'
        and del_flag = 'N'
        <include refid="commonCondition"></include>
        <if test='actPeriods != null and actPeriods != "" and actPeriods.size()>0'>
            AND target_period IN
            <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='overseaDesc != null and overseaDesc != ""'>
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test='includeFlag != null and includeFlag == "1"'>
            union
            select period_id periodId,
            target_period targetPeriod,
            bg_code bgCode,
            lv1_code lv1Code,
            lv1_name lv1Name,
            lv2_code lv2Code,
            lv2_name lv2Name,
            l1_name l1Name,
            l2_name l2Name,
            data_type as dataType,
            max(mgp_rate_before_fcst) mgpRateBefore,
            max(rev_percent_fcst) revPercent,
            max(equip_rev_before_fcst) equipRev,
            max(carryover_amount_fcst) carryoverAmount,
            max(plan_qty) shipQty,
            max(unit_price_fcst) unitPrice,
            max(unit_cost_fcst) unitCost,
            max(unit_price_fcst_upper) unitPriceUpper,
            max(unit_price_fcst_lower) unitPriceLower,
            max(unit_cost_fcst_upper) unitCostUpper,
            max(unit_cost_fcst_lower) unitCostLower,
            max(unit_price_fcst_acc) unitPriceAcc,
            max(unit_cost_fcst_acc) unitCostAcc
            from
            FIN_DM_OPT_FOP.DM_FOP_KR_CPF_L2_FCST_V
            where currency='CNY' and del_flag = 'N'
            <include refid="commonCondition"/>
            <if test='phaseDate != null and phaseDate != ""'>
                AND (phase_date = #{phaseDate,jdbcType=VARCHAR}
                <if test='specAutoTarget != null and specAutoTarget != ""'>
                    or phase_date = (select max(phase_date) from FIN_DM_OPT_FOP.kr_cpf_l2_fcst_t where del_flag='N' and period_id = #{periodId,jdbcType=VARCHAR}
                    and target_period = #{specAutoTarget,jdbcType=VARCHAR}
                    and phase_date not like '%-%'
                    )
                </if>
                or phase_date is null)
            </if>
            <if test='dataType != null and dataType !=""'>
                and data_type = #{dataType ,jdbcType=VARCHAR}
            </if>
            <if test='fcstPeriods != null and fcstPeriods != "" and fcstPeriods.size()>0'>
                AND target_period IN
                <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='specPeriods != null and specPeriods.size() > 0'>
                and fcst_type in
                <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='specPeriods == null'>
                and fcst_type is null
            </if>
            <if test='overseaDesc != null and overseaDesc != ""'>
                AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
            </if>
            group by  period_id ,target_period ,bg_code ,lv1_code ,lv1_name ,lv2_code ,lv2_name ,l1_name ,l2_name ,data_type
        </if>
    </select>

    <select id="getL2GroupInfo" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfL2Response">
        SELECT period_id periodId,
        target_period targetPeriod,
        lv1_code lv1Code,
        lv2_code lv2Code,
        l1_name l1Name,
        bg_code bgCode,
        sum(mgp_rate_before_act*rev_percent_act)mgpRateBefore,
        sum(rev_percent_act) revPercent,
        sum(equip_rev_before_act) equipRev,
        sum(carryover_amount_act) carryoverAmount,
        sum(ship_qty) shipQty,
        'ACT' as dataType
        FROM FIN_DM_OPT_FOP.dm_fop_l2_act_t
        where currency='CNY' and del_flag = 'N'
        <include refid="commonCondition"></include>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test='l2Names != null  and l2Names != "" and l2Names.size()>0'>
            AND l2_name IN
            <foreach collection='l2Names' item="item" separator="," open="(" close=")" index="">
                <![CDATA[
                    #{item,jdbcType=VARCHAR}
                ]]>
            </foreach>
        </if>
        <if test='actPeriods != null  and actPeriods != "" and actPeriods.size()>0 '>
            AND target_period IN
            <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        GROUP BY period_id,target_period,lv1_code,lv2_code,l1_name,bg_code
        <if test='includeFlag != null and includeFlag == "1"'>
            union
            SELECT period_id periodId,
            target_period targetPeriod,
            lv1_code lv1Code,
            lv2_code lv2Code,
            l1_name l1Name,
            bg_code bgCode,
            sum(mgp_rate_before_fcst*rev_percent_fcst)mgpRateBefore,
            sum(rev_percent_fcst) revPercent,
            sum(equip_rev_before_fcst) equipRev,
            sum(carryover_amount_fcst) carryoverAmount,
            sum(plan_qty) shipQty,
            data_type as dataType
            FROM FIN_DM_OPT_FOP.DM_FOP_KR_CPF_L2_FCST_V
            where currency='CNY' and del_flag = 'N'
            <include refid="commonCondition"></include>
            <if test="overseaDesc != null and overseaDesc != ''">
                AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
            </if>
            <if test='phaseDate != null and phaseDate != ""'>
                AND (phase_date = #{phaseDate,jdbcType=VARCHAR}
                <if test='specAutoTarget != null and specAutoTarget != ""'>
                    or phase_date = (select max(phase_date) from FIN_DM_OPT_FOP.kr_cpf_l2_fcst_t where del_flag='N' and period_id = #{periodId,jdbcType=VARCHAR}
                    and target_period = #{specAutoTarget,jdbcType=VARCHAR} and phase_date not like '%-%'
                    )
                </if>
                or phase_date is null)
            </if>
            <if test='dataType != null and dataType !=""'>
                and data_type = #{dataType ,jdbcType=VARCHAR}
            </if>
            <if test='l2Names != null  and l2Names != "" and l2Names.size()>0'>
                AND l2_name IN
                <foreach collection='l2Names' item="item" separator="," open="(" close=")" index="">
                    <![CDATA[
                        #{item,jdbcType=VARCHAR}
                    ]]>
                </foreach>
            </if>
            <if test='fcstPeriods != null  and fcstPeriods != ""'>
                AND target_period IN
                <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='specPeriods != null and specPeriods.size() > 0'>
                and fcst_type in
                <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='specPeriods == null'>
                and fcst_type is null
            </if>
            GROUP BY period_id,target_period,lv1_code,lv2_code,l1_name,bg_code,data_type
        </if>
    </select>

    <select id="getDataByDimension" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfL2Response">
        select period_id periodId,
        target_period targetPeriod,
        bg_code bgCode,
        lv1_code lv1Code,
        lv1_name lv1Name,
        lv2_code lv2Code,
        lv2_name lv2Name,
        l1_name l1Name,
        l2_name l2Name,
        mgp_rate_before_act mgpRateBefore,
        rev_percent_act revPercent,
        carryover_amount_act carryoverAmount,
        ship_qty shipQty,
        'ACT' as dataType
        from
        FIN_DM_OPT_FOP.dm_fop_l2_act_t
        where currency='CNY'
        and del_flag = 'N'
        <include refid="commonCondition"></include>
        <if test='actPeriods != null and actPeriods != ""'>
            AND target_period IN
            <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        union
        select period_id periodId,
        target_period targetPeriod,
        bg_code bgCode,
        lv1_code lv1Code,
        lv1_name lv1Name,
        lv2_code lv2Code,
        lv2_name lv2Name,
        l1_name l1Name,
        l2_name l2Name,
        max(mgp_rate_before_fcst) mgpRateBefore,
        max(rev_percent_fcst) revPercent,
        max(carryover_amount_fcst) carryoverAmount,
        max(plan_qty) shipQty,
        data_type as dataType
        from
        FIN_DM_OPT_FOP.DM_FOP_KR_CPF_L2_FCST_V
        where currency='CNY' and del_flag = 'N'
        <include refid="commonCondition"></include>
        <if test='phaseDate != null and phaseDate != ""'>
            AND (phase_date = #{phaseDate,jdbcType=VARCHAR}
            <if test='specAutoTarget != null and specAutoTarget != ""'>
                or phase_date = (select max(phase_date) from FIN_DM_OPT_FOP.kr_cpf_l2_fcst_t where del_flag='N' and period_id = #{periodId,jdbcType=VARCHAR}
                and target_period = #{specAutoTarget,jdbcType=VARCHAR} and phase_date not like '%-%' )
            </if>
            or phase_date is null)
        </if>
        <if test='dataType != null and dataType !=""'>
            and data_type = #{dataType ,jdbcType=VARCHAR}
        </if>
        <if test='fcstPeriods != null and fcstPeriods != "" and fcstPeriods.size()>0'>
            AND target_period IN
            <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='specPeriods != null and specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        group by period_id , target_period ,bg_code ,lv1_code ,lv1_name ,lv2_code ,lv2_name ,l1_name , l2_name ,data_type
    </select>

    <select id="exportL2Factor" resultType="com.huawei.it.fcst.profits.vo.response.KrCpfL2Response">
        select periodId,targetPeriod,bgCode,lv1Code,lv1Name, lv2Name,l1Name,l2Name,mgpRateBefore,revPercent,carryoverAmount,
        shipQty,unitPrice,unitCost,equipRevBefore,equipCostBefore,phaseDate,fcstType ,overseaDesc,groupKey
        from (
        select period_id periodId,
        target_period targetPeriod,
        bg_code bgCode,
        lv1_code lv1Code,
        lv1_name lv1Name,
        lv2_code lv2Code,
        lv2_name lv2Name,
        l1_name l1Name,
        l2_name l2Name,
        mgp_rate_before_act mgpRateBefore,
        rev_percent_act revPercent,
        carryover_amount_act carryoverAmount,
        ship_qty shipQty,
        unit_price_act unitPrice,
        unit_cost_act unitCost,
        equip_rev_before_act equipRevBefore,
        equip_cost_before_act equipCostBefore,
        '' phaseDate,
        '' fcstType,
        (case when oversea_desc  = '国内' then '中国区' else oversea_desc end) overseaDesc,
        'ACT' groupKey
        from
        FIN_DM_OPT_FOP.dm_fop_l2_act_t
        where currency='CNY' and del_flag = 'N'
        <include refid="expCondition"></include>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test="predictionType =='YEAR' ">
            <if test='actPeriods != null and actPeriods != ""'>
                AND target_period IN
                <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        <if test="predictionType !='YEAR' ">
            <if test='actPeriods != null and actPeriods != ""'>
                AND target_period NOT IN
                <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        UNION ALL
        select period_id periodId,
        target_period targetPeriod,
        bg_code bgCode,
        lv1_code lv1Code,
        lv1_name lv1Name,
        lv2_code lv2Code,
        lv2_name lv2Name,
        l1_name l1Name,
        l2_name l2Name,
        mgp_rate_before_fcst mgpRateBefore,
        rev_percent_fcst revPercent,
        carryover_amount_fcst carryoverAmount,
        plan_qty shipQty,
        unit_price_fcst unitPrice,
        unit_cost_fcst unitCost,
        equip_rev_before_fcst equipRevBefore,
        '' equipCostBefore,
        phase_date phaseDate,
        fcst_type,
        (case when oversea_desc  = '国内' then '中国区' else oversea_desc end) overseaDesc ,
        'fcst' groupKey
        from
        fin_dm_opt_fop.kr_cpf_l2_fcst_t
        where currency='CNY' and del_flag = 'N'
        <include refid="expCondition"></include>
        <![CDATA[
           and IFNULL(fcst_type,'null') <> '年度平均法'
           ]]>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test="predictionType =='YEAR' ">
            <if test='specPeriods != null and specPeriods.size() > 0'>
                and fcst_type in
                <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='fcstPeriods != null and fcstPeriods != "" and fcstPeriods.size()>0'>
                AND target_period IN
                <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        <if test='phaseDate != null and phaseDate != ""'>
            AND (phase_date = #{phaseDate,jdbcType=VARCHAR}
            or phase_date is null)
        </if>
        ) order by l1name desc ,targetPeriod desc;
    </select>


    <select id="exportL2FactorCount" resultType="int">
        select count(*) from (
        select period_id periodId
        from
        FIN_DM_OPT_FOP.dm_fop_l2_act_t
        where currency='CNY' and del_flag = 'N'
        <include refid="expCondition"></include>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test="predictionType =='YEAR' ">
            <if test='actPeriods != null and actPeriods != ""'>
                AND target_period IN
                <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        <if test="predictionType !='YEAR' ">
            <if test='actPeriods != null and actPeriods != ""'>
                AND target_period NOT IN
                <foreach collection='actPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        union all
        select period_id periodId
        from
        fin_dm_opt_fop.kr_cpf_l2_fcst_t
        where currency='CNY' and del_flag = 'N'
        <include refid="expCondition"></include>
        <![CDATA[
           and IFNULL(fcst_type,'null') <> '年度平均法'
           ]]>
        <if test="overseaDesc != null and overseaDesc != ''">
            AND oversea_desc =#{overseaDesc, jdbcType=VARCHAR}
        </if>
        <if test="predictionType =='YEAR' ">
            <if test='specPeriods != null and specPeriods.size() > 0'>
                and fcst_type in
                <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='fcstPeriods != null and fcstPeriods != "" and fcstPeriods.size()>0'>
                AND target_period IN
                <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>
        <if test='phaseDate != null and phaseDate != ""'>
            AND (phase_date = #{phaseDate,jdbcType=VARCHAR}
            or phase_date is null)
        </if>
        )
    </select>

    <sql id="expCondition">
        AND period_id = #{periodId,jdbcType=VARCHAR}
        <if test='lv1s != null and lv1s.size() > 0 '>
            AND lv1_code IN
            <foreach collection='lv1s' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCodes != null  and bgCodes.size() > 0'>
            AND bg_code IN
            <foreach collection='bgCodes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <select id="getTop5L2Name" parameterType="com.huawei.it.fcst.profits.vo.request.ForecastsRequest"
            resultType="java.lang.String">
        SELECT l2_name FROM FIN_DM_OPT_FOP.kr_cpf_l2_fcst_t
        where currency='CNY' and del_flag = 'N'
        <include refid="commonCondition"></include>
        <if test='phaseDate != null and phaseDate != ""'>
            AND (phase_date = #{phaseDate,jdbcType=VARCHAR} or phase_date is null)
        </if>
        <if test='fcstPeriods != null and fcstPeriods != "" and fcstPeriods.size()>0'>
            AND target_period IN
            <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='specPeriods != null and specPeriods.size() > 0'>
            and fcst_type in
            <foreach collection="specPeriods" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by rev_percent_fcst desc
        limit 5
    </select>

    <select id="getHisTop5L2Name" parameterType="com.huawei.it.fcst.profits.vo.request.ForecastsRequest"
            resultType="java.lang.String">
        SELECT l2_name FROM FIN_DM_OPT_FOP.dm_fop_l2_act_t
        where currency='CNY' and del_flag = 'N'
        <include refid="commonCondition"></include>
        <if test='fcstPeriods != null and fcstPeriods != "" and fcstPeriods.size()>0'>
            AND target_period IN
            <foreach collection='fcstPeriods' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by rev_percent_act desc
        limit 5
    </select>

    <sql id="commonCondition">
        <if test='lv2Code != null  and lv2Code != ""'>
            AND lv2_code = #{lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='l1Name != null  and l1Name != ""'>
            AND l1_name =
            <![CDATA[
                #{l1Name,jdbcType=VARCHAR}
            ]]>
        </if>
        <if test='periodId != null  and periodId != ""'>
            AND period_id = #{periodId,jdbcType=VARCHAR}
        </if>
        <if test='lv1Codes != null  and lv1Codes != ""'>
            AND lv1_code IN
            <foreach collection='lv1Codes' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='bgCode != null  and bgCode != ""'>
            AND bg_code IN
            <foreach collection='bgCode' item="item" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>
</mapper>
