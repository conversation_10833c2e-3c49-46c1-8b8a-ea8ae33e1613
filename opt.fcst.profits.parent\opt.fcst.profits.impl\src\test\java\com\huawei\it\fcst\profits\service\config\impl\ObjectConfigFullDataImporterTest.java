package com.huawei.it.fcst.profits.service.config.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;

import com.huawei.it.fcst.profits.common.user.JalorUserTools;
import com.huawei.it.fcst.profits.common.vo.DataPermissionsVO;
import com.huawei.it.fcst.profits.dao.ILabelConfigDao;
import com.huawei.it.fcst.profits.vo.CoaConfigDataVO;
import com.huawei.it.fcst.profits.vo.ObjectConfigDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;

import java.util.Arrays;
import java.util.HashSet;

/**
 * CoaConfigFullDataImporter Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>6月 14, 2023</pre>
 */
public class ObjectConfigFullDataImporterTest {

    @InjectMocks
    private ObjectConfigFullDataImporter objectConfigFullDataImporterUnderTest;

    @Mock
    private JalorUserTools jalorUserTools;

    @Mock
    private ILabelConfigDao iLabelConfigDao;
    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: importData(Attachment attachment, String roleId)
     */

    @Test
    public void testImportData() throws CommonApplicationException {
        // Setup
        final Attachment attachment = new Attachment("id", "mediaType", "object");
        DataPermissionsVO vo = new DataPermissionsVO();
        vo.setLv1DataType("Test");
        PowerMockito.doReturn(vo).when(jalorUserTools).getRolePermission(anyInt());
        // Run the test
        objectConfigFullDataImporterUnderTest.importData(attachment, "1250");
        Assertions.assertNotNull(attachment);
    }

    @Test
    public void testImportData1() throws CommonApplicationException {
        // Setup
        final Attachment attachment = new Attachment("id", "mediaType", "object");
        DataPermissionsVO vo = new DataPermissionsVO();
        vo.setLv1DataType("Test");
        PowerMockito.doReturn(vo).when(jalorUserTools).getRolePermission(anyInt());
        // Run the test
        objectConfigFullDataImporterUnderTest.importData(attachment, "1250");
        Assertions.assertNotNull(attachment);
    }

    /**
     * Method: doCoaData(int batchNum, Set<String> lv1NameSet, List<Object> voList)
     */
    @Test
    public void testDoCoaData() {
        // Setup
        PowerMockito.doNothing().when(iLabelConfigDao).deleteImportObjectData();
        PowerMockito.doReturn(1).when(iLabelConfigDao).createObjectDataList(any());
        ObjectConfigDataVO vo= new ObjectConfigDataVO();
        // Run the test
        objectConfigFullDataImporterUnderTest.doObjectData(0, new HashSet<>(Arrays.asList("value")), Arrays.asList(vo));

        // Verify the results
        Assertions.assertNotNull(vo);
    }

}
