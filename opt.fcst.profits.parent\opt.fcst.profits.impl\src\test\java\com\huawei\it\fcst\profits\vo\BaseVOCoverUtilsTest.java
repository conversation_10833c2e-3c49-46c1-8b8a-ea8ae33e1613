/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.profits.vo;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

public abstract class BaseVOCoverUtilsTest<T> {
    /**
     * getT 获取待测类型
     *
     * @return T
     */
    protected abstract Class<T> getTClass();

    /**
     * 覆盖get、set方法
     */
    @Test
    public void testGetMethodAndSetMethod() {
        Class<T> modelClass = getTClass();
        Field[] fields = modelClass.getDeclaredFields();
        for (Field field : fields) {
            String propertyName = field.getName();
            if (field.getGenericType().toString().equals("boolean") && propertyName.startsWith("is")) {
                propertyName = propertyName.substring(2);
            }

            PropertyDescriptor pd;
            try {
                pd = new PropertyDescriptor(propertyName, modelClass);
                Method get = pd.getReadMethod();
                Method set = pd.getWriteMethod();
                set.invoke(modelClass.newInstance(), get.invoke(modelClass.newInstance()));
            } catch (Exception e) {

            }
        }
        Assertions.assertNotNull(fields);
    }

    /**
     * 覆盖toString方法
     */
    @Test
    public void testToStringMethod() {
        Class<T> modelClass = getTClass();
        Method[] methods = modelClass.getDeclaredMethods();
        if (Arrays.stream(methods).map(Method::getName).noneMatch("toString"::equals)) {
            return;
        }

        try {
            modelClass.newInstance().toString();
        } catch (Exception e) {

        }
        Assertions.assertNotNull(methods);
    }

    /**
     * 覆盖 hashCode 方法
     */
    @Test
    public void testHashCodeMethod() {
        Class<T> modelClass = getTClass();
        Method[] methods = modelClass.getDeclaredMethods();
        if (Arrays.stream(methods).map(Method::getName).noneMatch("hashCode"::equals)) {
            return;
        }

        try {
            Object object = modelClass.newInstance();
            object.hashCode();

            Field[] fields = modelClass.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                field.set(object, null);
                object.hashCode();

                if (MAP1.containsKey(field.getType().getName())) {
                    Object fieldObject = MAP1.get(field.getType().getName());
                    field.set(object, fieldObject);
                    object.hashCode();
                } else {
                    Object fieldObject = field.getType().newInstance();
                    field.set(object, fieldObject);
                    object.hashCode();
                }
            }
        } catch (Exception e) {

        }
        Assertions.assertNotNull(methods);
    }

    /**
     * 覆盖 equals 方法
     */
    @Test
    public void testEqualsMethod() {
        Class<T> modelClass = getTClass();
        Method[] methods = modelClass.getDeclaredMethods();
        if (Arrays.stream(methods).map(Method::getName).noneMatch("equals"::equals)) {
            return;
        }

        try {
            Object o1 = modelClass.newInstance();
            Object o2 = modelClass.newInstance();
            o1.equals(null);
            o1.equals(o1);

            Field[] fields = modelClass.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                field.set(o1, null);
                field.set(o2, null);
                o1.equals(o2);

                if (MAP1.containsKey(field.getType().getName())) {
                    Object f1 = MAP1.get(field.getType().getName());
                    Object f2 = MAP2.get(field.getType().getName());
                    field.set(o1, null);
                    field.set(o2, f2);
                    o1.equals(o2);

                    field.set(o1, f1);
                    o1.equals(o2);

                    field.set(o2, f1);
                    o1.equals(o2);
                } else if (field.getType().getName().startsWith("[")) {
                    // 先不考虑数组情况
                } else {
                    Object f1 = field.getType().newInstance();
                    Object f2 = field.getType().newInstance();
                    field.set(o1, null);
                    field.set(o2, f2);
                    o1.equals(o2);

                    field.set(o1, f1);
                    o1.equals(o2);

                    field.set(o2, f1);
                    o1.equals(o2);
                }

            }
        } catch (Exception e) {

        }
        Assertions.assertNotNull(methods);
    }

    private static final Map<String, Object> MAP1 = new HashMap<>();
    private static final Map<String, Object> MAP2 = new HashMap<>();

    static {
        MAP1.put("java.lang.String", "test1");
        MAP2.put("java.lang.String", "test2");

        MAP1.put("java.lang.Integer", 1);
        MAP2.put("java.lang.Integer", 2);

        MAP1.put("java.lang.Long", 1L);
        MAP2.put("java.lang.Long", 2L);

        MAP1.put("java.lang.Double", 1.0);
        MAP2.put("java.lang.Double", 2.0);

        MAP1.put("java.lang.Float", 1F);
        MAP2.put("java.lang.Float", 2F);

        MAP1.put("java.util.List", new EqualList<>());
        MAP2.put("java.util.List", new EqualList<>());

        MAP1.put("java.util.Set", new EqualSet<>());
        MAP2.put("java.util.Set", new EqualSet<>());

        MAP1.put("java.util.Map", new EqualMap<>());
        MAP2.put("java.util.Map", new EqualMap<>());

        MAP1.put("java.lang.Byte", new Byte("1"));
        MAP2.put("java.lang.Byte", new Byte("2"));

        MAP1.put("java.lang.Boolean", Boolean.TRUE);
        MAP2.put("java.lang.Boolean", Boolean.FALSE);

        MAP1.put("java.util.Date", new Date());
        MAP2.put("java.util.Date", new Date());

        MAP1.put("int", 1);
        MAP2.put("int", 2);

        MAP1.put("int", 1);
        MAP2.put("int", 2);

        MAP1.put("long", 1L);
        MAP2.put("long", 2L);

        MAP1.put("float", 1F);
        MAP2.put("float", 2F);

        MAP1.put("double", 1D);
        MAP2.put("double", 2D);

        MAP1.put("byte", Byte.parseByte("1"));
        MAP2.put("byte", Byte.parseByte("2"));

        MAP1.put("short", Short.parseShort("1"));
        MAP2.put("short", Short.parseShort("2"));

        MAP1.put("char", 'a');
        MAP2.put("char", 'b');

        MAP1.put("boolean", true);
        MAP2.put("boolean", false);
    }

    private static class EqualList<E> extends ArrayList<E> {
        public boolean equals(Object o) {
            return o == this;
        }
    }

    private static class EqualSet<E> extends HashSet<E> {
        public boolean equals(Object o) {
            return o == this;
        }
    }

    private static class EqualMap<K, V> extends HashMap<K, V> {
        public boolean equals(Object o) {
            return o == this;
        }
    }
}