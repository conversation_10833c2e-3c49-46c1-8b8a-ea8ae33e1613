/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.service.predict.impl;

import com.huawei.it.fcst.profits.comm.AbstractService;
import com.huawei.it.fcst.profits.dao.IKrCpfL1Dao;
import com.huawei.it.fcst.profits.service.IKrCpfL1Service;
import com.huawei.it.fcst.profits.vo.request.ForecastsParamWrapper;
import com.huawei.it.fcst.profits.vo.request.ForecastsRequest;
import com.huawei.it.fcst.profits.vo.response.AxisVO;
import com.huawei.it.fcst.profits.vo.response.KrCpfL1Response;
import com.huawei.it.fcst.profits.vo.response.L1PolylineVO;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.inject.Inject;

/**
 * The Service of KrCpfL1ActVO entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-10-18 09:25:12
 */
@Service
public class KrCpfL1Service extends AbstractService implements IKrCpfL1Service {

    private static final Logger logger = LoggerFactory.getLogger(KrCpfL1Service.class);

    @Inject
    private IKrCpfL1Dao iKrCpfL1Dao;

    /**
     * 获取合拼数据
     *
     * @param forecastsRequest 请求信息
     * @return List
     */
    public List<KrCpfL1Response> getMergeData(ForecastsRequest forecastsRequest) {
        return iKrCpfL1Dao.getL1TotalInfo(forecastsRequest);
    }

    /**
     * 获取L1业务转结率信息、获取L1业务平均价格、平均成本信息
     *
     * @param forecastsRequest 请求信息
     * @return GroupL1GraphVO
     */
    @Override
    public L1PolylineVO getL1PolylineInfo(ForecastsRequest forecastsRequest) {
        // 区域值设置
        if(checkLv1Result(forecastsRequest)){
            return new L1PolylineVO();
        }
        L1PolylineVO groupL1VO = new L1PolylineVO();
        forecastsRequest.withOverseaDescValue();
        ForecastsParamWrapper.ForceastParamEnum enumType = ForecastsParamWrapper
                .ForceastParamEnum
                .getByCode(ForecastsParamWrapper.getSplicedPredictionTabGraphValue(forecastsRequest));
        if (Objects.isNull(enumType)) {
            logger.error("请检查请求参数");
            return groupL1VO;
        }
        // 通过枚举获取当前参数构建逻辑；
        ForecastsRequest buildParam = enumType.beforeBuildParam(forecastsRequest);
        List<KrCpfL1Response> mergeData = getMergeData(buildParam);
        Map<String, AxisVO> axisVOTMap = enumType.afterBuildParam(forecastsRequest);
        Map<String, KrCpfL1Response> axisData = new LinkedHashMap<>();
        LinkedList<AxisVO> axisHeader = new LinkedList<>();
        axisVOTMap.forEach((key,value) ->{
            axisData.put(key,null);
            axisHeader.add(value);
        });
        mergeData.stream().forEach(l1Act -> {
            axisData.entrySet().forEach(entry -> {
                String targetPeriod = l1Act.getTargetPeriod() + l1Act.getDataType();
                if (StringUtils.equals(targetPeriod, entry.getKey())) {
                    axisData.put(targetPeriod, l1Act);
                }
            });
        });
        groupL1VO.setAxisData(axisData);
        groupL1VO.setAxisHeader(axisHeader);
        groupL1VO.setL1Name(buildParam.getL1Name());
        groupL1VO.setCurYear(buildParam.getCurYear());
        return groupL1VO;
    }
}
