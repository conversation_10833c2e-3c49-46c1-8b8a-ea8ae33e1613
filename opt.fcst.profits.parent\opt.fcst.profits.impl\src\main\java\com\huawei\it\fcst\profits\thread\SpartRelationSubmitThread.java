/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.fcst.profits.thread;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.huawei.it.fcst.profits.common.constants.CommonConstant;
import com.huawei.it.fcst.profits.common.enums.SaveTypeEnum;
import com.huawei.it.fcst.profits.dao.ILabelOperateLogDao;
import com.huawei.it.fcst.profits.dao.ISpartProfitingRelationDao;
import com.huawei.it.fcst.profits.utils.CommUtils;
import com.huawei.it.fcst.profits.vo.LabelOperateLogVO;
import com.huawei.it.fcst.profits.vo.SpartProfitingRelationVO;
import com.huawei.it.fcst.profits.vo.request.LabelInfoRequest;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;

public class SpartRelationSubmitThread implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(SpartRelationSubmitThread.class);

    private Long userId;

    private Timestamp curTime;

    private List<SpartProfitingRelationVO> copyDataList;

    private static ReentrantLock reentrantLock = new ReentrantLock(true);

    private CountDownLatch countDownLatch;

    private AtomicReference<CopyOnWriteArrayList<SpartProfitingRelationVO>> atomicReference;

    private LabelOperateLogVO labelOperateLogVO;

    private ILabelOperateLogDao iOperateLogDao;

    /**
     * 构造方法
     *
     * @param labelOperateLogVO 日志信息
     * @param userId            userID
     * @param copyDataList      操作数据
     * @param atomicReference   对象
     * @param countDownLatch    同步锁
     * @param curTime           时间
     */
    public SpartRelationSubmitThread(LabelOperateLogVO labelOperateLogVO, Long userId, List<SpartProfitingRelationVO> copyDataList, AtomicReference<CopyOnWriteArrayList<SpartProfitingRelationVO>> atomicReference,
                                     CountDownLatch countDownLatch,
                                     Timestamp curTime) {
        this.userId = userId;
        this.curTime = curTime;
        this.countDownLatch = countDownLatch;
        this.atomicReference = atomicReference;
        this.copyDataList = copyDataList;
        this.labelOperateLogVO = labelOperateLogVO;
        iOperateLogDao = Jalor.getContext().getBean(ILabelOperateLogDao.class);
    }

    @Override
    public void run() {
        synchronized (SpartRelationSubmitThread.class) {
            Object relationDao = Jalor.getContext().getBean("ISpartProfitingRelationDao");
            if (Objects.isNull(relationDao)) {
                return;
            }
            try {
                List<SpartProfitingRelationVO> save2SubmitList = new ArrayList<>();
                List<SpartProfitingRelationVO> moveStatusList = new ArrayList<>();
                List<SpartProfitingRelationVO> coverStatuslList = new ArrayList<>();
                Map<String, List<SpartProfitingRelationVO>> submitRetMap = new HashMap<>();
                buildDiffScenarioList(copyDataList, save2SubmitList, submitRetMap, curTime);
                // 新增数据提交
                int saveNum = commitSave2SubmitData(userId, curTime, save2SubmitList, atomicReference);
                // 提交数据
                int num = commitSpartData(userId, copyDataList, moveStatusList, coverStatuslList, submitRetMap, curTime, atomicReference);
                upsertOperateNum(labelOperateLogVO, num + saveNum);
                countDownLatch.countDown();
            } catch (Exception ex) {
                logger.error("SpartRelationMainSubmitThread occurs error: {}", ex);
                countDownLatch.countDown();
            } finally {
                ((ISpartProfitingRelationDao) relationDao).uptCopyList("1", copyDataList, userId);
            }
        }
    }

    private void upsertOperateNum(LabelOperateLogVO labelOperateLogVO, int num) {
        if (Objects.isNull(labelOperateLogVO) || labelOperateLogVO.getIsSkip()) {
            return;
        }
        LabelOperateLogVO logVO = LabelOperateLogVO.builder().objectId(labelOperateLogVO.getObjectId()).upsertNum(num).status("PROCESSING").build();
        iOperateLogDao.updateUpsertNum(logVO);
    }

    /**
     * 构造不同场景的数组
     *
     * @param dataList
     * @param save2SubmitList
     * @param submitRetMap
     */
    public void buildDiffScenarioList(List<SpartProfitingRelationVO> dataList, List<SpartProfitingRelationVO> save2SubmitList,
                                      Map<String, List<SpartProfitingRelationVO>> submitRetMap,
                                      Timestamp curTime) throws CommonApplicationException {
        List<SpartProfitingRelationVO> tempList = new ArrayList<>();
        dataList.stream().forEach(result -> {
            result.setUnionKey(CommUtils.getUnionKey(String.valueOf(result.getPeriodId()), result.getItemCode(), result.getL1Name()));
            result.setUnionStatusKey(CommUtils.getUnionKey(result.getUpdateFlag(), result.getStatus()));
            if (CommonConstant.AUDIT_SAVE_STATUS_LIST.contains(result.getUnionStatusKey())) {
                result.setOptType("add");
                result.setLastUpdateDate(curTime);
                result.setLastUpdatedBy(userId);
                save2SubmitList.add(result);
            }
            if (CommonConstant.AUDIT_MOVE_STATUS_LIST.contains(result.getUnionStatusKey())) {
                result.setLastUpdateDate(curTime);
                result.setLastUpdatedBy(userId);
                tempList.add(result);
            }
        });
        if (CollectionUtils.isEmpty(tempList)) {
            return;
        }
        LabelInfoRequest labelInfoRequest = new LabelInfoRequest();
        labelInfoRequest.setStatus(SaveTypeEnum.SUBMIT.getCode());
        labelInfoRequest.setSubmitList(tempList);
        labelInfoRequest.setOptType(null);
        Object relationDao = Jalor.getContext().getBean("ISpartProfitingRelationDao");
        if (Objects.isNull(relationDao)) {
            return;
        }
        labelInfoRequest.setOptType(null);
        List<SpartProfitingRelationVO> submitList = ((ISpartProfitingRelationDao) relationDao).findDataByParam(labelInfoRequest);
        if (CollectionUtils.isEmpty(submitList)) {
            return;
        }
        submitRetMap.putAll(submitList.stream().collect(Collectors.groupingBy(result -> {
            String unionKey = CommUtils.getUnionKey(String.valueOf(result.getPeriodId()), result.getItemCode(), result.getL1Name());
            result.setUnionKey(unionKey);
            return unionKey;
        })));
    }

    public int commitSave2SubmitData(Long userId, Timestamp lastUpdateDate, List<SpartProfitingRelationVO> save2SubmitList, AtomicReference<CopyOnWriteArrayList<SpartProfitingRelationVO>> atomicReference) throws CommonApplicationException {
        Object relationDao = Jalor.getContext().getBean("ISpartProfitingRelationDao");
        int num = 0;
        if (Objects.isNull(relationDao)) {
            return num;
        }
        if (CollectionUtils.isNotEmpty(save2SubmitList)) {
            atomicReference.get().addAll(save2SubmitList);
            num = ((ISpartProfitingRelationDao) relationDao).updateMoveList(userId, lastUpdateDate, null, save2SubmitList);
        }
        return num;
    }

    /**
     * 提交数据
     *
     * @param dataList     更新数据
     * @param submitRetMap 修改数据
     */
    public int commitSpartData(Long userId, List<SpartProfitingRelationVO> dataList, List<SpartProfitingRelationVO> moveStatusList,
                               List<SpartProfitingRelationVO> coverStatuslList, Map<String, List<SpartProfitingRelationVO>> submitRetMap,
                               Timestamp curTime, AtomicReference<CopyOnWriteArrayList<SpartProfitingRelationVO>> atomicReference) throws CommonApplicationException {
        List<SpartProfitingRelationVO> originList = new ArrayList<>();
        List<SpartProfitingRelationVO> oldList = new ArrayList<>();
        int num = 0;
        dataProcessing(userId, dataList, moveStatusList, coverStatuslList, submitRetMap, curTime, atomicReference, originList, oldList);
        Object relationDao = Jalor.getContext().getBean("ISpartProfitingRelationDao");
        if (Objects.isNull(relationDao)) {
            return num;
        }
        if (CollectionUtils.isNotEmpty(oldList)) {
            ((ISpartProfitingRelationDao) relationDao).updateOldList(oldList);
        }

        if (CollectionUtils.isNotEmpty(moveStatusList)) {
            num = ((ISpartProfitingRelationDao) relationDao).updateMoveList(userId, curTime, "", moveStatusList);
        }
        if (CollectionUtils.isNotEmpty(coverStatuslList)) {
            // 返回数据条数，历史原因，获取了初始化表删除，在做写入；
            num = num + coverStatuslList.size();
            ((ISpartProfitingRelationDao) relationDao).uptCoverList(coverStatuslList);
            coverStatuslList.addAll(originList);
            ((ISpartProfitingRelationDao) relationDao).createList(coverStatuslList);
        }
        return num;
    }

    private void dataProcessing(Long userId, List<SpartProfitingRelationVO> dataList, List<SpartProfitingRelationVO> moveStatusList, List<SpartProfitingRelationVO> coverStatuslList, Map<String, List<SpartProfitingRelationVO>> submitRetMap, Timestamp curTime, AtomicReference<CopyOnWriteArrayList<SpartProfitingRelationVO>> atomicReference, List<SpartProfitingRelationVO> originList, List<SpartProfitingRelationVO> oldList) {
        dataList.stream().forEach(data -> {
            if (Objects.isNull(submitRetMap.get(data.getUnionKey())) || CollectionUtils.isEmpty(submitRetMap.get(data.getUnionKey()))) {
                return;
            }
            if (submitRetMap.get(data.getUnionKey()).size() == 1) {
                // 比较数据是否有变化
                if (compareToSubmitData(data, submitRetMap.get(data.getUnionKey()).get(0))) {
                    oldList.add(data);
                    return;
                }
                data.setOptType("move");
                data.setStatus(SaveTypeEnum.SUBMIT.getCode());
                data.setLastUpdatedBy(userId);
                data.setLastUpdateDate(curTime);
                moveStatusList.add(data);
                if (StringUtils.equals("Y", data.getUpdateFlag())) {
                    atomicReference.get().add(data);
                }
            } else if (submitRetMap.get(data.getUnionKey()).size() == 2) {
                List<SpartProfitingRelationVO> relationVOList = submitRetMap.get(data.getUnionKey());
                // 获取修改后提交的数据
                final Optional<SpartProfitingRelationVO> optional = relationVOList.stream().filter(item -> "Y".equalsIgnoreCase(item.getUpdateFlag())).findFirst();
                SpartProfitingRelationVO relation = null;
                if (optional.isPresent()) {
                    relation = optional.get();
                }
                // 比较数据是否有变化
                if (relation == null || compareToSubmitData(data, optional.get())) {
                    oldList.add(data);
                    return;
                }
                // 集合设置初始化数据
                final Optional<SpartProfitingRelationVO> first = relationVOList.stream().filter(item -> "N".equalsIgnoreCase(item.getUpdateFlag())).findFirst();
                if (first.isPresent()) {
                    originList.add(first.get());
                }
                relation.setL2Name(data.getL2Name());
                relation.setL3Name(data.getL3Name());
                relation.setL1Coefficient(data.getL1Coefficient());
                relation.setL2Coefficient(data.getL2Coefficient());
                relation.setL3Coefficient(data.getL3Coefficient());
                relation.setOptType("cover");
                relation.setLastUpdatedBy(userId);
                relation.setLastUpdateDate(curTime);
                coverStatuslList.add(relation);
                atomicReference.get().add(relation);
            } else {
                logger.error("submitData..count error: {}", submitRetMap.get(data.getUnionKey()).size());
            }
        });
    }

    private boolean compareToSubmitData(SpartProfitingRelationVO source, SpartProfitingRelationVO target) {
        if (!StringUtils.equals(source.getL2Name(), target.getL2Name())) {
            return Boolean.FALSE;
        }
        if (!StringUtils.equals(source.getL3Name(), target.getL3Name())) {
            return Boolean.FALSE;
        }
        if (!coefficientCompare(source.getL1Coefficient(), target.getL1Coefficient())) {
            return Boolean.FALSE;
        }
        if (!coefficientCompare(source.getL2Coefficient(), target.getL2Coefficient())) {
            return Boolean.FALSE;
        }
        if (!coefficientCompare(source.getL3Coefficient(), target.getL3Coefficient())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean coefficientCompare(BigDecimal val, BigDecimal target) {
        if (ObjectUtils.allNull(val, target)) {
            return Boolean.TRUE;
        }
        if (Objects.nonNull(val) && val.compareTo(BigDecimal.ZERO) == BigDecimal.ZERO.intValue() && Objects.isNull(target)) {
            return Boolean.TRUE;
        }
        if (Objects.nonNull(target) && target.compareTo(BigDecimal.ZERO) == BigDecimal.ZERO.intValue() && Objects.isNull(val)) {
            return Boolean.TRUE;
        }
        if (ObjectUtils.allNotNull(val, target)) {
            return val.setScale(6, BigDecimal.ROUND_DOWN).compareTo(target.setScale(6, BigDecimal.ROUND_DOWN)) == BigDecimal.ZERO.intValue();
        } else {
            return Boolean.FALSE;
        }
    }
}
